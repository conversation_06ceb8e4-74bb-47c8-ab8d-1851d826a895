#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import urllib3
import sys

# 禁用SSL警告（因为使用自签名证书）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_api(url, method='GET', data=None, description=''):
    """测试API接口"""
    try:
        if method == 'GET':
            response = requests.get(url, verify=False, timeout=10)
        elif method == 'POST':
            response = requests.post(url, json=data, verify=False, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            data_size = len(response.content)
            print(f"✅ {description}: 成功 ({data_size} bytes)")
            if data_size > 2048:
                print(f"   📊 大数据接口: {data_size} bytes (>2KB)")
            return True
        else:
            print(f"❌ {description}: 失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {description}: 连接失败 - {str(e)}")
        return False

def main():
    base_url = "https://localhost"
    
    print("🔒 HTTPS API 接口测试")
    print("=" * 50)
    print(f"服务器地址: {base_url}")
    print()
    
    # 测试所有API接口
    tests = [
        (f"{base_url}/api/test", "GET", None, "1. 基础测试API"),
        (f"{base_url}/api/complex", "POST", {"test": "complex_data"}, "2. 复杂数据API"),
        (f"{base_url}/api/users", "GET", None, "3. 用户管理API"),
        (f"{base_url}/api/data", "GET", None, "4. 大数据API (>2KB)"),
        (f"{base_url}/api/status", "GET", None, "5. 系统状态API"),
        (f"{base_url}/api/config", "POST", {"config": "test"}, "6. 配置管理API"),
        (f"{base_url}/api/log", "GET", None, "7. 日志API (>2KB)"),
        (f"{base_url}/api/metrics", "GET", None, "8. 监控指标API"),
        (f"{base_url}/api/search", "GET", None, "9. 搜索API"),
        (f"{base_url}/api/search?q=test", "POST", {"query": "test"}, "10. 搜索API (带参数)"),
    ]
    
    successful = 0
    total = len(tests)
    
    for url, method, data, description in tests:
        if test_api(url, method, data, description):
            successful += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {successful}/{total} 个接口测试成功")
    
    if successful == total:
        print("🎉 所有API接口都正常工作！")
    else:
        print(f"⚠️  有 {total - successful} 个接口需要检查")
    
    print()
    print("📝 注意：")
    print("   - /api/data 和 /api/log 接口返回 >2KB 数据")
    print("   - 所有接口支持 GET 和 POST 方法")
    print("   - 使用自签名证书，生产环境请使用正式证书")

if __name__ == "__main__":
    main() 