#!/bin/bash

echo "🔒 HTTPS 测试服务器启动脚本"
echo "=================================="

# 检查是否在正确的目录
if [ ! -f "server.crt" ] || [ ! -f "server.key" ]; then
    echo "❌ 未找到SSL证书文件！"
    echo "请确保在包含 server.crt 和 server.key 的目录中运行此脚本"
    exit 1
fi

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装！"
    exit 1
fi

# 检查端口是否已被占用
if ss -tlnp | grep -q ":443 "; then
    echo "⚠️  端口 443 已被占用"
    echo "正在尝试停止现有服务..."
    pkill -f "python3.*https_server.py"
    sleep 2
fi

# 获取本机IP
LOCAL_IP=$(ip route get 1 | awk '{print $7; exit}')

echo "🚀 启动 HTTPS 测试服务器..."
echo ""
echo "📍 访问地址："
echo "   https://localhost"
echo "   https://127.0.0.1"
echo "   https://${LOCAL_IP}"
echo ""
echo "🔒 证书信息："
echo "   - 类型: 自签名证书"
echo "   - 有效期: 365天"
echo "   - CN: localhost"
echo ""
echo "⚠️  浏览器安全提示："
echo "   由于使用自签名证书，浏览器会显示安全警告"
echo "   请点击 '高级' → '继续访问' 来访问测试服务"
echo ""
echo "🛑 停止服务: 按 Ctrl+C"
echo "=================================="
echo ""

# 启动服务器
python3 https_server.py 