#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import http.server
import socketserver
import ssl
import json
import urllib.parse
from datetime import datetime
import os

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        """处理 GET 请求"""
        # 记录请求信息
        self.log_request_info('GET')
        
        if self.path == '/':
            # 主页
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            
            html_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTPS 测试服务</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; text-align: center; }
        .info-box { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #2980b9; }
        .result { margin-top: 10px; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 HTTPS 测试服务</h1>
        
        <div class="info-box">
            <h3>✅ HTTPS 连接成功！</h3>
            <p>您已经成功通过 HTTPS 协议访问本服务器。</p>
            <p><strong>服务器信息：</strong></p>
            <ul>
                <li>协议：HTTPS (TLS/SSL)</li>
                <li>端口：443</li>
                <li>证书：自签名证书</li>
                <li>API接口：10个 (其中2个返回>2KB数据)</li>
                <li>访问时间：<span id="currentTime"></span></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 HTTPS 功能测试 (10个API接口)</h3>
            <p style="color: #666; margin-bottom: 15px;">
                <strong>可用接口：</strong>
                /api/test (基础测试) • /api/complex (复杂数据) • /api/users (用户管理) • 
                /api/data (大数据-2KB+) • /api/status (系统状态) • /api/config (配置管理) • 
                /api/log (日志-2KB+) • /api/metrics (监控指标) • /api/search (搜索)
            </p>
            
            <div>
                <button onclick="testGet()">GET 请求测试</button>
                <button onclick="testPost()">POST 请求测试</button>
                <button onclick="testJson()">JSON 数据测试</button>
                <button onclick="testUsers()">用户管理API</button>
                <button onclick="testLargeData()">大数据API</button>
                <br><br>
                <button onclick="testStatus()">系统状态API</button>
                <button onclick="testConfig()">配置管理API</button>
                <button onclick="testLogs()">日志API</button>
                <button onclick="testMetrics()">监控指标API</button>
                <button onclick="testSearch()">搜索API</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <div id="testResult" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 请求日志</h3>
            <div id="requestLog" class="log">
                <p>等待请求...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 证书信息</h3>
            <div class="log">
                <p><strong>颁发者：</strong> TestOrg</p>
                <p><strong>使用者：</strong> localhost</p>
                <p><strong>有效期：</strong> 365 天</p>
                <p><strong>算法：</strong> RSA</p>
                <p><strong>用途：</strong> eBPF HTTPS 流量分析测试</p>
            </div>
        </div>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');

        let logCounter = 1;

        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }

        function addLog(message) {
            const logDiv = document.getElementById('requestLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] #${logCounter++} ${message}`;
            
            if (logDiv.innerHTML.includes('等待请求...')) {
                logDiv.innerHTML = '';
            }
            
            logDiv.innerHTML += logEntry + '<br>';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testGet() {
            addLog('发起 GET 请求测试...');
            fetch('/api/test?type=get&data=test_data')
                .then(response => response.json())
                .then(data => {
                    showResult('GET 请求测试成功！服务器返回: ' + JSON.stringify(data), true);
                    addLog('GET 请求测试成功');
                })
                .catch(error => {
                    showResult('GET 请求测试失败: ' + error.message, false);
                    addLog('GET 请求测试失败');
                });
        }

        function testPost() {
            addLog('发起 POST 请求测试...');
            const testData = {
                test_type: 'POST',
                timestamp: new Date().toISOString(),
                data: 'This is test data for eBPF analysis'
            };

            fetch('/api/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('POST 请求测试成功！服务器返回: ' + JSON.stringify(data), true);
                addLog('POST 请求测试成功');
            })
            .catch(error => {
                showResult('POST 请求测试失败: ' + error.message, false);
                addLog('POST 请求测试失败');
            });
        }

        function testJson() {
            addLog('发起 JSON 数据测试...');
            const complexData = {
                user: {
                    id: 12345,
                    name: 'test_user',
                    permissions: ['read', 'write', 'admin']
                },
                session: {
                    token: 'abc123def456',
                    expires: new Date(Date.now() + 3600000).toISOString()
                },
                metadata: {
                    browser: navigator.userAgent,
                    timestamp: Date.now(),
                    test_purpose: 'eBPF HTTPS流量分析'
                }
            };

            fetch('/api/complex', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(complexData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('JSON 数据测试成功！复杂数据已成功发送和接收', true);
                addLog('JSON 复杂数据测试成功');
            })
            .catch(error => {
                showResult('JSON 数据测试失败: ' + error.message, false);
                addLog('JSON 数据测试失败');
            });
        }

        function testUsers() {
            addLog('发起用户管理API测试...');
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    showResult('用户管理API测试成功！获取到 ' + data.total_users + ' 个用户', true);
                    addLog('用户管理API测试成功');
                })
                .catch(error => {
                    showResult('用户管理API测试失败: ' + error.message, false);
                    addLog('用户管理API测试失败');
                });
        }

        function testLargeData() {
            addLog('发起大数据API测试...');
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    showResult('大数据API测试成功！获取到 ' + data.total_records + ' 条记录 (>2KB)', true);
                    addLog('大数据API测试成功');
                })
                .catch(error => {
                    showResult('大数据API测试失败: ' + error.message, false);
                    addLog('大数据API测试失败');
                });
        }

        function testStatus() {
            addLog('发起系统状态API测试...');
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    showResult('系统状态API测试成功！系统运行正常', true);
                    addLog('系统状态API测试成功');
                })
                .catch(error => {
                    showResult('系统状态API测试失败: ' + error.message, false);
                    addLog('系统状态API测试失败');
                });
        }

        function testConfig() {
            addLog('发起配置管理API测试...');
            const configData = {
                update_type: 'partial',
                config_section: 'server',
                new_values: {
                    debug_mode: true,
                    max_connections: 150
                }
            };

            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(configData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('配置管理API测试成功！配置已更新', true);
                addLog('配置管理API测试成功');
            })
            .catch(error => {
                showResult('配置管理API测试失败: ' + error.message, false);
                addLog('配置管理API测试失败');
            });
        }

        function testLogs() {
            addLog('发起日志API测试...');
            fetch('/api/log')
                .then(response => response.json())
                .then(data => {
                    showResult('日志API测试成功！获取到 ' + data.total_entries + ' 条日志记录 (>2KB)', true);
                    addLog('日志API测试成功');
                })
                .catch(error => {
                    showResult('日志API测试失败: ' + error.message, false);
                    addLog('日志API测试失败');
                });
        }

        function testMetrics() {
            addLog('发起监控指标API测试...');
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    showResult('监控指标API测试成功！健康评分: ' + data.health_score, true);
                    addLog('监控指标API测试成功');
                })
                .catch(error => {
                    showResult('监控指标API测试失败: ' + error.message, false);
                    addLog('监控指标API测试失败');
                });
        }

        function testSearch() {
            addLog('发起搜索API测试...');
            const searchData = {
                query: 'https',
                filters: {
                    type: ['document', 'api_doc'],
                    tags: ['https', 'ssl']
                },
                sort: 'relevance',
                limit: 10
            };

            fetch('/api/search?q=https', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(searchData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('搜索API测试成功！找到 ' + data.total_results + ' 个结果', true);
                addLog('搜索API测试成功');
            })
            .catch(error => {
                showResult('搜索API测试失败: ' + error.message, false);
                addLog('搜索API测试失败');
            });
        }

        function clearLog() {
            document.getElementById('requestLog').innerHTML = '<p>日志已清空...</p>';
            document.getElementById('testResult').style.display = 'none';
            logCounter = 1;
        }

        // 页面加载时自动记录
        window.onload = function() {
            addLog('页面加载完成，HTTPS连接建立成功');
        };
    </script>
</body>
</html>
            """
            
            self.wfile.write(html_content.encode('utf-8'))
            
        elif self.path.startswith('/api/test'):
            # API 测试端点
            self.handle_api_test()
            
        elif self.path.startswith('/api/complex'):
            # 复杂数据测试端点
            self.handle_complex_api()
            
        elif self.path.startswith('/api/users'):
            # 用户管理API
            self.handle_users_api()
            
        elif self.path.startswith('/api/data'):
            # 大数据量API (返回2KB+数据)
            self.handle_large_data_api()
            
        elif self.path.startswith('/api/status'):
            # 系统状态API
            self.handle_status_api()
            
        elif self.path.startswith('/api/config'):
            # 配置管理API
            self.handle_config_api()
            
        elif self.path.startswith('/api/log'):
            # 日志API (返回2KB+数据)
            self.handle_log_api()
            
        elif self.path.startswith('/api/metrics'):
            # 监控指标API
            self.handle_metrics_api()
            
        elif self.path.startswith('/api/search'):
            # 搜索API
            self.handle_search_api()
            
        else:
            # 其他请求返回404
            self.send_error(404, "页面未找到")

    def do_POST(self):
        """处理 POST 请求"""
        self.log_request_info('POST')
        
        if self.path.startswith('/api/test'):
            self.handle_api_test()
        elif self.path.startswith('/api/complex'):
            self.handle_complex_api()
        elif self.path.startswith('/api/users'):
            self.handle_users_api()
        elif self.path.startswith('/api/data'):
            self.handle_large_data_api()
        elif self.path.startswith('/api/status'):
            self.handle_status_api()
        elif self.path.startswith('/api/config'):
            self.handle_config_api()
        elif self.path.startswith('/api/log'):
            self.handle_log_api()
        elif self.path.startswith('/api/metrics'):
            self.handle_metrics_api()
        elif self.path.startswith('/api/search'):
            self.handle_search_api()
        else:
            self.send_error(404, "API 端点未找到")

    def handle_api_test(self):
        """处理简单API测试"""
        try:
            # 读取请求体
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 解析查询参数
            query_params = {}
            if '?' in self.path:
                query_string = self.path.split('?', 1)[1]
                query_params = urllib.parse.parse_qs(query_string)
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'timestamp': datetime.now().isoformat(),
                'query_params': query_params,
                'has_body': len(post_data) > 0,
                'body_size': len(post_data),
                'message': 'API 测试成功'
            }
            
            if post_data:
                try:
                    json_data = json.loads(post_data.decode('utf-8'))
                    response_data['received_data'] = json_data
                except:
                    response_data['raw_data'] = post_data.decode('utf-8', errors='ignore')
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_complex_api(self):
        """处理复杂数据API测试"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            
            if post_data:
                json_data = json.loads(post_data.decode('utf-8'))
                
                response_data = {
                    'status': 'success',
                    'message': '复杂数据处理成功',
                    'received_data_summary': {
                        'user_id': json_data.get('user', {}).get('id'),
                        'session_token': json_data.get('session', {}).get('token'),
                        'metadata_keys': list(json_data.get('metadata', {}).keys())
                    },
                    'processing_info': {
                        'timestamp': datetime.now().isoformat(),
                        'data_size': len(post_data),
                        'processing_time': '< 1ms'
                    }
                }
            else:
                response_data = {'status': 'error', 'message': '未收到数据'}
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_users_api(self):
        """处理用户管理API"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 模拟用户数据
            users_data = [
                {
                    "id": 1001,
                    "name": "张三",
                    "email": "<EMAIL>",
                    "role": "admin",
                    "created_at": "2024-01-15T08:30:00Z",
                    "last_login": "2024-01-20T14:22:33Z",
                    "permissions": ["read", "write", "delete", "admin"]
                },
                {
                    "id": 1002,
                    "name": "李四", 
                    "email": "<EMAIL>",
                    "role": "user",
                    "created_at": "2024-01-16T09:15:00Z",
                    "last_login": "2024-01-20T11:45:22Z",
                    "permissions": ["read", "write"]
                },
                {
                    "id": 1003,
                    "name": "王五",
                    "email": "<EMAIL>",
                    "role": "viewer",
                    "created_at": "2024-01-17T10:20:00Z",
                    "last_login": "2024-01-19T16:30:15Z",
                    "permissions": ["read"]
                }
            ]
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/users',
                'timestamp': datetime.now().isoformat(),
                'total_users': len(users_data),
                'users': users_data,
                'message': '用户数据获取成功'
            }
            
            if post_data:
                try:
                    json_data = json.loads(post_data.decode('utf-8'))
                    response_data['received_data'] = json_data
                    response_data['message'] = '用户数据更新成功'
                except:
                    response_data['raw_data'] = post_data.decode('utf-8', errors='ignore')
                    
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_large_data_api(self):
        """处理大数据量API (返回2KB+数据)"""
        try:
            # 生成大量数据，确保超过2KB
            large_dataset = []
            for i in range(50):  # 生成50条记录
                record = {
                    "record_id": f"REC_{i+1:04d}",
                    "timestamp": datetime.now().isoformat(),
                    "data_type": "sensor_reading",
                    "sensor_info": {
                        "sensor_id": f"SENSOR_{(i % 10) + 1:03d}",
                        "location": f"Building_{(i % 5) + 1}_Floor_{(i % 3) + 1}_Room_{(i % 8) + 1:02d}",
                        "coordinates": {
                            "latitude": 39.9042 + (i * 0.001),
                            "longitude": 116.4074 + (i * 0.001),
                            "altitude": 50 + (i * 2)
                        }
                    },
                    "readings": {
                        "temperature": 20.5 + (i % 15),
                        "humidity": 45.0 + (i % 30),
                        "pressure": 1013.25 + (i % 20),
                        "air_quality": {
                            "pm25": 15 + (i % 50),
                            "pm10": 25 + (i % 60),
                            "co2": 400 + (i % 200),
                            "no2": 10 + (i % 30)
                        }
                    },
                    "metadata": {
                        "device_status": "active" if i % 10 != 9 else "maintenance",
                        "battery_level": 100 - (i % 100),
                        "signal_strength": -30 - (i % 50),
                        "firmware_version": f"v{1 + (i % 3)}.{2 + (i % 5)}.{(i % 10)}",
                        "calibration_date": "2024-01-01T00:00:00Z",
                        "next_maintenance": "2024-02-01T00:00:00Z"
                    },
                    "analysis": {
                        "trend": "stable" if i % 3 == 0 else ("increasing" if i % 3 == 1 else "decreasing"),
                        "anomaly_score": round((i % 100) / 100.0, 3),
                        "confidence_level": round(0.8 + ((i % 20) / 100.0), 2),
                        "prediction": {
                            "next_hour": 20.5 + (i % 15) + 0.5,
                            "next_day": 20.5 + (i % 15) + 2.0,
                            "accuracy": round(0.85 + ((i % 15) / 100.0), 2)
                        }
                    }
                }
                large_dataset.append(record)
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/data',
                'timestamp': datetime.now().isoformat(),
                'total_records': len(large_dataset),
                'data_size_info': 'Large dataset (>2KB)',
                'dataset': large_dataset,
                'summary': {
                    'active_sensors': sum(1 for r in large_dataset if r['metadata']['device_status'] == 'active'),
                    'maintenance_sensors': sum(1 for r in large_dataset if r['metadata']['device_status'] == 'maintenance'),
                    'avg_temperature': round(sum(r['readings']['temperature'] for r in large_dataset) / len(large_dataset), 2),
                    'avg_humidity': round(sum(r['readings']['humidity'] for r in large_dataset) / len(large_dataset), 2)
                },
                'message': '大数据集获取成功'
            }
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_status_api(self):
        """处理系统状态API"""
        try:
            import psutil
            import platform
            
            # 获取系统信息
            system_info = {
                "system": platform.system(),
                "node": platform.node(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor()
            }
            
            # 获取CPU和内存信息
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/status',
                'timestamp': datetime.now().isoformat(),
                'system_info': system_info,
                'performance': {
                    'cpu_usage': f"{cpu_percent}%",
                    'memory': {
                        'total': f"{memory.total // (1024**3)} GB",
                        'available': f"{memory.available // (1024**3)} GB",
                        'used': f"{memory.used // (1024**3)} GB",
                        'percentage': f"{memory.percent}%"
                    },
                    'disk': {
                        'total': f"{disk.total // (1024**3)} GB",
                        'free': f"{disk.free // (1024**3)} GB",
                        'used': f"{disk.used // (1024**3)} GB",
                        'percentage': f"{(disk.used / disk.total * 100):.1f}%"
                    }
                },
                'server_status': {
                    'uptime': "运行中",
                    'connections': "正常",
                    'ssl_status': "已启用",
                    'port': 443
                },
                'message': '系统状态正常'
            }
            
            self.send_json_response(response_data)
            
        except ImportError:
            # 如果psutil不可用，返回基本信息
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/status',
                'timestamp': datetime.now().isoformat(),
                'system_info': {
                    "system": platform.system(),
                    "node": platform.node(),
                    "machine": platform.machine()
                },
                'server_status': {
                    'uptime': "运行中",
                    'connections': "正常",
                    'ssl_status': "已启用",
                    'port': 443
                },
                'note': 'psutil模块未安装，显示基本信息',
                'message': '系统状态正常'
            }
            self.send_json_response(response_data)
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_config_api(self):
        """处理配置管理API"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 模拟配置数据
            config_data = {
                "server_config": {
                    "host": "0.0.0.0",
                    "port": 443,
                    "ssl_enabled": True,
                    "max_connections": 100,
                    "timeout": 30,
                    "debug_mode": False
                },
                "security_config": {
                    "ssl_version": "TLSv1.2+",
                    "cipher_suites": ["ECDHE-RSA-AES256-GCM-SHA384", "ECDHE-RSA-AES128-GCM-SHA256"],
                    "cert_file": "server.crt",
                    "key_file": "server.key",
                    "require_client_cert": False
                },
                "logging_config": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "file_path": "/var/log/https_server.log",
                    "max_size": "10MB",
                    "backup_count": 5
                },
                "api_config": {
                    "rate_limit": "100/minute",
                    "cors_enabled": True,
                    "allowed_origins": ["*"],
                    "max_payload_size": "10MB"
                }
            }
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/config',
                'timestamp': datetime.now().isoformat(),
                'config': config_data,
                'message': '配置获取成功'
            }
            
            if post_data:
                try:
                    json_data = json.loads(post_data.decode('utf-8'))
                    response_data['received_config'] = json_data
                    response_data['message'] = '配置更新成功'
                except:
                    response_data['raw_data'] = post_data.decode('utf-8', errors='ignore')
                    
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_log_api(self):
        """处理日志API (返回2KB+数据)"""
        try:
            # 生成大量日志数据，确保超过2KB
            log_entries = []
            log_levels = ['INFO', 'DEBUG', 'WARNING', 'ERROR', 'CRITICAL']
            components = ['AuthService', 'DatabaseManager', 'APIHandler', 'SSLManager', 'RequestProcessor']
            actions = ['用户登录', '数据查询', 'SSL握手', '请求处理', '缓存更新', '配置加载', '健康检查', '数据备份']
            
            for i in range(80):  # 生成80条日志记录
                timestamp = datetime.now().replace(
                    hour=(datetime.now().hour - (i // 10)) % 24,
                    minute=(datetime.now().minute - (i % 60)) % 60,
                    second=(datetime.now().second - (i % 60)) % 60
                ).isoformat()
                
                level = log_levels[i % len(log_levels)]
                component = components[i % len(components)]
                action = actions[i % len(actions)]
                
                log_entry = {
                    "timestamp": timestamp,
                    "level": level,
                    "component": component,
                    "message": f"{action} - 请求ID: REQ_{i+1:05d}",
                    "details": {
                        "request_id": f"REQ_{i+1:05d}",
                        "client_ip": f"192.168.1.{(i % 254) + 1}",
                        "user_agent": "Mozilla/5.0 (compatible; HTTPS-Test-Client/1.0)",
                        "response_time": f"{10 + (i % 500)}ms",
                        "status_code": 200 if i % 10 != 9 else (400 if i % 20 == 9 else 500),
                        "bytes_sent": 1024 + (i * 100),
                        "bytes_received": 256 + (i * 50)
                    },
                    "context": {
                        "session_id": f"SES_{(i // 5) + 1:08d}",
                        "correlation_id": f"COR_{i+1:010d}",
                        "trace_id": f"TRC_{(i // 3) + 1:012d}",
                        "operation": action.replace(' ', '_').lower(),
                        "duration": f"{5 + (i % 100)}ms"
                    }
                }
                
                if level in ['ERROR', 'CRITICAL']:
                    log_entry["error_details"] = {
                        "error_code": f"E{1000 + (i % 100)}",
                        "error_message": f"操作失败: {action}",
                        "stack_trace": f"File '/app/main.py', line {100 + (i % 50)}, in handle_request"
                    }
                
                log_entries.append(log_entry)
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/log',
                'timestamp': datetime.now().isoformat(),
                'data_size_info': 'Large log dataset (>2KB)',
                'total_entries': len(log_entries),
                'log_entries': log_entries,
                'statistics': {
                    'info_count': sum(1 for entry in log_entries if entry['level'] == 'INFO'),
                    'debug_count': sum(1 for entry in log_entries if entry['level'] == 'DEBUG'),
                    'warning_count': sum(1 for entry in log_entries if entry['level'] == 'WARNING'),
                    'error_count': sum(1 for entry in log_entries if entry['level'] == 'ERROR'),
                    'critical_count': sum(1 for entry in log_entries if entry['level'] == 'CRITICAL')
                },
                'message': '日志数据获取成功'
            }
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_metrics_api(self):
        """处理监控指标API"""
        try:
            # 模拟监控指标数据
            current_time = datetime.now()
            metrics_data = {
                "server_metrics": {
                    "requests_per_second": 45.2,
                    "average_response_time": "120ms",
                    "total_requests": 15674,
                    "successful_requests": 15234,
                    "failed_requests": 440,
                    "active_connections": 12,
                    "uptime": "15d 8h 32m",
                    "last_restart": "2024-01-05T14:30:00Z"
                },
                "performance_metrics": {
                    "cpu_usage": "15.3%",
                    "memory_usage": "2.4GB",
                    "disk_io": {
                        "read_ops": 1250,
                        "write_ops": 890,
                        "read_bytes": "45MB",
                        "write_bytes": "23MB"
                    },
                    "network_io": {
                        "bytes_in": "156MB",
                        "bytes_out": "234MB",
                        "packets_in": 45234,
                        "packets_out": 52341
                    }
                },
                "api_metrics": {
                    "/api/test": {"calls": 3421, "avg_time": "85ms", "success_rate": "99.2%"},
                    "/api/complex": {"calls": 1234, "avg_time": "156ms", "success_rate": "98.8%"},
                    "/api/users": {"calls": 567, "avg_time": "95ms", "success_rate": "99.5%"},
                    "/api/data": {"calls": 890, "avg_time": "245ms", "success_rate": "99.1%"},
                    "/api/status": {"calls": 234, "avg_time": "312ms", "success_rate": "100%"},
                    "/api/config": {"calls": 123, "avg_time": "78ms", "success_rate": "99.9%"},
                    "/api/log": {"calls": 345, "avg_time": "198ms", "success_rate": "98.7%"}
                },
                "security_metrics": {
                    "ssl_handshakes": 2341,
                    "failed_authentications": 12,
                    "blocked_ips": 3,
                    "rate_limited_requests": 45,
                    "certificate_expiry": "2024-12-31T23:59:59Z"
                }
            }
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/metrics',
                'timestamp': current_time.isoformat(),
                'collection_time': current_time.isoformat(),
                'metrics': metrics_data,
                'health_score': 95.7,
                'alerts': [
                    {
                        "level": "warning",
                        "message": "磁盘使用率较高",
                        "value": "78%",
                        "threshold": "80%"
                    }
                ],
                'message': '监控指标获取成功'
            }
            
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def handle_search_api(self):
        """处理搜索API"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 解析查询参数
            query_params = {}
            if '?' in self.path:
                query_string = self.path.split('?', 1)[1]
                query_params = urllib.parse.parse_qs(query_string)
            
            search_query = query_params.get('q', [''])[0] if 'q' in query_params else ''
            
            # 模拟搜索结果
            search_results = [
                {
                    "id": "doc_001",
                    "title": "HTTPS 协议详解",
                    "content": "HTTPS（HyperText Transfer Protocol Secure）是HTTP的安全版本...",
                    "url": "/docs/https-protocol",
                    "score": 0.95,
                    "type": "document",
                    "tags": ["https", "ssl", "security", "protocol"]
                },
                {
                    "id": "api_002", 
                    "title": "API 接口文档",
                    "content": "本文档介绍了所有可用的API接口及其使用方法...",
                    "url": "/docs/api-reference",
                    "score": 0.87,
                    "type": "api_doc",
                    "tags": ["api", "documentation", "reference"]
                },
                {
                    "id": "tut_003",
                    "title": "SSL 证书配置教程",
                    "content": "如何为HTTPS服务器配置SSL证书，包括自签名证书的生成...",
                    "url": "/tutorials/ssl-certificate",
                    "score": 0.82,
                    "type": "tutorial",
                    "tags": ["ssl", "certificate", "tutorial", "configuration"]
                },
                {
                    "id": "faq_004",
                    "title": "常见问题解答",
                    "content": "关于HTTPS服务器使用过程中的常见问题及解决方案...",
                    "url": "/faq/https-server",
                    "score": 0.76,
                    "type": "faq",
                    "tags": ["faq", "troubleshooting", "help"]
                },
                {
                    "id": "log_005",
                    "title": "日志分析指南",
                    "content": "如何分析和理解HTTPS服务器的日志输出...",
                    "url": "/guides/log-analysis",
                    "score": 0.71,
                    "type": "guide",
                    "tags": ["logging", "analysis", "monitoring"]
                }
            ]
            
            # 如果有搜索词，过滤结果
            if search_query:
                filtered_results = [
                    result for result in search_results 
                    if search_query.lower() in result['title'].lower() 
                    or search_query.lower() in result['content'].lower()
                    or any(search_query.lower() in tag for tag in result['tags'])
                ]
            else:
                filtered_results = search_results
            
            response_data = {
                'status': 'success',
                'method': self.command,
                'endpoint': '/api/search',
                'timestamp': datetime.now().isoformat(),
                'query': search_query,
                'total_results': len(filtered_results),
                'results': filtered_results,
                'suggestions': ['https协议', 'ssl证书', 'api文档', '日志分析', '安全配置'],
                'search_time': '0.023s',
                'message': f'搜索完成，找到 {len(filtered_results)} 个结果'
            }
            
            if post_data:
                try:
                    json_data = json.loads(post_data.decode('utf-8'))
                    response_data['advanced_search'] = json_data
                    response_data['message'] = '高级搜索完成'
                except:
                    response_data['raw_query'] = post_data.decode('utf-8', errors='ignore')
                    
            self.send_json_response(response_data)
            
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)}, 500)

    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-length', str(len(json_data.encode('utf-8'))))
        self.end_headers()
        
        self.wfile.write(json_data.encode('utf-8'))

    def log_request_info(self, method):
        """记录请求信息"""
        client_ip = self.client_address[0]
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        user_agent = self.headers.get('User-Agent', 'Unknown')
        
        print(f"[{timestamp}] {method} {self.path} - {client_ip} - {user_agent}")

def create_https_server(port=443):
    """创建HTTPS服务器"""
    
    # 检查证书文件是否存在
    if not os.path.exists('server.crt') or not os.path.exists('server.key'):
        print("❌ 证书文件不存在！请先生成 SSL 证书。")
        return None
    
    try:
        # 创建HTTP服务器
        httpd = socketserver.TCPServer(("", port), MyHTTPRequestHandler)
        
        # 配置SSL
        context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        context.load_cert_chain(certfile="server.crt", keyfile="server.key")
        
        # 包装socket以支持SSL
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        
        print(f"🚀 HTTPS 服务器启动成功！")
        print(f"📍 服务器地址: https://localhost:{port}")
        print(f"🔒 使用自签名证书")
        print(f"⚠️  浏览器可能显示安全警告，点击'高级'→'继续访问'即可")
        print(f"🔄 按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        return httpd
        
    except Exception as e:
        print(f"❌ 创建HTTPS服务器失败: {e}")
        return None

if __name__ == "__main__":
    # 获取本机IP（用于显示访问地址）
    import socket
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    
    print("🔒 HTTPS 测试服务器")
    print("=" * 50)
    print(f"本机IP: {local_ip}")
    print()
    
    # 创建并启动服务器
    server = create_https_server(443)
    if server:
        try:
            server.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")
            server.shutdown()
            server.server_close()
    else:
        print("❌ 无法启动服务器") 