# HTTPS 测试服务使用说明

## 🎯 服务概述

这是一个专为 eBPF HTTPS 流量分析项目搭建的测试服务器，提供完整的 HTTPS 功能测试环境。

## 📋 服务特性

- ✅ **完整 HTTPS 支持**: 使用自签名 SSL 证书
- ✅ **丰富 API 接口**: 10个不同功能的 API 端点
- ✅ **大数据支持**: 2个接口返回 >2KB 数据
- ✅ **多种请求类型**: GET、POST、JSON API
- ✅ **实时日志记录**: 显示所有请求的详细信息
- ✅ **交互式测试页面**: 内置的网页测试界面
- ✅ **适合 eBPF 分析**: 产生丰富的加密流量数据

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
```bash
./start_https_server.sh
```

### 方法二：直接运行
```bash
python3 https_server.py
```

## 🌐 访问地址

服务器启动后，可通过以下地址访问：

- **本地访问**: https://localhost
- **局域网访问**: https://**************
- **回环地址**: https://127.0.0.1

## ⚠️ 证书安全提示

由于使用自签名证书，浏览器会显示安全警告：

1. 点击 **"高级"** 或 **"Advanced"**
2. 选择 **"继续访问"** 或 **"Proceed to localhost"**
3. 即可正常访问服务

## 🧪 功能测试

### 网页界面测试
1. 访问主页：https://localhost
2. 使用内置的测试按钮（共10个API接口）：
   - **GET 请求测试**: 测试简单的 GET 请求
   - **POST 请求测试**: 测试 JSON 数据提交
   - **JSON 数据测试**: 测试复杂数据结构
   - **用户管理API**: 测试用户数据管理
   - **大数据API**: 测试大数据集返回 (>2KB)
   - **系统状态API**: 获取服务器状态信息
   - **配置管理API**: 测试配置数据操作
   - **日志API**: 测试日志数据获取 (>2KB)
   - **监控指标API**: 获取性能监控数据
   - **搜索API**: 测试数据搜索功能
   - **清空日志**: 清除测试日志

### 命令行测试
```bash
# 测试主页面
curl -k https://localhost

# 测试基础 API
curl -k https://localhost/api/test

# 测试 POST 请求
curl -k -X POST -H "Content-Type: application/json" \
     -d '{"test":"data","message":"eBPF测试"}' \
     https://localhost/api/test

# 测试用户管理 API
curl -k https://localhost/api/users

# 测试大数据 API (>2KB)
curl -k https://localhost/api/data

# 测试系统状态 API
curl -k https://localhost/api/status

# 测试日志 API (>2KB)
curl -k https://localhost/api/log

# 测试搜索 API
curl -k "https://localhost/api/search?q=test"
```

### API 测试脚本
使用提供的测试脚本一次性测试所有API：
```bash
python3 test_apis.py
```

## 📊 API 端点 (总计10个)

| 端点 | 方法 | 描述 | 数据大小 |
|------|------|------|----------|
| `/` | GET | 主页面，显示测试界面 | 标准 |
| `/api/test` | GET/POST | 简单 API 测试端点 | 标准 |
| `/api/complex` | POST | 复杂数据处理端点 | 标准 |
| `/api/users` | GET/POST | 用户管理API | 标准 |
| `/api/data` | GET/POST | 大数据集API | **>2KB** |
| `/api/status` | GET/POST | 系统状态API | 标准 |
| `/api/config` | GET/POST | 配置管理API | 标准 |
| `/api/log` | GET/POST | 日志数据API | **>2KB** |
| `/api/metrics` | GET/POST | 监控指标API | 标准 |
| `/api/search` | GET/POST | 搜索功能API | 标准 |

## 🔍 服务器日志

服务器会在控制台显示详细的请求日志：
```
[2025-06-07 08:04:21] GET / - 127.0.0.1 - Mozilla/5.0...
[2025-06-07 08:04:22] POST /api/test - 127.0.0.1 - curl/7.61.1
```

## 📁 文件结构

```
/home/<USER>/https-test-server/
├── server.crt              # SSL 证书文件
├── server.key              # SSL 私钥文件  
├── https_server.py         # HTTPS 服务器主程序
├── start_https_server.sh   # 启动脚本
├── test_apis.py            # API 测试脚本
├── HTTPS服务说明.md        # 本说明文档
└── injected_keylog.txt     # SSL 密钥日志文件
```

## 🔧 服务管理

### 启动服务
```bash
./start_https_server.sh
```

### 停止服务
- 在运行服务的终端按 `Ctrl+C`
- 或者使用命令：`pkill -f "python3.*https_server.py"`

### 查看端口状态
```bash
ss -tlnp | grep 443
```

## 🎯 eBPF 测试用途

此服务器专为 eBPF HTTPS 流量分析设计，提供：

1. **丰富的 HTTPS 流量**: 支持多种请求类型
2. **可控的测试环境**: 可预测的请求和响应
3. **详细的日志记录**: 便于对比分析结果
4. **实时交互**: 支持手动和自动化测试

## 🛠️ 故障排除

### 端口被占用
```bash
# 查看占用端口的进程
ss -tlnp | grep 443

# 杀死占用进程
pkill -f "python3.*https_server.py"
```

### 证书问题
```bash
# 重新生成证书
openssl req -new -x509 -keyout server.key -out server.crt \
            -days 365 -nodes \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=TestOrg/OU=TestUnit/CN=localhost"
```

### Python 依赖问题
```bash
# 检查 Python3 版本
python3 --version

# 确保必要模块可用
python3 -c "import ssl, http.server, socketserver, json"
```

## 📝 更新日志

- **v1.1** (2025-06-07)
  - **NEW**: 扩展到10个API接口 (新增7个)
  - **NEW**: 2个大数据接口返回 >2KB 数据
  - **NEW**: API 测试脚本 (test_apis.py)
  - **NEW**: 丰富的模拟数据 (用户、日志、监控等)
  - **IMPROVED**: 更新的交互式测试界面
  - **IMPROVED**: 完善的文档说明

- **v1.0** (2025-06-07)
  - 初始版本
  - 基础 HTTPS 服务功能
  - 自签名证书支持
  - 交互式测试界面
  - 3个基础 API 端点

---

**注意**: 此服务器仅用于开发和测试目的，请勿在生产环境中使用自签名证书。 