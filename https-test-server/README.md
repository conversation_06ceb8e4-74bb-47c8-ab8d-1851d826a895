# HTTPS 测试服务器

这是一个用于eBPF HTTPS流量分析的测试服务器项目。

## 功能特性

- 🔒 完整的HTTPS服务器实现
- 📊 10个不同的API接口
- 🌐 支持GET和POST请求
- 📈 包含大数据接口(>2KB)
- 🛡️ SSL/TLS加密通信
- 📝 完整的请求日志记录
- 🔧 系统状态监控

## 文件说明

- `https_server.py` - 主要的HTTPS服务器实现
- `test_apis.py` - API接口测试脚本
- `start_https_server.sh` - 服务器启动脚本
- `server.crt` - SSL证书文件
- `server.key` - SSL私钥文件
- `HTTPS服务说明.md` - 详细的服务说明文档

## 快速开始

### 环境要求

- Python 3.6+
- 所需库：requests, urllib3, psutil

### 安装依赖

```bash
pip3 install requests urllib3 psutil
```

### 启动服务器

```bash
# 方法1：直接运行
python3 https_server.py

# 方法2：使用启动脚本
./start_https_server.sh
```

### 测试API

```bash
python3 test_apis.py
```

## API接口

1. `/api/test` - 基础测试API
2. `/api/complex` - 复杂数据API
3. `/api/users` - 用户管理API
4. `/api/data` - 大数据API (>2KB)
5. `/api/status` - 系统状态API
6. `/api/config` - 配置管理API
7. `/api/log` - 日志API (>2KB)
8. `/api/metrics` - 监控指标API
9. `/api/search` - 搜索API

## 访问地址

- `https://localhost:443`
- `https://127.0.0.1:443`
- `https://YOUR_SERVER_IP:443`

## 注意事项

- 使用自签名证书，浏览器会显示安全警告
- 需要开放443端口防火墙
- 生产环境请使用正式SSL证书

## 用途

本项目主要用于：
- eBPF HTTPS流量分析测试
- SSL/TLS协议研究
- 网络安全工具开发
- HTTPS协议学习 