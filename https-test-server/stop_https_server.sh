#!/bin/bash

echo "🛑 停止HTTPS测试服务器"
echo "=================================="

# 查找并杀死所有相关进程
echo "🔍 查找HTTPS服务器进程..."

# 查找python3 https_server.py进程
HTTPS_PIDS=$(ps aux | grep "python3.*https_server.py" | grep -v grep | awk '{print $2}')
if [ -n "$HTTPS_PIDS" ]; then
    echo "📍 找到HTTPS服务器进程: $HTTPS_PIDS"
    echo "🔪 正在停止进程..."
    kill $HTTPS_PIDS
    sleep 2
    
    # 检查是否还存在，强制杀死
    REMAINING_PIDS=$(ps aux | grep "python3.*https_server.py" | grep -v grep | awk '{print $2}')
    if [ -n "$REMAINING_PIDS" ]; then
        echo "⚡ 强制停止残留进程: $REMAINING_PIDS"
        kill -9 $REMAINING_PIDS
        sleep 1
    fi
else
    echo "ℹ️  没有找到运行中的HTTPS服务器进程"
fi

# 查找并杀死start_https_server.sh脚本进程
SCRIPT_PIDS=$(ps aux | grep "start_https_server.sh" | grep -v grep | awk '{print $2}')
if [ -n "$SCRIPT_PIDS" ]; then
    echo "📍 找到启动脚本进程: $SCRIPT_PIDS"
    echo "🔪 正在停止启动脚本..."
    kill $SCRIPT_PIDS
    sleep 1
    
    # 强制杀死如果还存在
    REMAINING_SCRIPT_PIDS=$(ps aux | grep "start_https_server.sh" | grep -v grep | awk '{print $2}')
    if [ -n "$REMAINING_SCRIPT_PIDS" ]; then
        echo "⚡ 强制停止启动脚本: $REMAINING_SCRIPT_PIDS"
        kill -9 $REMAINING_SCRIPT_PIDS
    fi
fi

# 检查端口占用情况
echo ""
echo "🔍 检查端口占用情况..."
PORT_443=$(netstat -tlnp 2>/dev/null | grep ":443 " | grep -v grep)
PORT_80=$(netstat -tlnp 2>/dev/null | grep ":80 " | grep -v grep)

if [ -n "$PORT_443" ]; then
    echo "⚠️  端口443仍被占用:"
    echo "$PORT_443"
    # 提取PID并杀死
    PID_443=$(echo "$PORT_443" | awk '{print $7}' | cut -d'/' -f1)
    if [ -n "$PID_443" ] && [ "$PID_443" != "-" ]; then
        echo "🔪 强制释放端口443，杀死进程 $PID_443"
        kill -9 $PID_443 2>/dev/null
    fi
fi

if [ -n "$PORT_80" ]; then
    echo "⚠️  端口80仍被占用:"
    echo "$PORT_80"
    # 提取PID并杀死
    PID_80=$(echo "$PORT_80" | awk '{print $7}' | cut -d'/' -f1)
    if [ -n "$PID_80" ] && [ "$PID_80" != "-" ]; then
        echo "🔪 强制释放端口80，杀死进程 $PID_80"
        kill -9 $PID_80 2>/dev/null
    fi
fi

# 等待端口释放
echo ""
echo "⏳ 等待端口释放..."
sleep 2

# 最终检查
echo ""
echo "🔍 最终状态检查:"
FINAL_HTTPS_PIDS=$(ps aux | grep "python3.*https_server.py" | grep -v grep | awk '{print $2}')
FINAL_PORT_443=$(netstat -tlnp 2>/dev/null | grep ":443 " | grep -v grep)

if [ -z "$FINAL_HTTPS_PIDS" ]; then
    echo "✅ HTTPS服务器进程已完全停止"
else
    echo "❌ 仍有残留进程: $FINAL_HTTPS_PIDS"
fi

if [ -z "$FINAL_PORT_443" ]; then
    echo "✅ 端口443已释放"
else
    echo "❌ 端口443仍被占用"
fi

echo ""
echo "🎉 HTTPS服务器停止操作完成"
echo "==================================" 