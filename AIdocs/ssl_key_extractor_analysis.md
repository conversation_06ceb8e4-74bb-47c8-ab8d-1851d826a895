# SSL Key Extractor 技术分析报告

## 项目概述

ssl_key_extractor 是一个基于 OpenSSL 函数拦截的 SSL/TLS 密钥提取工具，通过 LD_PRELOAD 机制注入到目标进程中，拦截 `SSL_CTX_new` 等关键函数，自动设置 keylog 回调来提取 TLS 握手密钥。

## 核心技术原理

### 1. 注入机制
- **LD_PRELOAD 技术**：通过动态链接器预加载机制，在程序启动时强制加载注入库
- **函数拦截**：使用 `dlsym(RTLD_NEXT, "SSL_CTX_new")` 拦截 OpenSSL 函数调用
- **回调注入**：在 SSL_CTX 创建时自动调用 `SSL_CTX_set_keylog_callback()` 设置密钥回调

### 2. 部署方式
- **全局注入**：通过 `/etc/ld.so.preload` 实现系统级预加载
- **配置驱动**：使用 `/etc/ssl-keylog.conf` 配置文件管理参数
- **透明监控**：无需修改应用启动参数或配置

## 问题分析

### 问题1：是否可以仅通过环境变量获取密钥，而不使用注入？

**答案：不可以，必须使用注入方式**

**技术原因：**

1. **OpenSSL 默认行为**
   - OpenSSL 库默认不会输出 TLS 密钥
   - 即使设置 `SSLKEYLOGFILE` 环境变量，也需要应用程序主动调用 `SSL_CTX_set_keylog_callback()`
   - 大多数生产应用（如 nginx、apache）并未实现这个功能

2. **环境变量局限性**
   ```bash
   # 这些环境变量对大多数应用无效：
   export SSLKEYLOGFILE=/tmp/keylog.txt
   export KEYLOG_FILE=/tmp/keylog.txt
   ```
   - nginx、apache 等服务器不会读取这些环境变量
   - 只有少数客户端工具（如某些版本的 curl）支持

3. **必须的代码修改**
   要让应用输出密钥，需要在源码中添加：
   ```c
   SSL_CTX_set_keylog_callback(ctx, keylog_callback_function);
   ```

**验证方案：**
```bash
# 测试1：仅环境变量（预期失败）
export SSLKEYLOGFILE=/tmp/test_keylog.txt
systemctl restart nginx
curl -k https://localhost/
# 检查：cat /tmp/test_keylog.txt  # 文件为空或不存在

# 测试2：使用注入（预期成功）
make global-enable
systemctl restart nginx  
curl -k https://localhost/
# 检查：cat /tmp/global_ssl_keylog.txt  # 包含密钥数据
```

### 问题2：已运行的nginx是否需要重启才能生效？是否有立即生效的方法？

**答案：必须重启，无法对已运行进程立即生效**

**技术原因：**

1. **LD_PRELOAD 时机限制**
   - LD_PRELOAD 只在进程启动时生效
   - 已运行的进程无法动态加载新的预加载库
   - 这是 Linux 动态链接器的基本限制

2. **内存空间隔离**
   - 每个进程有独立的内存空间
   - 无法从外部向运行中的进程注入代码
   - SSL_CTX 对象已经创建，无法追溯设置回调

3. **nginx 进程模型**
   - nginx master 进程在启动时确定预加载库
   - worker 进程继承 master 的内存映像
   - 配置重载（reload）不会重新加载预加载库

**当前无立即生效的方法，但可以考虑以下替代方案：**

#### 方案A：进程替换（推荐）
```bash
# 优雅重启，最小化服务中断
systemctl reload nginx  # 先尝试配置重载
systemctl restart nginx # 如果需要注入，必须重启
```

#### 方案B：滚动部署
```bash
# 对于多实例部署
# 1. 启用全局注入
make global-enable

# 2. 逐个重启实例
systemctl stop nginx@instance1
systemctl start nginx@instance1
# 验证实例1正常后，继续下一个实例
```

#### 方案C：新进程验证
```bash
# 启动新的测试进程验证注入
LD_PRELOAD=./libkeylog_injector.so openssl s_client -connect target:443
```

## 技术限制与适用性

### 支持的应用类型
✅ **完全支持**：
- nginx (使用 OpenSSL)
- apache httpd (使用 OpenSSL)  
- curl (使用 OpenSSL)
- 任何直接调用 OpenSSL 的 C/C++ 应用

❌ **不支持**：
- Java/Tomcat (使用 JSSE)
- Node.js (内置 TLS 实现)
- Go 应用 (crypto/tls 包)
- .NET Core (内置 SSL 实现)

### 部署环境兼容性
✅ **兼容**：
- systemd 服务
- Docker 容器
- 传统 init 系统

## 验证测试方案

### 测试1：环境变量方式验证
```bash
#!/bin/bash
echo "=== 测试环境变量方式 ==="

# 清理之前的测试
rm -f /tmp/env_test_keylog.txt

# 设置环境变量
export SSLKEYLOGFILE=/tmp/env_test_keylog.txt

# 重启nginx
systemctl restart nginx

# 执行HTTPS请求
curl -k https://localhost/ >/dev/null 2>&1

# 检查结果
if [[ -f /tmp/env_test_keylog.txt ]] && [[ -s /tmp/env_test_keylog.txt ]]; then
    echo "✅ 环境变量方式成功"
    wc -l /tmp/env_test_keylog.txt
else
    echo "❌ 环境变量方式失败（预期结果）"
fi
```

### 测试2：注入方式验证
```bash
#!/bin/bash
echo "=== 测试注入方式 ==="

# 启用全局注入
make global-enable

# 重启nginx
systemctl restart nginx

# 执行HTTPS请求
curl -k https://localhost/ >/dev/null 2>&1

# 检查结果
if [[ -f /tmp/global_ssl_keylog.txt ]] && [[ -s /tmp/global_ssl_keylog.txt ]]; then
    echo "✅ 注入方式成功"
    wc -l /tmp/global_ssl_keylog.txt
    echo "最新密钥记录："
    tail -3 /tmp/global_ssl_keylog.txt
else
    echo "❌ 注入方式失败"
fi
```

### 测试3：运行时注入验证
```bash
#!/bin/bash
echo "=== 测试运行时注入可行性 ==="

# 启动nginx
systemctl start nginx
nginx_pid=$(pgrep -f "nginx: master")

echo "nginx master PID: $nginx_pid"

# 检查当前加载的库
echo "当前加载的库："
pmap $nginx_pid | grep -E "(ssl|crypto|keylog)" || echo "未找到相关库"

# 尝试启用注入（对已运行进程应该无效）
make global-enable

# 不重启，直接测试
curl -k https://localhost/ >/dev/null 2>&1

# 检查密钥文件
if [[ -f /tmp/global_ssl_keylog.txt ]] && [[ -s /tmp/global_ssl_keylog.txt ]]; then
    echo "❌ 意外成功（可能之前已启用）"
else
    echo "✅ 运行时注入无效（符合预期）"
fi

# 重启后测试
systemctl restart nginx
curl -k https://localhost/ >/dev/null 2>&1

if [[ -f /tmp/global_ssl_keylog.txt ]] && [[ -s /tmp/global_ssl_keylog.txt ]]; then
    echo "✅ 重启后注入生效"
else
    echo "❌ 重启后注入仍然失败"
fi
```

## 结论与建议

### 核心结论
1. **必须使用注入方式**：仅设置环境变量无法让 nginx 等应用输出 SSL 密钥
2. **必须重启进程**：LD_PRELOAD 机制决定了无法对运行中的进程立即生效

### 生产部署建议
1. **规划维护窗口**：部署时需要重启目标服务
2. **滚动部署**：对于集群环境，逐个实例重启
3. **监控验证**：部署后验证密钥提取功能正常
4. **安全考虑**：密钥文件权限设置为 600，定期清理

### 替代方案
对于无法重启的场景，可考虑：
1. **反向代理**：在应用前部署支持密钥提取的代理
2. **网络层抓包**：使用 eBPF 或 tcpdump 等工具
3. **应用层修改**：修改应用源码支持密钥输出

## 深入技术分析

### LD_PRELOAD 机制详解

#### 工作原理
```c
// 动态链接器在进程启动时的加载顺序：
// 1. 读取 /etc/ld.so.preload
// 2. 读取 LD_PRELOAD 环境变量
// 3. 加载预加载库到进程内存空间
// 4. 解析符号，预加载库的符号优先级高于原始库
```

#### 时机限制
- **进程创建时**：execve() 系统调用时动态链接器处理预加载
- **运行时无效**：已运行进程的符号表已固定，无法动态修改
- **继承机制**：子进程继承父进程的预加载设置

### OpenSSL 密钥提取机制

#### 标准流程
```c
// 应用需要主动调用以下代码才能输出密钥：
SSL_CTX *ctx = SSL_CTX_new(method);
SSL_CTX_set_keylog_callback(ctx, keylog_callback);

// keylog_callback 函数示例：
void keylog_callback(const SSL *ssl, const char *line) {
    FILE *f = fopen(getenv("SSLKEYLOGFILE"), "a");
    if (f) {
        fprintf(f, "%s\n", line);
        fclose(f);
    }
}
```

#### 注入器的拦截点
```c
// 注入器拦截 SSL_CTX_new 函数：
SSL_CTX* SSL_CTX_new(const SSL_METHOD *method) {
    // 1. 调用原始函数创建 SSL_CTX
    SSL_CTX *ctx = original_SSL_CTX_new(method);

    // 2. 自动注入 keylog 回调
    SSL_CTX_set_keylog_callback(ctx, our_keylog_callback);

    return ctx;
}
```

### 进程内存空间分析

#### 为什么运行时注入不可行
```bash
# 查看进程内存映射
cat /proc/[nginx_pid]/maps | grep -E "(ssl|crypto)"

# 典型输出：
# 7f8b2c000000-7f8b2c200000 r-xp 00000000 08:01 libssl.so.1.1
# 7f8b2c200000-7f8b2c400000 r--p 00200000 08:01 libssl.so.1.1
# 7f8b2c400000-7f8b2c500000 rw-p 00400000 08:01 libssl.so.1.1
```

- **代码段 (r-xp)**：只读可执行，包含函数代码
- **数据段 (r--p)**：只读数据，包含符号表
- **BSS段 (rw-p)**：可读写，包含全局变量

运行时无法修改代码段和符号表，因此无法注入新的函数拦截。

## 实际验证脚本

### 完整验证脚本
```bash
#!/bin/bash
# ssl_key_extractor_verification.sh

set -e

LOG_FILE="/tmp/ssl_verification.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

log() {
    echo "[$TIMESTAMP] $1" | tee -a "$LOG_FILE"
}

# 清理函数
cleanup() {
    log "清理测试环境..."
    rm -f /tmp/env_test_keylog.txt
    rm -f /tmp/runtime_test_keylog.txt
    sudo ./setup_global_injection.sh disable >/dev/null 2>&1 || true
}

# 测试1：环境变量方式
test_environment_variable() {
    log "=== 测试1：环境变量方式 ==="

    cleanup

    # 设置环境变量
    export SSLKEYLOGFILE=/tmp/env_test_keylog.txt

    # 重启nginx（带环境变量）
    sudo systemctl stop nginx
    sudo SSLKEYLOGFILE=/tmp/env_test_keylog.txt systemctl start nginx

    sleep 2

    # 执行HTTPS请求
    curl -k https://localhost/ >/dev/null 2>&1 || true

    # 检查结果
    if [[ -f /tmp/env_test_keylog.txt ]] && [[ -s /tmp/env_test_keylog.txt ]]; then
        log "✅ 环境变量方式成功（意外）"
        log "密钥数量: $(wc -l < /tmp/env_test_keylog.txt)"
    else
        log "❌ 环境变量方式失败（符合预期）"
    fi
}

# 测试2：运行时注入
test_runtime_injection() {
    log "=== 测试2：运行时注入测试 ==="

    cleanup

    # 启动nginx（无注入）
    sudo systemctl start nginx
    nginx_pid=$(pgrep -f "nginx: master" | head -1)
    log "nginx master PID: $nginx_pid"

    # 检查当前内存映射
    log "当前加载的SSL相关库："
    pmap "$nginx_pid" | grep -E "(ssl|crypto)" | head -5 | while read line; do
        log "  $line"
    done

    # 启用全局注入（对运行中进程应该无效）
    sudo make global-enable >/dev/null 2>&1

    # 不重启，直接测试
    curl -k https://localhost/ >/dev/null 2>&1 || true

    # 检查密钥文件
    if [[ -f /tmp/global_ssl_keylog.txt ]] && [[ -s /tmp/global_ssl_keylog.txt ]]; then
        log "❌ 运行时注入意外成功"
        log "密钥数量: $(wc -l < /tmp/global_ssl_keylog.txt)"
    else
        log "✅ 运行时注入无效（符合预期）"
    fi

    # 重启后测试
    log "重启nginx后测试..."
    sudo systemctl restart nginx
    sleep 2

    curl -k https://localhost/ >/dev/null 2>&1 || true

    if [[ -f /tmp/global_ssl_keylog.txt ]] && [[ -s /tmp/global_ssl_keylog.txt ]]; then
        log "✅ 重启后注入生效"
        log "密钥数量: $(wc -l < /tmp/global_ssl_keylog.txt)"
        log "最新密钥记录："
        tail -3 /tmp/global_ssl_keylog.txt | while read line; do
            log "  ${line:0:80}..."
        done
    else
        log "❌ 重启后注入失败"
    fi
}

# 测试3：进程内存分析
test_process_memory() {
    log "=== 测试3：进程内存分析 ==="

    nginx_pid=$(pgrep -f "nginx: master" | head -1)
    if [[ -z "$nginx_pid" ]]; then
        log "nginx未运行，启动nginx..."
        sudo systemctl start nginx
        nginx_pid=$(pgrep -f "nginx: master" | head -1)
    fi

    log "分析nginx进程内存映射 (PID: $nginx_pid):"

    # SSL库映射
    log "SSL/Crypto库映射："
    pmap "$nginx_pid" | grep -E "(ssl|crypto)" | while read line; do
        log "  $line"
    done

    # 检查是否加载了注入器
    log "注入器库映射："
    if pmap "$nginx_pid" | grep -q "keylog"; then
        pmap "$nginx_pid" | grep "keylog" | while read line; do
            log "  ✅ $line"
        done
    else
        log "  ❌ 未发现注入器库"
    fi

    # 符号表分析
    log "检查SSL_CTX_new符号："
    if command -v nm >/dev/null 2>&1; then
        lib_path=$(pmap "$nginx_pid" | grep libssl | head -1 | awk '{print $6}')
        if [[ -n "$lib_path" ]]; then
            nm -D "$lib_path" 2>/dev/null | grep SSL_CTX_new | head -3 | while read line; do
                log "  $line"
            done
        fi
    fi
}

# 主函数
main() {
    log "开始SSL Key Extractor验证测试"
    log "测试环境: $(uname -a)"

    # 检查依赖
    if ! command -v nginx >/dev/null 2>&1; then
        log "错误: nginx未安装"
        exit 1
    fi

    if [[ ! -f "./libkeylog_injector.so" ]]; then
        log "编译注入器库..."
        make >/dev/null 2>&1
    fi

    # 执行测试
    test_environment_variable
    test_runtime_injection
    test_process_memory

    log "验证测试完成，详细日志: $LOG_FILE"
}

# 执行主函数
main "$@"
```

## 总结

通过深入分析 ssl_key_extractor 项目，我们得出以下关键结论：

### 问题1答案：必须使用注入
- 环境变量方式对nginx等服务器无效
- 需要应用主动调用 `SSL_CTX_set_keylog_callback()`
- 注入器通过拦截 `SSL_CTX_new` 自动设置回调

### 问题2答案：必须重启进程
- LD_PRELOAD 只在进程启动时生效
- 运行时无法修改进程的符号表和内存映射
- 这是Linux动态链接器的基本限制

### 最佳实践
1. 规划维护窗口进行部署
2. 使用滚动重启减少服务中断
3. 部署前在测试环境充分验证
4. 考虑反向代理等替代方案
