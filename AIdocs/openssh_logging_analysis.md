# OpenSSH 用户操作日志记录分析与实施方案

## 概述

OpenSSH 本身提供了多层次的日志记录功能，无需注入即可实现用户操作审计。通过合理配置 SSH 服务器和系统组件，可以全面记录用户的 SSH 会话和操作行为。

## SSH 日志记录层次

### 1. SSH 连接层日志
- **功能**：记录连接建立、认证、断开等事件
- **配置文件**：`/etc/ssh/sshd_config`
- **日志位置**：`/var/log/auth.log` 或 `/var/log/secure`

### 2. 会话层日志
- **功能**：记录用户会话的完整操作过程
- **实现方式**：ForceCommand + script 命令
- **日志位置**：自定义目录

### 3. 命令层日志
- **功能**：记录用户执行的具体命令
- **实现方式**：bash 历史记录 + auditd
- **日志位置**：`~/.bash_history` + `/var/log/audit/audit.log`

## 详细配置方案

### 方案1：基础SSH连接日志

#### 1.1 配置 sshd_config
```bash
# 编辑SSH服务器配置
sudo vim /etc/ssh/sshd_config

# 添加或修改以下配置：
# 日志级别设置
LogLevel VERBOSE

# 系统日志设施
SyslogFacility AUTHPRIV

# 启用详细的认证日志
UsePAM yes

# 记录登录用户信息
PrintLastLog yes

# 启用TCP Keepalive
TCPKeepAlive yes

# 客户端活跃检测
ClientAliveInterval 60
ClientAliveCountMax 3
```

#### 1.2 重启SSH服务
```bash
sudo systemctl restart sshd
```

#### 1.3 查看连接日志
```bash
# CentOS/RHEL 系统
sudo tail -f /var/log/secure

# Ubuntu/Debian 系统  
sudo tail -f /var/log/auth.log

# 过滤SSH相关日志
sudo grep "sshd" /var/log/auth.log
```

### 方案2：完整会话记录（推荐）

#### 2.1 创建会话记录脚本
```bash
sudo mkdir -p /var/log/ssh-sessions
sudo chmod 755 /var/log/ssh-sessions

# 创建会话记录脚本
sudo tee /usr/local/bin/ssh-session-logger.sh << 'EOF'
#!/bin/bash

# SSH会话记录脚本
# 记录用户的完整SSH会话

# 配置变量
LOG_DIR="/var/log/ssh-sessions"
SESSION_ID="${USER}_$(date +%Y%m%d_%H%M%S)_$$"
LOG_FILE="${LOG_DIR}/${SESSION_ID}.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 记录会话开始信息
{
    echo "=== SSH会话开始 ==="
    echo "时间: $(date)"
    echo "用户: $USER"
    echo "来源IP: $SSH_CLIENT"
    echo "TTY: $SSH_TTY"
    echo "会话ID: $SESSION_ID"
    echo "========================"
    echo ""
} >> "$LOG_FILE"

# 设置文件权限
chmod 600 "$LOG_FILE"

# 启动script命令记录会话
exec script -f -q "$LOG_FILE"
EOF

sudo chmod +x /usr/local/bin/ssh-session-logger.sh
```

#### 2.2 配置强制执行会话记录
```bash
# 方法1：全局强制记录（所有用户）
sudo vim /etc/ssh/sshd_config

# 添加以下配置：
ForceCommand /usr/local/bin/ssh-session-logger.sh

# 方法2：特定用户组记录
# 创建需要审计的用户组
sudo groupadd ssh-audit

# 为特定组配置强制命令
echo "Match Group ssh-audit" >> /etc/ssh/sshd_config
echo "    ForceCommand /usr/local/bin/ssh-session-logger.sh" >> /etc/ssh/sshd_config

# 将用户添加到审计组
sudo usermod -a -G ssh-audit username
```

#### 2.3 重启SSH服务
```bash
sudo systemctl restart sshd
```

### 方案3：命令级详细审计

#### 3.1 配置 auditd 系统审计
```bash
# 安装auditd
sudo yum install audit -y  # CentOS/RHEL
sudo apt install auditd -y # Ubuntu/Debian

# 启动auditd服务
sudo systemctl enable auditd
sudo systemctl start auditd

# 配置审计规则
sudo tee -a /etc/audit/rules.d/ssh-audit.rules << 'EOF'
# SSH相关审计规则

# 监控SSH配置文件修改
-w /etc/ssh/sshd_config -p wa -k ssh_config_change

# 监控用户登录
-w /var/log/wtmp -p wa -k user_login
-w /var/log/btmp -p wa -k user_login_failed

# 监控命令执行
-a always,exit -F arch=b64 -S execve -k command_exec
-a always,exit -F arch=b32 -S execve -k command_exec

# 监控文件访问
-a always,exit -F arch=b64 -S open,openat,creat -F success=1 -k file_access
-a always,exit -F arch=b32 -S open,openat,creat -F success=1 -k file_access

# 监控网络连接
-a always,exit -F arch=b64 -S connect,accept -k network_connect
-a always,exit -F arch=b32 -S connect,accept -k network_connect
EOF

# 重新加载审计规则
sudo augenrules --load
sudo systemctl restart auditd
```

#### 3.2 增强bash历史记录
```bash
# 全局bash历史配置
sudo tee -a /etc/profile << 'EOF'

# SSH会话历史记录增强配置
if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
    # 设置历史记录格式（包含时间戳）
    export HISTTIMEFORMAT="%Y-%m-%d %H:%M:%S "
    
    # 增加历史记录条数
    export HISTSIZE=10000
    export HISTFILESIZE=20000
    
    # 实时保存历史记录
    export PROMPT_COMMAND="history -a"
    
    # 记录所有命令（包括重复的）
    unset HISTCONTROL
    
    # 自定义历史文件位置
    export HISTFILE="/var/log/ssh-sessions/.bash_history_${USER}_$(date +%Y%m%d)"
    
    # 确保历史文件目录存在
    mkdir -p /var/log/ssh-sessions
    touch "$HISTFILE"
    chmod 600 "$HISTFILE"
fi
EOF
```

### 方案4：实时监控脚本

#### 4.1 创建实时监控脚本
```bash
sudo tee /usr/local/bin/ssh-monitor.sh << 'EOF'
#!/bin/bash

# SSH实时监控脚本
# 监控SSH连接和用户活动

LOG_FILE="/var/log/ssh-monitor.log"

log_event() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# 监控SSH连接
monitor_ssh_connections() {
    while true; do
        # 获取当前SSH连接
        current_sessions=$(who | grep pts)
        
        if [ -n "$current_sessions" ]; then
            echo "$current_sessions" | while read line; do
                user=$(echo "$line" | awk '{print $1}')
                tty=$(echo "$line" | awk '{print $2}')
                time=$(echo "$line" | awk '{print $3" "$4}')
                ip=$(echo "$line" | awk '{print $5}' | tr -d '()')
                
                log_event "ACTIVE_SESSION: User=$user TTY=$tty Time=$time IP=$ip"
            done
        fi
        
        sleep 30
    done
}

# 监控进程活动
monitor_process_activity() {
    while true; do
        # 监控SSH相关进程
        ssh_processes=$(ps aux | grep -E "(sshd:|ssh)" | grep -v grep)
        
        if [ -n "$ssh_processes" ]; then
            echo "$ssh_processes" | while read line; do
                log_event "SSH_PROCESS: $line"
            done
        fi
        
        sleep 60
    done
}

# 启动监控
log_event "SSH监控启动"
monitor_ssh_connections &
monitor_process_activity &

wait
EOF

sudo chmod +x /usr/local/bin/ssh-monitor.sh
```

#### 4.2 创建systemd服务
```bash
sudo tee /etc/systemd/system/ssh-monitor.service << 'EOF'
[Unit]
Description=SSH Connection Monitor
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/bin/ssh-monitor.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable ssh-monitor.service
sudo systemctl start ssh-monitor.service
```

## 日志分析与查询

### 1. SSH连接日志分析
```bash
# 查看SSH登录成功记录
sudo grep "Accepted" /var/log/auth.log

# 查看SSH登录失败记录
sudo grep "Failed" /var/log/auth.log

# 按用户统计登录次数
sudo grep "Accepted" /var/log/auth.log | awk '{print $9}' | sort | uniq -c

# 按IP统计登录次数
sudo grep "Accepted" /var/log/auth.log | awk '{print $11}' | sort | uniq -c
```

### 2. 会话记录分析
```bash
# 查看所有会话记录
ls -la /var/log/ssh-sessions/

# 查看特定用户的会话
ls -la /var/log/ssh-sessions/ | grep username

# 实时查看会话内容
sudo tail -f /var/log/ssh-sessions/username_20241215_143022_12345.log

# 搜索特定命令
sudo grep -r "sudo\|rm\|chmod" /var/log/ssh-sessions/
```

### 3. 审计日志分析
```bash
# 查看命令执行记录
sudo ausearch -k command_exec

# 查看文件访问记录
sudo ausearch -k file_access

# 查看网络连接记录
sudo ausearch -k network_connect

# 按用户查看审计记录
sudo ausearch -ua username

# 按时间范围查看审计记录
sudo ausearch -ts today -k command_exec
```

## 安全考虑

### 1. 日志文件保护
```bash
# 设置日志目录权限
sudo chmod 750 /var/log/ssh-sessions
sudo chown root:adm /var/log/ssh-sessions

# 防止用户删除日志
sudo chattr +a /var/log/ssh-sessions/*.log
```

### 2. 日志轮转配置
```bash
sudo tee /etc/logrotate.d/ssh-sessions << 'EOF'
/var/log/ssh-sessions/*.log {
    daily
    missingok
    rotate 90
    compress
    delaycompress
    notifempty
    copytruncate
    create 600 root root
}
EOF
```

### 3. 远程日志传输
```bash
# 配置rsyslog远程传输
sudo tee -a /etc/rsyslog.conf << 'EOF'
# SSH日志远程传输
$ModLoad imfile
$InputFileName /var/log/ssh-sessions/*.log
$InputFileTag ssh-session:
$InputFileStateFile ssh-session-state
$InputFileSeverity info
$InputFileFacility local0
$InputRunFileMonitor

# 发送到远程日志服务器
local0.* @@log-server.example.com:514
EOF

sudo systemctl restart rsyslog
```

## 验证测试

### 1. 基础功能测试
```bash
# 测试SSH连接日志
ssh user@localhost
sudo grep "$(date '+%b %d')" /var/log/auth.log | tail -5

# 测试会话记录
# 登录后执行一些命令，然后检查日志文件
sudo ls -la /var/log/ssh-sessions/
sudo tail /var/log/ssh-sessions/$(ls -t /var/log/ssh-sessions/ | head -1)
```

### 2. 审计功能测试
```bash
# 执行一些命令
ls /etc
sudo cat /etc/passwd
touch /tmp/test.txt

# 检查审计记录
sudo ausearch -k command_exec | tail -10
sudo ausearch -k file_access | tail -10
```

## 总结

OpenSSH 通过以下机制实现完整的用户操作审计：

1. **内置日志功能**：通过 LogLevel 和 SyslogFacility 配置连接层日志
2. **ForceCommand 机制**：强制执行会话记录脚本
3. **系统审计集成**：结合 auditd 实现命令级审计
4. **bash 历史增强**：详细记录用户命令历史

这些方案无需注入，完全基于 OpenSSH 和 Linux 系统的原生功能，提供了全面的用户操作审计能力。

## 高级配置方案

### 方案5：基于 tlog 的专业会话记录

#### 5.1 安装 tlog
```bash
# CentOS/RHEL 8+
sudo dnf install tlog -y

# Ubuntu 20.04+
sudo apt install tlog -y

# 从源码编译（如果包不可用）
git clone https://github.com/Scribery/tlog.git
cd tlog
autoreconf -i -f
./configure --prefix=/usr --sysconfdir=/etc
make && sudo make install
```

#### 5.2 配置 tlog
```bash
# 创建tlog配置文件
sudo tee /etc/tlog/tlog-rec-session.conf << 'EOF'
{
    "version": 1,
    "output": {
        "type": "file",
        "file": {
            "path": "/var/log/tlog/tlog-rec-session.log"
        }
    },
    "log": {
        "level": "info"
    },
    "session": {
        "recording": {
            "enabled": true,
            "shell": "/bin/bash"
        }
    }
}
EOF

# 创建日志目录
sudo mkdir -p /var/log/tlog
sudo chmod 755 /var/log/tlog

# 配置SSH使用tlog
sudo vim /etc/ssh/sshd_config
# 添加：
# ForceCommand /usr/bin/tlog-rec-session
```

### 方案6：集成 ELK 日志分析栈

#### 6.1 配置 Filebeat 收集SSH日志
```bash
# 安装Filebeat
curl -L -O https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-8.11.0-linux-x86_64.tar.gz
tar xzvf filebeat-8.11.0-linux-x86_64.tar.gz

# 配置Filebeat
sudo tee /etc/filebeat/filebeat.yml << 'EOF'
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/auth.log
    - /var/log/secure
    - /var/log/ssh-sessions/*.log
  fields:
    log_type: ssh_audit
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/audit/audit.log
  fields:
    log_type: system_audit
  fields_under_root: true

output.elasticsearch:
  hosts: ["localhost:9200"]
  index: "ssh-audit-%{+yyyy.MM.dd}"

processors:
- add_host_metadata:
    when.not.contains.tags: forwarded

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
EOF
```

#### 6.2 创建 Logstash 解析配置
```bash
sudo tee /etc/logstash/conf.d/ssh-audit.conf << 'EOF'
input {
  beats {
    port => 5044
  }
}

filter {
  if [log_type] == "ssh_audit" {
    if [message] =~ /Accepted/ {
      grok {
        match => {
          "message" => "%{SYSLOGTIMESTAMP:timestamp} %{IPORHOST:server} sshd\[%{POSINT:pid}\]: Accepted %{WORD:auth_method} for %{USERNAME:username} from %{IPORHOST:client_ip} port %{POSINT:client_port} ssh2"
        }
      }
      mutate {
        add_field => { "event_type" => "ssh_login_success" }
      }
    }

    if [message] =~ /Failed/ {
      grok {
        match => {
          "message" => "%{SYSLOGTIMESTAMP:timestamp} %{IPORHOST:server} sshd\[%{POSINT:pid}\]: Failed %{WORD:auth_method} for %{USERNAME:username} from %{IPORHOST:client_ip} port %{POSINT:client_port} ssh2"
        }
      }
      mutate {
        add_field => { "event_type" => "ssh_login_failed" }
      }
    }
  }

  if [log_type] == "system_audit" {
    if [message] =~ /type=EXECVE/ {
      grok {
        match => {
          "message" => "type=EXECVE.*argc=%{POSINT:argc}.*a0=\"%{DATA:command}\""
        }
      }
      mutate {
        add_field => { "event_type" => "command_execution" }
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "ssh-audit-%{+YYYY.MM.dd}"
  }
}
EOF
```

### 方案7：实时告警系统

#### 7.1 创建实时告警脚本
```bash
sudo tee /usr/local/bin/ssh-alert.sh << 'EOF'
#!/bin/bash

# SSH实时告警系统
# 监控可疑SSH活动并发送告警

ALERT_LOG="/var/log/ssh-alerts.log"
WEBHOOK_URL="https://hooks.slack.com/services/YOUR/WEBHOOK/URL"

# 告警函数
send_alert() {
    local message="$1"
    local severity="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 记录到日志
    echo "[$timestamp] [$severity] $message" >> "$ALERT_LOG"

    # 发送到Slack（可选）
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 SSH Alert [$severity]: $message\"}" \
            "$WEBHOOK_URL" 2>/dev/null
    fi

    # 发送邮件（可选）
    echo "$message" | mail -s "SSH Alert [$severity]" <EMAIL> 2>/dev/null
}

# 监控SSH暴力破解
monitor_brute_force() {
    tail -f /var/log/auth.log | while read line; do
        if echo "$line" | grep -q "Failed password"; then
            ip=$(echo "$line" | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}')
            user=$(echo "$line" | grep -oE 'for [a-zA-Z0-9_-]+' | cut -d' ' -f2)

            # 检查最近5分钟内的失败次数
            recent_failures=$(grep "Failed password.*$ip" /var/log/auth.log | \
                grep "$(date '+%b %d %H:')" | wc -l)

            if [ "$recent_failures" -gt 5 ]; then
                send_alert "Brute force attack detected from $ip (user: $user, failures: $recent_failures)" "HIGH"
            fi
        fi
    done
}

# 监控异常登录
monitor_unusual_login() {
    tail -f /var/log/auth.log | while read line; do
        if echo "$line" | grep -q "Accepted"; then
            ip=$(echo "$line" | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}')
            user=$(echo "$line" | grep -oE 'for [a-zA-Z0-9_-]+' | cut -d' ' -f2)
            hour=$(date '+%H')

            # 检查是否为非工作时间登录
            if [ "$hour" -lt 8 ] || [ "$hour" -gt 18 ]; then
                send_alert "Off-hours login detected: $user from $ip at $(date)" "MEDIUM"
            fi

            # 检查是否为新IP
            if ! grep -q "$ip" /var/log/known_ips.txt 2>/dev/null; then
                send_alert "Login from new IP: $user from $ip" "MEDIUM"
                echo "$ip" >> /var/log/known_ips.txt
            fi
        fi
    done
}

# 启动监控
monitor_brute_force &
monitor_unusual_login &

wait
EOF

sudo chmod +x /usr/local/bin/ssh-alert.sh
```

#### 7.2 创建告警服务
```bash
sudo tee /etc/systemd/system/ssh-alert.service << 'EOF'
[Unit]
Description=SSH Real-time Alert System
After=network.target

[Service]
Type=forking
ExecStart=/usr/local/bin/ssh-alert.sh
Restart=always
RestartSec=10
User=root

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl enable ssh-alert.service
sudo systemctl start ssh-alert.service
```

## 部署自动化脚本

### 完整部署脚本
```bash
#!/bin/bash
# ssh-audit-deploy.sh - SSH审计系统一键部署脚本

set -e

LOG_FILE="/tmp/ssh-audit-deploy.log"
BACKUP_DIR="/tmp/ssh-config-backup-$(date +%Y%m%d_%H%M%S)"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 备份现有配置
backup_configs() {
    log "备份现有配置..."
    mkdir -p "$BACKUP_DIR"

    [ -f /etc/ssh/sshd_config ] && cp /etc/ssh/sshd_config "$BACKUP_DIR/"
    [ -f /etc/audit/rules.d/audit.rules ] && cp /etc/audit/rules.d/audit.rules "$BACKUP_DIR/"
    [ -f /etc/profile ] && cp /etc/profile "$BACKUP_DIR/"

    log "配置已备份到: $BACKUP_DIR"
}

# 安装依赖
install_dependencies() {
    log "安装依赖包..."

    if command -v yum >/dev/null 2>&1; then
        yum install -y audit rsyslog logrotate
    elif command -v apt >/dev/null 2>&1; then
        apt update
        apt install -y auditd rsyslog logrotate
    else
        log "错误: 不支持的包管理器"
        exit 1
    fi
}

# 配置SSH服务器
configure_ssh() {
    log "配置SSH服务器..."

    # 备份原配置
    cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

    # 添加审计配置
    cat >> /etc/ssh/sshd_config << 'EOF'

# SSH审计配置
LogLevel VERBOSE
SyslogFacility AUTHPRIV
UsePAM yes
PrintLastLog yes
TCPKeepAlive yes
ClientAliveInterval 60
ClientAliveCountMax 3

# 会话记录（可选，取消注释启用）
# ForceCommand /usr/local/bin/ssh-session-logger.sh
EOF

    log "SSH配置已更新"
}

# 部署会话记录器
deploy_session_logger() {
    log "部署会话记录器..."

    # 创建日志目录
    mkdir -p /var/log/ssh-sessions
    chmod 755 /var/log/ssh-sessions

    # 部署会话记录脚本（前面已定义的脚本）
    # ... 这里会包含完整的脚本内容

    log "会话记录器部署完成"
}

# 配置审计系统
configure_audit() {
    log "配置系统审计..."

    # 启动auditd
    systemctl enable auditd
    systemctl start auditd

    # 添加审计规则（前面已定义的规则）
    # ... 这里会包含完整的规则内容

    log "系统审计配置完成"
}

# 配置日志轮转
configure_logrotate() {
    log "配置日志轮转..."

    # 添加SSH会话日志轮转配置（前面已定义）
    # ... 这里会包含完整的配置内容

    log "日志轮转配置完成"
}

# 验证部署
verify_deployment() {
    log "验证部署..."

    # 检查SSH配置
    if sshd -t; then
        log "✅ SSH配置验证通过"
    else
        log "❌ SSH配置验证失败"
        return 1
    fi

    # 检查审计服务
    if systemctl is-active auditd >/dev/null; then
        log "✅ 审计服务运行正常"
    else
        log "❌ 审计服务未运行"
        return 1
    fi

    # 检查日志目录
    if [ -d /var/log/ssh-sessions ]; then
        log "✅ 日志目录创建成功"
    else
        log "❌ 日志目录创建失败"
        return 1
    fi

    log "✅ 部署验证完成"
}

# 主函数
main() {
    log "开始SSH审计系统部署..."

    # 检查权限
    if [ "$EUID" -ne 0 ]; then
        log "错误: 需要root权限"
        exit 1
    fi

    backup_configs
    install_dependencies
    configure_ssh
    deploy_session_logger
    configure_audit
    configure_logrotate

    # 重启服务
    log "重启相关服务..."
    systemctl restart sshd
    systemctl restart auditd

    verify_deployment

    log "🎉 SSH审计系统部署完成!"
    log "📋 部署日志: $LOG_FILE"
    log "💾 配置备份: $BACKUP_DIR"

    echo ""
    echo "🔍 测试命令:"
    echo "  sudo tail -f /var/log/auth.log"
    echo "  sudo ls -la /var/log/ssh-sessions/"
    echo "  sudo ausearch -k command_exec"
}

# 执行部署
main "$@"
```

## 监控仪表板

### Grafana 仪表板配置
```json
{
  "dashboard": {
    "title": "SSH Audit Dashboard",
    "panels": [
      {
        "title": "SSH Login Attempts",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(ssh_login_total[5m])",
            "legendFormat": "Login Rate"
          }
        ]
      },
      {
        "title": "Failed Login Attempts by IP",
        "type": "table",
        "targets": [
          {
            "expr": "topk(10, sum by (client_ip) (ssh_failed_login_total))",
            "format": "table"
          }
        ]
      },
      {
        "title": "Active SSH Sessions",
        "type": "stat",
        "targets": [
          {
            "expr": "ssh_active_sessions",
            "legendFormat": "Active Sessions"
          }
        ]
      }
    ]
  }
}
```

这个完整的SSH审计方案提供了：

1. **多层次日志记录**：从连接到命令的全方位审计
2. **实时监控告警**：自动检测异常行为
3. **专业工具集成**：支持ELK、Grafana等现代监控栈
4. **自动化部署**：一键部署脚本
5. **安全防护**：日志保护和远程传输

相比SSL密钥提取的注入方案，SSH审计完全基于系统原生功能，更加稳定可靠。
