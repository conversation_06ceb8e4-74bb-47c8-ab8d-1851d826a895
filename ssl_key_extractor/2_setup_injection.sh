#!/bin/bash

# SSL密钥注入设置脚本
# 设置全局LD_PRELOAD和配置文件，使所有HTTPS应用自动被监控
# 
# 使用方法:
#   ./2_setup_injection.sh

set -e

# 配置
KEYLOG_LIB="$(pwd)/libkeylog_injector.so"
KEYLOG_FILE="/tmp/global_ssl_keylog.txt"
CONFIG_FILE="/etc/ssl-keylog.conf"
LD_SO_PRELOAD="/etc/ld.so.preload"
BACKUP_SUFFIX="backup_$(date +%s)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要root权限运行SSL注入设置"
        echo "请使用: sudo $0"
        exit 1
    fi
    log_info "权限检查通过"
}

# 检查SSL注入器库
check_ssl_injector() {
    log_step "检查SSL注入器库..."
    
    if [[ ! -f "$KEYLOG_LIB" ]]; then
        log_error "SSL注入器库不存在: $KEYLOG_LIB"
        log_info "请先编译: make"
        exit 1
    fi
    
    # 检查库文件格式
    if ! file "$KEYLOG_LIB" | grep -q "ELF 64-bit"; then
        log_error "SSL注入器库格式不正确"
        exit 1
    fi
    
    # 检查库依赖
    if ! ldd "$KEYLOG_LIB" &>/dev/null; then
        log_error "SSL注入器库依赖检查失败，可能与当前系统不兼容"
        log_info "请重新编译: make clean && make"
        exit 1
    fi
    
    log_success "SSL注入器库检查通过: $KEYLOG_LIB"
}

# 备份现有配置
backup_existing_config() {
    log_step "备份现有配置..."
    
    # 备份LD_PRELOAD文件
    if [[ -f "$LD_SO_PRELOAD" ]]; then
        cp "$LD_SO_PRELOAD" "${LD_SO_PRELOAD}.${BACKUP_SUFFIX}"
        log_info "已备份 $LD_SO_PRELOAD -> ${LD_SO_PRELOAD}.${BACKUP_SUFFIX}"
    fi
    
    # 备份配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        cp "$CONFIG_FILE" "${CONFIG_FILE}.${BACKUP_SUFFIX}"
        log_info "已备份 $CONFIG_FILE -> ${CONFIG_FILE}.${BACKUP_SUFFIX}"
    fi
    
    log_success "配置备份完成"
}

# 设置全局LD_PRELOAD
setup_global_preload() {
    log_step "设置全局LD_PRELOAD..."
    
    # 检查是否已经添加
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD" 2>/dev/null; then
        log_warn "SSL注入器已在全局LD_PRELOAD中"
        
        # 检查路径是否正确
        if grep -q "$KEYLOG_LIB" "$LD_SO_PRELOAD"; then
            log_info "路径正确，跳过添加"
            return 0
        else
            log_warn "路径不正确，将更新"
            # 移除旧的条目
            sed -i '/libkeylog_injector/d' "$LD_SO_PRELOAD"
        fi
    fi
    
    # 添加SSL注入器到全局预加载
    echo "$KEYLOG_LIB" >> "$LD_SO_PRELOAD"
    log_success "已添加到全局LD_PRELOAD: $LD_SO_PRELOAD"
    
    # 验证添加结果
    if grep -q "$KEYLOG_LIB" "$LD_SO_PRELOAD"; then
        log_success "LD_PRELOAD设置验证通过"
    else
        log_error "LD_PRELOAD设置验证失败"
        exit 1
    fi
}

# 创建配置文件
create_config_file() {
    log_step "创建SSL密钥提取器配置文件..."
    
    cat > "$CONFIG_FILE" << EOF
# SSL KEYLOG 全局配置文件
# 此文件被 libkeylog_injector.so 读取
# 创建时间: $(date)

# SSL密钥输出文件路径
KEYLOG_FILE=$KEYLOG_FILE

# 静默模式（1=启用，0=禁用）
# 启用后不会在控制台输出调试信息
KEYLOG_SILENT=1

# 详细日志（仅调试时启用，生产环境建议禁用）
# 启用后会在单独的日志文件中记录详细信息
KEYLOG_ENABLE_DETAILED_LOG=0

# 配置说明:
# - KEYLOG_FILE: SSL密钥记录文件，兼容Wireshark格式
# - KEYLOG_SILENT: 控制是否在控制台输出调试信息
# - KEYLOG_ENABLE_DETAILED_LOG: 控制是否启用详细日志记录
EOF
    
    chmod 644 "$CONFIG_FILE"
    log_success "配置文件已创建: $CONFIG_FILE"
}

# 创建SSL密钥文件
create_keylog_file() {
    log_step "创建SSL密钥文件..."
    
    # 创建目录
    mkdir -p "$(dirname "$KEYLOG_FILE")"
    
    # 创建文件
    touch "$KEYLOG_FILE"
    chmod 600 "$KEYLOG_FILE"
    
    # 清空文件内容（如果之前存在）
    > "$KEYLOG_FILE"
    
    log_success "SSL密钥文件已创建: $KEYLOG_FILE"
}

# 验证注入设置
verify_injection_setup() {
    log_step "验证注入设置..."
    
    local verification_failed=0
    
    # 检查LD_PRELOAD
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "$KEYLOG_LIB" "$LD_SO_PRELOAD"; then
        log_success "✓ 全局LD_PRELOAD设置正确"
    else
        log_error "✗ 全局LD_PRELOAD设置失败"
        verification_failed=1
    fi
    
    # 检查配置文件
    if [[ -f "$CONFIG_FILE" ]] && grep -q "KEYLOG_FILE=$KEYLOG_FILE" "$CONFIG_FILE"; then
        log_success "✓ 配置文件设置正确"
    else
        log_error "✗ 配置文件设置失败"
        verification_failed=1
    fi
    
    # 检查SSL密钥文件
    if [[ -f "$KEYLOG_FILE" ]] && [[ -w "$KEYLOG_FILE" ]]; then
        log_success "✓ SSL密钥文件设置正确"
    else
        log_error "✗ SSL密钥文件设置失败"
        verification_failed=1
    fi
    
    # 检查库文件权限
    if [[ -r "$KEYLOG_LIB" ]]; then
        log_success "✓ SSL注入器库权限正确"
    else
        log_error "✗ SSL注入器库权限不足"
        verification_failed=1
    fi
    
    if [[ $verification_failed -eq 1 ]]; then
        log_error "注入设置验证失败，请检查上述错误"
        exit 1
    fi
    
    log_success "注入设置验证通过"
}

# 显示设置摘要
show_setup_summary() {
    echo ""
    echo -e "${BLUE}=== SSL密钥注入设置完成 ===${NC}"
    echo ""
    echo -e "${GREEN}配置摘要:${NC}"
    echo "  • 注入器库: $KEYLOG_LIB"
    echo "  • 全局配置: $CONFIG_FILE"
    echo "  • SSL密钥文件: $KEYLOG_FILE"
    echo "  • 全局LD_PRELOAD: $LD_SO_PRELOAD"
    echo ""
    echo -e "${GREEN}备份文件:${NC}"
    if [[ -f "${LD_SO_PRELOAD}.${BACKUP_SUFFIX}" ]]; then
        echo "  • LD_PRELOAD备份: ${LD_SO_PRELOAD}.${BACKUP_SUFFIX}"
    fi
    if [[ -f "${CONFIG_FILE}.${BACKUP_SUFFIX}" ]]; then
        echo "  • 配置文件备份: ${CONFIG_FILE}.${BACKUP_SUFFIX}"
    fi
    echo ""
    echo -e "${YELLOW}重要提醒:${NC}"
    echo "• 现在所有新启动的HTTPS应用都将自动被监控"
    echo "• 对于已运行的服务（如nginx），需要重启才能生效"
    echo "• SSL密钥将自动记录到: $KEYLOG_FILE"
    echo ""
    echo -e "${CYAN}下一步操作:${NC}"
    echo "1. 重启需要监控的服务:"
    echo "   systemctl restart nginx"
    echo "   systemctl restart apache2"
    echo "   # 或重启其他HTTPS服务"
    echo ""
    echo "2. 运行抓包脚本:"
    echo "   ./3_capture_traffic.sh [时间]"
    echo ""
    echo "3. 完成后清理注入:"
    echo "   ./4_cleanup_injection.sh"
}

# 测试注入功能
test_injection() {
    log_step "测试SSL注入功能..."
    
    # 记录测试前的密钥数量
    local before_count=0
    if [[ -f "$KEYLOG_FILE" ]]; then
        before_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
    fi
    
    log_info "测试前密钥数量: $before_count"
    
    # 执行简单的HTTPS请求测试
    log_info "执行HTTPS测试请求..."
    if timeout 10 curl -s -o /dev/null https://httpbin.org/get 2>/dev/null; then
        log_success "HTTPS测试请求成功"
    else
        log_warn "HTTPS测试请求失败，可能是网络问题"
    fi
    
    # 等待一下让密钥写入
    sleep 2
    
    # 检查密钥增加
    local after_count=0
    if [[ -f "$KEYLOG_FILE" ]]; then
        after_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
    fi
    
    log_info "测试后密钥数量: $after_count"
    
    if [[ $after_count -gt $before_count ]]; then
        log_success "✅ SSL注入测试成功！新增 $((after_count - before_count)) 条密钥"
        
        # 显示最新的密钥记录（截断显示）
        if [[ $((after_count - before_count)) -le 3 ]]; then
            echo ""
            log_info "新增的密钥记录:"
            tail -$((after_count - before_count)) "$KEYLOG_FILE" | while read line; do
                echo "     ${line:0:80}..."
            done
        fi
        return 0
    else
        log_warn "⚠️ SSL注入测试未检测到新密钥"
        log_info "可能的原因:"
        log_info "  1. curl使用的SSL库不是OpenSSL"
        log_info "  2. 网络连接问题"
        log_info "  3. 注入器未正确加载"
        log_info "建议重启一个已知使用OpenSSL的服务进行测试"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}SSL密钥注入设置脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo "  --test        设置完成后执行测试"
    echo ""
    echo "功能:"
    echo "• 设置全局LD_PRELOAD"
    echo "• 创建SSL密钥提取器配置文件"
    echo "• 创建SSL密钥记录文件"
    echo "• 备份现有配置"
    echo "• 验证设置正确性"
    echo ""
    echo "注意事项:"
    echo "• 需要root权限运行"
    echo "• 会影响所有新启动的应用程序"
    echo "• 已运行的服务需要重启才能生效"
    echo "• 使用完毕后建议运行清理脚本"
}

# 主函数
main() {
    local run_test=0
    
    # 处理命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --test)
                run_test=1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${BLUE}=== SSL密钥注入设置 ===${NC}"
    echo ""
    echo "设置时间: $(date)"
    echo "设置主机: $(hostname)"
    echo ""
    
    # 执行设置步骤
    check_permissions
    check_ssl_injector
    backup_existing_config
    setup_global_preload
    create_config_file
    create_keylog_file
    verify_injection_setup
    
    # 显示设置摘要
    show_setup_summary
    
    # 可选的测试
    if [[ $run_test -eq 1 ]]; then
        echo ""
        test_injection
    fi
    
    echo ""
    log_success "SSL密钥注入设置完成！"
    
    exit 0
}

# 运行主函数
main "$@"
