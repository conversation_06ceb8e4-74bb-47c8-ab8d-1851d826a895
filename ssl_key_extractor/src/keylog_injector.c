#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <dlfcn.h>
#include <openssl/ssl.h>
#include <time.h>
#include <unistd.h>
#include <sys/stat.h>
#include <errno.h>

// 配置选项
#define DEFAULT_KEYLOG_FILE "/tmp/injected_keylog.txt"
#define DEFAULT_LOG_FILE "/tmp/keylog_injector.log"
#define MAX_PATH_LEN 1024
#define MAX_PROC_NAME 256

// 全局变量
static SSL_CTX* (*original_SSL_CTX_new)(const SSL_METHOD *method) = NULL;
static FILE *keylog_file = NULL;          // 纯粹的SSL keylog文件（兼容Wireshark）
static FILE *log_file = NULL;             // 详细日志文件（时间戳、进程信息等）
static char keylog_path[MAX_PATH_LEN] = {0};
static char log_path[MAX_PATH_LEN] = {0};
static char process_name[MAX_PROC_NAME] = {0};
static int silent_mode = 0;
static int initialized = 0;
static int enable_detailed_log = 0;       // 是否启用详细日志（默认禁用，调试时启用）

// 日志函数
static void log_info(const char *format, ...) {
    if (silent_mode) return;
    
    va_list args;
    va_start(args, format);
    printf("[KEYLOG-INJECTOR-%s] ", process_name[0] ? process_name : "UNKNOWN");
    vprintf(format, args);
    printf("\n");
    fflush(stdout);
    va_end(args);
}

// 写入详细日志文件
static void write_detailed_log(const char *format, ...) {
    if (!enable_detailed_log || !log_file) return;
    
    time_t now = time(NULL);
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", localtime(&now));
    
    fprintf(log_file, "[%s] [%s:%d] ", timestamp, process_name, getpid());
    
    va_list args;
    va_start(args, format);
    vfprintf(log_file, format, args);
    va_end(args);
    
    fprintf(log_file, "\n");
    fflush(log_file);
}

// 检查文件是否存在
static int file_exists(const char *path) {
    struct stat st;
    return (stat(path, &st) == 0);
}

// 检查keylog文件是否需要重新创建
static int check_keylog_file_validity() {
    if (!keylog_file) return 0;
    
    // 检查文件是否仍然存在
    if (!file_exists(keylog_path)) {
        log_info("WARNING: SSL keylog文件被删除，需要重新创建: %s", keylog_path);
        write_detailed_log("WARNING: SSL keylog文件被删除，需要重新创建: %s", keylog_path);
        
        // 关闭当前的文件句柄
        fclose(keylog_file);
        keylog_file = NULL;
        return 0;
    }
    
    return 1;
}

// 获取当前进程名
static void get_process_name() {
    FILE *f = fopen("/proc/self/comm", "r");
    if (f) {
        if (fgets(process_name, sizeof(process_name), f)) {
            char *newline = strchr(process_name, '\n');
            if (newline) *newline = '\0';
        }
        fclose(f);
    }
    
    if (!process_name[0]) {
        strncpy(process_name, "unknown", sizeof(process_name) - 1);
    }
}

// 从配置文件读取设置
static void read_config_file() {
    FILE *conf_file = fopen("/etc/ssl-keylog.conf", "r");
    if (!conf_file) return;
    
    char line[1024];
    while (fgets(line, sizeof(line), conf_file)) {
        // 移除换行符
        char *newline = strchr(line, '\n');
        if (newline) *newline = '\0';
        
        // 跳过注释和空行
        if (line[0] == '#' || line[0] == '\0') continue;
        
        // 解析配置项
        if (strncmp(line, "KEYLOG_FILE=", 12) == 0) {
            strncpy(keylog_path, line + 12, MAX_PATH_LEN - 1);
            keylog_path[MAX_PATH_LEN - 1] = '\0';
        } else if (strncmp(line, "KEYLOG_SILENT=", 14) == 0) {
            silent_mode = (strcmp(line + 14, "1") == 0);
        } else if (strncmp(line, "KEYLOG_ENABLE_DETAILED_LOG=", 28) == 0) {
            enable_detailed_log = (strcmp(line + 28, "1") == 0);
        }
    }
    
    fclose(conf_file);
}

// 初始化配置
static void init_config() {
    if (initialized) return;
    
    get_process_name();
    
    // 首先从配置文件读取（全局配置）
    read_config_file();
    
    // 然后从环境变量读取（可以覆盖配置文件）
    if (getenv("KEYLOG_SILENT")) {
        silent_mode = 1;
    }
    
    // 检查是否启用详细日志（默认禁用）
    if (getenv("KEYLOG_ENABLE_DETAILED_LOG")) {
        enable_detailed_log = 1;
    }
    
    // 确定keylog文件路径
    const char *env_path = getenv("KEYLOG_FILE");
    if (env_path && strlen(env_path) > 0) {
        strncpy(keylog_path, env_path, MAX_PATH_LEN - 1);
    } else if (keylog_path[0] == '\0') {
        // 如果配置文件中没有设置，使用默认值
        strncpy(keylog_path, DEFAULT_KEYLOG_FILE, MAX_PATH_LEN - 1);
    }
    
    // 确定详细日志文件路径
    const char *log_env_path = getenv("KEYLOG_LOG_FILE");
    if (log_env_path && strlen(log_env_path) > 0) {
        strncpy(log_path, log_env_path, MAX_PATH_LEN - 1);
    } else {
        strncpy(log_path, DEFAULT_LOG_FILE, MAX_PATH_LEN - 1);
    }
    
    // 创建目录
    char *dir_end = strrchr(keylog_path, '/');
    if (dir_end) {
        *dir_end = '\0';
        mkdir(keylog_path, 0755);
        *dir_end = '/';
    }
    
    dir_end = strrchr(log_path, '/');
    if (dir_end) {
        *dir_end = '\0';
        mkdir(log_path, 0755);
        *dir_end = '/';
    }
    
    initialized = 1;
    log_info("注入器初始化完成 - 进程: %s (PID: %d)", process_name, getpid());
    log_info("SSL Keylog文件: %s", keylog_path);
    if (enable_detailed_log) {
        log_info("详细日志文件: %s", log_path);
    }
}

// 打开或重新打开keylog文件
static int ensure_keylog_file() {
    // 如果文件句柄存在，检查文件是否仍然有效
    if (keylog_file && !check_keylog_file_validity()) {
        // 文件已被删除，需要重新创建
    }
    
    // 如果文件句柄不存在，尝试打开
    if (!keylog_file) {
        keylog_file = fopen(keylog_path, "a");
        if (!keylog_file) {
            log_info("WARNING: 无法打开SSL keylog文件: %s (errno: %d)", keylog_path, errno);
            write_detailed_log("WARNING: 无法打开SSL keylog文件: %s (errno: %d)", keylog_path, errno);
            return 0;
        }
        
        chmod(keylog_path, 0600);
        setvbuf(keylog_file, NULL, _IOLBF, 0);
        
        log_info("SUCCESS: SSL keylog文件已打开: %s", keylog_path);
        write_detailed_log("SUCCESS: SSL keylog文件已打开: %s", keylog_path);
    }
    
    return 1;
}

// 打开详细日志文件
static int open_log_file() {
    if (!enable_detailed_log || log_file) return 1;
    
    log_file = fopen(log_path, "a");
    if (!log_file) {
        log_info("WARNING: 无法打开详细日志文件: %s (errno: %d)", log_path, errno);
        return 0;
    }
    
    chmod(log_path, 0600);
    setvbuf(log_file, NULL, _IOLBF, 0);
    
    log_info("SUCCESS: 详细日志文件已打开: %s", log_path);
    
    // 写入会话开始标记
    write_detailed_log("=== SSL KEYLOG会话开始 ===");
    write_detailed_log("进程: %s (PID: %d)", process_name, getpid());
    write_detailed_log("SSL Keylog文件: %s", keylog_path);
    
    return 1;
}

// 关闭文件
static void close_files() {
    if (keylog_file) {
        fclose(keylog_file);
        keylog_file = NULL;
        log_info("SSL keylog文件已关闭");
    }
    
    if (log_file) {
        write_detailed_log("=== SSL KEYLOG会话结束 ===");
        fclose(log_file);
        log_file = NULL;
        log_info("详细日志文件已关闭");
    }
}

// KEYLOG 回调函数（带自动重新创建功能）
void our_keylog_callback(const SSL *ssl, const char *line) {
    // 确保keylog文件可用（自动重新创建如果需要）
    if (!ensure_keylog_file()) {
        log_info("ERROR: 无法确保SSL keylog文件可用");
        return;
    }
    
    // 写入纯粹的SSL keylog数据（兼容Wireshark）
    if (keylog_file) {
        int ret = fprintf(keylog_file, "%s\n", line);
        if (ret < 0) {
            log_info("ERROR: 写入SSL keylog失败");
            // 尝试重新打开文件
            fclose(keylog_file);
            keylog_file = NULL;
            if (ensure_keylog_file()) {
                fprintf(keylog_file, "%s\n", line);
                fflush(keylog_file);
            }
        } else {
            fflush(keylog_file);
        }
    }
    
    // 写入详细日志（包含时间戳和进程信息）
    write_detailed_log("SSL_KEYLOG: %s", line);
    
    if (!silent_mode) {
        log_info("TLS密钥已提取 - %.50s...", line);
    }
}

// 拦截 SSL_CTX_new 函数
SSL_CTX* SSL_CTX_new(const SSL_METHOD *method) {
    // 初始化配置
    if (!initialized) {
        init_config();
    }
    
    log_info("SSL_CTX_new 被调用 (method: %p)", method);
    write_detailed_log("SSL_CTX_new 被调用 (method: %p)", method);
    
    // 加载原始函数
    if (!original_SSL_CTX_new) {
        original_SSL_CTX_new = dlsym(RTLD_NEXT, "SSL_CTX_new");
        if (!original_SSL_CTX_new) {
            log_info("ERROR: 无法加载原始函数: %s", dlerror());
            write_detailed_log("ERROR: 无法加载原始函数: %s", dlerror());
            return NULL;
        }
        log_info("原始 SSL_CTX_new 函数已加载");
        write_detailed_log("原始 SSL_CTX_new 函数已加载");
    }
    
    // 调用原始函数
    SSL_CTX *ctx = original_SSL_CTX_new(method);
    
    if (!ctx) {
        log_info("ERROR: 原始 SSL_CTX_new 返回 NULL");
        write_detailed_log("ERROR: 原始 SSL_CTX_new 返回 NULL");
        return NULL;
    }
    
    log_info("SSL_CTX 创建成功: %p", ctx);
    write_detailed_log("SSL_CTX 创建成功: %p", ctx);
    
    // 确保keylog文件可用
    if (!ensure_keylog_file()) {
        log_info("WARNING: 无法设置SSL keylog记录，但SSL上下文仍然有效");
        write_detailed_log("WARNING: 无法设置SSL keylog记录");
        return ctx;
    }
    
    if (!log_file) {
        open_log_file();  // 忽略失败，详细日志是可选的
    }
    
    // 注入 KEYLOG 回调
    if (keylog_file) {
        SSL_CTX_set_keylog_callback(ctx, our_keylog_callback);
        log_info("SUCCESS: KEYLOG回调已注入到 SSL_CTX: %p", ctx);
        write_detailed_log("SUCCESS: KEYLOG回调已注入到 SSL_CTX: %p", ctx);
    }
    
    return ctx;
}

// 构造函数
__attribute__((constructor))
void init_injector() {
    init_config();
    log_info("SSL KEYLOG注入器已加载");
    if (enable_detailed_log) {
        open_log_file();
        write_detailed_log("SSL KEYLOG注入器已加载");
    }
}

// 析构函数
__attribute__((destructor))
void cleanup_injector() {
    log_info("开始清理SSL KEYLOG注入器...");
    write_detailed_log("开始清理SSL KEYLOG注入器...");
    close_files();
    log_info("SSL KEYLOG注入器清理完成");
} 