#!/bin/bash

# 便携式HTTPS流量抓包工具
# 用于客户现场快速部署、抓包、清理
# 
# 使用方法:
#   ./portable_capture.sh [时间(秒)] [网络接口] [过滤条件]
#   ./portable_capture.sh 60                    # 抓包60秒，自动检测接口
#   ./portable_capture.sh 120 eth0              # 在eth0接口抓包120秒
#   ./portable_capture.sh 300 any "port 443"    # 抓包300秒，只抓443端口

set -e

# 默认配置
DEFAULT_DURATION=60
DEFAULT_INTERFACE="any"
DEFAULT_FILTER="port 443 or port 8443 or port 9443"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="./capture_${TIMESTAMP}"
PCAP_FILE="${OUTPUT_DIR}/https_traffic_${TIMESTAMP}.pcap"
KEYLOG_FILE="${OUTPUT_DIR}/ssl_keylog_${TIMESTAMP}.txt"
GLOBAL_KEYLOG="/tmp/global_ssl_keylog.txt"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}便携式HTTPS流量抓包工具${NC}"
    echo ""
    echo "用法: $0 [时间(秒)] [网络接口] [过滤条件]"
    echo ""
    echo "参数:"
    echo "  时间(秒)    抓包持续时间，默认60秒"
    echo "  网络接口    网络接口名称，默认'any'(所有接口)"
    echo "  过滤条件    tcpdump过滤条件，默认'port 443 or port 8443 or port 9443'"
    echo ""
    echo "示例:"
    echo "  $0                           # 抓包60秒，所有HTTPS端口"
    echo "  $0 120                       # 抓包120秒"
    echo "  $0 300 eth0                  # 在eth0接口抓包300秒"
    echo "  $0 180 any \"port 443\"        # 只抓443端口，180秒"
    echo ""
    echo "输出:"
    echo "  - PCAP文件: 包含加密的HTTPS流量"
    echo "  - KeyLog文件: 包含SSL密钥，用于Wireshark解密"
    echo "  - 打包文件: 便于传输的压缩包"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要root权限运行"
        echo "请使用: sudo $0 $*"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查tcpdump
    if ! command -v tcpdump &> /dev/null; then
        missing_deps+=("tcpdump")
    fi
    
    # 检查SSL注入器库
    if [[ ! -f "./libkeylog_injector.so" ]]; then
        log_error "SSL注入器库不存在: ./libkeylog_injector.so"
        log_info "请先编译: make"
        exit 1
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_info "请安装: yum install -y ${missing_deps[*]} 或 apt install -y ${missing_deps[*]}"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 检测网络接口
detect_interface() {
    if [[ "$1" == "any" ]]; then
        echo "any"
        return
    fi
    
    # 检查指定接口是否存在
    if [[ -n "$1" ]] && ip link show "$1" &>/dev/null; then
        echo "$1"
        return
    fi
    
    # 自动检测活跃接口
    local active_interface=$(ip route | grep default | awk '{print $5}' | head -1)
    if [[ -n "$active_interface" ]]; then
        log_info "自动检测到网络接口: $active_interface"
        echo "$active_interface"
    else
        log_warn "无法检测网络接口，使用默认值: any"
        echo "any"
    fi
}

# 启用SSL密钥注入
enable_ssl_injection() {
    log_step "启用SSL密钥注入..."
    
    # 备份现有配置
    if [[ -f "/etc/ld.so.preload" ]]; then
        cp /etc/ld.so.preload /etc/ld.so.preload.backup.$(date +%s) 2>/dev/null || true
    fi
    
    # 设置全局LD_PRELOAD
    local abs_lib_path=$(realpath "./libkeylog_injector.so")
    echo "$abs_lib_path" > /etc/ld.so.preload
    
    # 创建配置文件
    cat > /etc/ssl-keylog.conf << EOF
# SSL密钥提取器配置
KEYLOG_FILE=$GLOBAL_KEYLOG
KEYLOG_SILENT=1
KEYLOG_ENABLE_DETAILED_LOG=0
EOF
    
    # 创建密钥文件
    touch "$GLOBAL_KEYLOG"
    chmod 600 "$GLOBAL_KEYLOG"
    
    log_info "SSL密钥注入已启用"
    log_info "密钥文件: $GLOBAL_KEYLOG"
}

# 禁用SSL密钥注入
disable_ssl_injection() {
    log_step "禁用SSL密钥注入..."
    
    # 清空LD_PRELOAD
    if [[ -f "/etc/ld.so.preload" ]]; then
        > /etc/ld.so.preload
    fi
    
    # 删除配置文件
    rm -f /etc/ssl-keylog.conf
    
    log_info "SSL密钥注入已禁用"
}

# 创建输出目录
create_output_dir() {
    log_step "创建输出目录..."
    mkdir -p "$OUTPUT_DIR"
    log_info "输出目录: $OUTPUT_DIR"
}

# 开始抓包
start_capture() {
    local duration=$1
    local interface=$2
    local filter=$3
    
    log_step "开始HTTPS流量抓包..."
    log_info "持续时间: ${duration}秒"
    log_info "网络接口: $interface"
    log_info "过滤条件: $filter"
    log_info "PCAP文件: $PCAP_FILE"
    
    # 显示倒计时
    echo -e "${YELLOW}抓包进行中...${NC}"
    
    # 启动tcpdump（后台运行）
    tcpdump -i "$interface" -w "$PCAP_FILE" -s 0 "$filter" &
    local tcpdump_pid=$!
    
    # 倒计时显示
    for ((i=duration; i>0; i--)); do
        printf "\r${CYAN}剩余时间: %02d:%02d${NC}" $((i/60)) $((i%60))
        sleep 1
    done
    
    # 停止tcpdump
    kill $tcpdump_pid 2>/dev/null || true
    wait $tcpdump_pid 2>/dev/null || true
    
    echo -e "\n${GREEN}抓包完成！${NC}"
}

# 收集SSL密钥
collect_ssl_keys() {
    log_step "收集SSL密钥..."
    
    if [[ -f "$GLOBAL_KEYLOG" ]] && [[ -s "$GLOBAL_KEYLOG" ]]; then
        cp "$GLOBAL_KEYLOG" "$KEYLOG_FILE"
        local key_count=$(wc -l < "$KEYLOG_FILE")
        log_info "收集到 $key_count 条SSL密钥记录"
        log_info "密钥文件: $KEYLOG_FILE"
    else
        log_warn "未找到SSL密钥记录"
        touch "$KEYLOG_FILE"
    fi
}

# 生成分析报告
generate_report() {
    log_step "生成分析报告..."
    
    local report_file="${OUTPUT_DIR}/capture_report_${TIMESTAMP}.txt"
    
    cat > "$report_file" << EOF
HTTPS流量抓包报告
================

抓包时间: $(date)
持续时间: $1 秒
网络接口: $2
过滤条件: $3

文件信息:
--------
PCAP文件: $(basename "$PCAP_FILE")
大小: $(ls -lh "$PCAP_FILE" 2>/dev/null | awk '{print $5}' || echo "未知")

SSL密钥文件: $(basename "$KEYLOG_FILE")
密钥记录数: $(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo "0")

系统信息:
--------
操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")
内核版本: $(uname -r)
网络接口: $(ip addr show | grep -E "^[0-9]+:" | awk '{print $2}' | tr -d ':' | tr '\n' ' ')

使用说明:
--------
1. 将PCAP文件和SSL密钥文件下载到本地
2. 在Wireshark中打开PCAP文件
3. 在Wireshark中配置SSL密钥文件:
   Edit -> Preferences -> Protocols -> TLS -> (Pre)-Master-Secret log filename
4. 设置密钥文件路径后重新加载PCAP文件即可看到解密内容

注意事项:
--------
- 确保抓包期间有HTTPS流量产生
- SSL密钥只能解密使用OpenSSL的应用程序流量
- Java、Node.js、Go等应用可能无法解密
EOF
    
    log_info "分析报告: $report_file"
}

# 打包文件
package_files() {
    log_step "打包抓包文件..."
    
    local package_file="https_capture_${TIMESTAMP}.tar.gz"
    
    cd "$(dirname "$OUTPUT_DIR")"
    tar -czf "$package_file" "$(basename "$OUTPUT_DIR")"
    
    log_info "打包完成: $package_file"
    log_info "文件大小: $(ls -lh "$package_file" | awk '{print $5}')"
    
    echo ""
    echo -e "${GREEN}=== 抓包完成 ===${NC}"
    echo -e "输出目录: ${CYAN}$OUTPUT_DIR${NC}"
    echo -e "打包文件: ${CYAN}$package_file${NC}"
    echo ""
    echo -e "${YELLOW}下一步操作:${NC}"
    echo "1. 下载打包文件到本地"
    echo "2. 解压后在Wireshark中打开PCAP文件"
    echo "3. 配置SSL密钥文件进行解密分析"
}

# 清理环境
cleanup() {
    log_step "清理环境..."
    
    # 禁用SSL注入
    disable_ssl_injection
    
    # 清理临时文件
    rm -f "$GLOBAL_KEYLOG"
    
    log_info "环境清理完成"
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    # 解析参数
    local duration=${1:-$DEFAULT_DURATION}
    local interface_input=${2:-$DEFAULT_INTERFACE}
    local filter=${3:-$DEFAULT_FILTER}
    
    # 显示帮助
    if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
        show_help
        exit 0
    fi
    
    # 验证参数
    if ! [[ "$duration" =~ ^[0-9]+$ ]] || [[ "$duration" -lt 1 ]]; then
        log_error "无效的时间参数: $duration"
        echo "时间必须是正整数（秒）"
        exit 1
    fi
    
    local interface=$(detect_interface "$interface_input")
    
    echo -e "${BLUE}=== 便携式HTTPS流量抓包工具 ===${NC}"
    echo ""
    
    # 执行步骤
    check_permissions
    check_dependencies
    create_output_dir
    enable_ssl_injection
    
    # 等待一下让注入生效
    sleep 2
    
    start_capture "$duration" "$interface" "$filter"
    collect_ssl_keys
    generate_report "$duration" "$interface" "$filter"
    package_files
    
    echo -e "${GREEN}抓包任务完成！${NC}"
}

# 运行主函数
main "$@"
