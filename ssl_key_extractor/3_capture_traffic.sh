#!/bin/bash

# HTTPS流量抓包脚本
# 执行tcpdump抓包并收集SSL密钥，生成可用于Wireshark解密的文件
# 
# 使用方法:
#   ./3_capture_traffic.sh [时间(秒)] [网络接口] [过滤条件]
#   ./3_capture_traffic.sh 60                    # 抓包60秒，自动检测接口
#   ./3_capture_traffic.sh 120 eth0              # 在eth0接口抓包120秒
#   ./3_capture_traffic.sh 300 any "port 443"    # 抓包300秒，只抓443端口

set -e

# 默认配置
DEFAULT_DURATION=60
DEFAULT_INTERFACE="any"
DEFAULT_FILTER="port 443 or port 8443 or port 9443"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="./capture_${TIMESTAMP}"
PCAP_FILE="${OUTPUT_DIR}/https_traffic_${TIMESTAMP}.pcap"
KEYLOG_FILE="${OUTPUT_DIR}/ssl_keylog_${TIMESTAMP}.txt"
GLOBAL_KEYLOG="/tmp/global_ssl_keylog.txt"
REPORT_FILE="${OUTPUT_DIR}/capture_report_${TIMESTAMP}.txt"

# 全局变量
KEYLOG_START_COUNT=0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}HTTPS流量抓包脚本${NC}"
    echo ""
    echo "用法: $0 [时间(秒)] [网络接口] [过滤条件]"
    echo ""
    echo "参数:"
    echo "  时间(秒)    抓包持续时间，默认60秒"
    echo "  网络接口    网络接口名称，默认'any'(所有接口)"
    echo "  过滤条件    tcpdump过滤条件，默认'port 443 or port 8443 or port 9443'"
    echo ""
    echo "示例:"
    echo "  $0                           # 抓包60秒，所有HTTPS端口"
    echo "  $0 120                       # 抓包120秒"
    echo "  $0 300 eth0                  # 在eth0接口抓包300秒"
    echo "  $0 180 any \"port 443\"        # 只抓443端口，180秒"
    echo ""
    echo "输出文件:"
    echo "  • PCAP文件: 包含加密的HTTPS流量"
    echo "  • KeyLog文件: 包含SSL密钥，用于Wireshark解密"
    echo "  • 报告文件: 抓包摘要和使用说明"
    echo "  • 打包文件: 便于传输的压缩包"
    echo ""
    echo "前置条件:"
    echo "  • 需要root权限"
    echo "  • 需要先运行 ./2_setup_injection.sh"
    echo "  • 需要重启相关HTTPS服务"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要root权限运行抓包脚本"
        echo "请使用: sudo $0 $*"
        exit 1
    fi
    log_info "权限检查通过"
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查tcpdump
    if ! command -v tcpdump &> /dev/null; then
        missing_deps+=("tcpdump")
    fi
    
    # 检查tar和gzip（用于打包）
    if ! command -v tar &> /dev/null; then
        missing_deps+=("tar")
    fi
    
    if ! command -v gzip &> /dev/null; then
        missing_deps+=("gzip")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖: ${missing_deps[*]}"
        log_info "请安装: yum install -y ${missing_deps[*]} 或 apt install -y ${missing_deps[*]}"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查SSL注入状态
check_injection_status() {
    log_step "检查SSL注入状态..."
    
    # 检查LD_PRELOAD
    if [[ ! -f "/etc/ld.so.preload" ]] || ! grep -q "libkeylog_injector" "/etc/ld.so.preload"; then
        log_error "SSL注入未启用"
        log_info "请先运行: ./2_setup_injection.sh"
        exit 1
    fi
    
    # 检查全局密钥文件
    if [[ ! -f "$GLOBAL_KEYLOG" ]]; then
        log_warn "全局SSL密钥文件不存在，将创建: $GLOBAL_KEYLOG"
        touch "$GLOBAL_KEYLOG"
        chmod 600 "$GLOBAL_KEYLOG"
    fi
    
    log_success "SSL注入状态检查通过"
}

# 检测网络接口
detect_interface() {
    local input_interface=$1
    
    if [[ "$input_interface" == "any" ]]; then
        echo "any"
        return
    fi
    
    # 检查指定接口是否存在
    if [[ -n "$input_interface" ]] && ip link show "$input_interface" &>/dev/null; then
        echo "$input_interface"
        return
    fi
    
    # 自动检测活跃接口
    local active_interface=$(ip route | grep default | awk '{print $5}' | head -1)
    if [[ -n "$active_interface" ]]; then
        log_info "自动检测到网络接口: $active_interface"
        echo "$active_interface"
    else
        log_warn "无法检测网络接口，使用默认值: any"
        echo "any"
    fi
}

# 创建输出目录
create_output_dir() {
    log_step "创建输出目录..."
    mkdir -p "$OUTPUT_DIR"
    log_success "输出目录: $OUTPUT_DIR"
}

# 准备SSL密钥文件（不清空，记录起始位置）
prepare_keylog_file() {
    log_step "准备SSL密钥文件..."

    # 确保全局密钥文件存在
    if [[ ! -f "$GLOBAL_KEYLOG" ]]; then
        touch "$GLOBAL_KEYLOG"
        chmod 600 "$GLOBAL_KEYLOG"
    fi

    # 记录抓包开始前的密钥数量
    KEYLOG_START_COUNT=$(wc -l < "$GLOBAL_KEYLOG" 2>/dev/null || echo 0)

    log_info "抓包前已有密钥记录: $KEYLOG_START_COUNT 条"
    log_info "将只收集抓包期间新增的SSL密钥"

    log_success "SSL密钥文件已准备: $GLOBAL_KEYLOG"
}

# 开始抓包
start_capture() {
    local duration=$1
    local interface=$2
    local filter=$3
    
    log_step "开始HTTPS流量抓包..."
    log_info "持续时间: ${duration}秒"
    log_info "网络接口: $interface"
    log_info "过滤条件: $filter"
    log_info "PCAP文件: $PCAP_FILE"
    
    # 显示抓包状态
    echo ""
    echo -e "${YELLOW}=== 抓包进行中 ===${NC}"
    echo -e "${CYAN}提示: 现在可以访问HTTPS网站或重启HTTPS服务来生成流量${NC}"
    echo ""
    
    # 启动tcpdump（后台运行）
    tcpdump -i "$interface" -w "$PCAP_FILE" -s 0 "$filter" &
    local tcpdump_pid=$!
    
    # 倒计时显示
    for ((i=duration; i>0; i--)); do
        # 检查tcpdump是否还在运行
        if ! kill -0 $tcpdump_pid 2>/dev/null; then
            log_error "tcpdump进程意外终止"
            exit 1
        fi
        
        # 显示进度和新增密钥数量
        local current_keys=0
        if [[ -f "$GLOBAL_KEYLOG" ]]; then
            current_keys=$(wc -l < "$GLOBAL_KEYLOG" 2>/dev/null || echo 0)
        fi
        local new_keys=$((current_keys - KEYLOG_START_COUNT))

        printf "\r${CYAN}剩余时间: %02d:%02d | 新增SSL密钥: %d 条${NC}" \
               $((i/60)) $((i%60)) $new_keys
        sleep 1
    done
    
    # 停止tcpdump
    kill $tcpdump_pid 2>/dev/null || true
    wait $tcpdump_pid 2>/dev/null || true
    
    echo ""
    echo ""
    log_success "抓包完成！"
}

# 收集SSL密钥（只收集新增的）
collect_ssl_keys() {
    log_step "收集SSL密钥..."

    local current_count=0
    if [[ -f "$GLOBAL_KEYLOG" ]]; then
        current_count=$(wc -l < "$GLOBAL_KEYLOG" 2>/dev/null || echo 0)
    fi

    local new_keys_count=$((current_count - KEYLOG_START_COUNT))

    if [[ $new_keys_count -gt 0 ]]; then
        # 只提取新增的密钥记录
        tail -n "$new_keys_count" "$GLOBAL_KEYLOG" > "$KEYLOG_FILE"
        log_success "收集到 $new_keys_count 条新的SSL密钥记录"
        log_info "密钥文件: $KEYLOG_FILE"

        # 显示部分密钥记录（用于验证）
        if [[ $new_keys_count -le 5 ]]; then
            echo ""
            log_info "新增的SSL密钥记录:"
            while read line; do
                echo "     ${line:0:80}..."
            done < "$KEYLOG_FILE"
        elif [[ $new_keys_count -gt 5 ]]; then
            echo ""
            log_info "新增的SSL密钥记录（前3条）:"
            head -3 "$KEYLOG_FILE" | while read line; do
                echo "     ${line:0:80}..."
            done
        fi
    else
        log_warn "抓包期间未捕获到新的SSL密钥记录"
        log_info "抓包前: $KEYLOG_START_COUNT 条，抓包后: $current_count 条"
        log_info "可能的原因:"
        log_info "  1. 抓包期间没有新的HTTPS连接"
        log_info "  2. 应用程序未使用OpenSSL"
        log_info "  3. 服务未重启，注入未生效"
        touch "$KEYLOG_FILE"
    fi
}

# 验证抓包文件
verify_capture_files() {
    log_step "验证抓包文件..."
    
    # 检查PCAP文件
    if [[ -f "$PCAP_FILE" ]]; then
        local pcap_size=$(ls -lh "$PCAP_FILE" | awk '{print $5}')
        log_success "PCAP文件: $PCAP_FILE ($pcap_size)"
        
        # 使用tcpdump验证文件格式
        if tcpdump -r "$PCAP_FILE" -c 1 &>/dev/null; then
            log_success "PCAP文件格式验证通过"
        else
            log_error "PCAP文件格式验证失败"
        fi
    else
        log_error "PCAP文件不存在"
    fi
    
    # 检查密钥文件
    if [[ -f "$KEYLOG_FILE" ]]; then
        local key_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
        log_success "SSL密钥文件: $KEYLOG_FILE ($key_count 条记录)"
    else
        log_error "SSL密钥文件不存在"
    fi
}

# 生成分析报告
generate_report() {
    local duration=$1
    local interface=$2
    local filter=$3
    
    log_step "生成分析报告..."
    
    local pcap_size="未知"
    local key_count=0
    
    if [[ -f "$PCAP_FILE" ]]; then
        pcap_size=$(ls -lh "$PCAP_FILE" 2>/dev/null | awk '{print $5}' || echo "未知")
    fi
    
    if [[ -f "$KEYLOG_FILE" ]]; then
        key_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
    fi
    
    cat > "$REPORT_FILE" << EOF
HTTPS流量抓包报告
================

抓包信息:
--------
抓包时间: $(date)
持续时间: $duration 秒
网络接口: $interface
过滤条件: $filter
抓包主机: $(hostname)

文件信息:
--------
PCAP文件: $(basename "$PCAP_FILE")
文件大小: $pcap_size
SSL密钥文件: $(basename "$KEYLOG_FILE")
密钥记录数: $key_count

系统信息:
--------
操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")
内核版本: $(uname -r)
网络接口列表: $(ip addr show | grep -E "^[0-9]+:" | awk '{print $2}' | tr -d ':' | tr '\n' ' ')

Wireshark解密配置:
-----------------
1. 在Wireshark中打开PCAP文件: $(basename "$PCAP_FILE")

2. 配置SSL密钥文件:
   - 菜单: Edit -> Preferences
   - 协议: Protocols -> TLS
   - 设置: (Pre)-Master-Secret log filename
   - 路径: $(basename "$KEYLOG_FILE")

3. 重新加载PCAP文件即可看到解密的HTTPS内容

使用说明:
--------
• 确保PCAP文件和SSL密钥文件在同一目录
• SSL密钥只能解密使用OpenSSL的应用程序流量
• Java、Node.js、Go等应用可能使用不同的SSL库，无法解密
• 如果没有密钥记录，请检查服务是否重启以及是否有HTTPS流量

故障排除:
--------
如果无法解密HTTPS流量:
1. 检查SSL密钥文件是否有内容
2. 确认抓包期间有HTTPS流量产生
3. 验证目标应用使用OpenSSL库
4. 检查服务是否在注入设置后重启

技术细节:
--------
• 使用LD_PRELOAD机制注入SSL密钥提取器
• 兼容OpenSSL 1.0.x、1.1.x、3.x版本
• 支持TLS 1.0-1.3协议
• 密钥格式兼容Wireshark标准

生成时间: $(date)
EOF
    
    log_success "分析报告: $REPORT_FILE"
}

# 打包文件
package_files() {
    log_step "打包抓包文件..."
    
    local package_file="https_capture_${TIMESTAMP}.tar.gz"
    
    # 切换到输出目录的父目录进行打包
    cd "$(dirname "$OUTPUT_DIR")"
    tar -czf "$package_file" "$(basename "$OUTPUT_DIR")"
    
    local package_size=$(ls -lh "$package_file" | awk '{print $5}')
    log_success "打包完成: $package_file ($package_size)"
    
    # 计算MD5校验和
    local md5_hash=$(md5sum "$package_file" 2>/dev/null | awk '{print $1}' || echo "N/A")
    
    echo ""
    echo -e "${GREEN}=== 抓包任务完成 ===${NC}"
    echo ""
    echo -e "${CYAN}输出文件:${NC}"
    echo "  • 输出目录: $OUTPUT_DIR"
    echo "  • PCAP文件: $(basename "$PCAP_FILE")"
    echo "  • SSL密钥: $(basename "$KEYLOG_FILE")"
    echo "  • 分析报告: $(basename "$REPORT_FILE")"
    echo "  • 打包文件: $package_file"
    echo "  • MD5校验: $md5_hash"
    echo ""
    echo -e "${YELLOW}下一步操作:${NC}"
    echo "1. 下载打包文件到本地进行分析"
    echo "2. 在Wireshark中打开PCAP文件"
    echo "3. 配置SSL密钥文件进行解密"
    echo "4. 运行清理脚本: ./4_cleanup_injection.sh"
}

# 主函数
main() {
    # 解析参数
    local duration=${1:-$DEFAULT_DURATION}
    local interface_input=${2:-$DEFAULT_INTERFACE}
    local filter=${3:-$DEFAULT_FILTER}
    
    # 显示帮助
    if [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
        show_help
        exit 0
    fi
    
    # 验证参数
    if ! [[ "$duration" =~ ^[0-9]+$ ]] || [[ "$duration" -lt 1 ]]; then
        log_error "无效的时间参数: $duration"
        echo "时间必须是正整数（秒）"
        exit 1
    fi
    
    if [[ "$duration" -gt 3600 ]]; then
        log_warn "抓包时间较长: ${duration}秒，确认继续？(y/N)"
        read -r confirm
        if [[ "$confirm" != "y" ]] && [[ "$confirm" != "Y" ]]; then
            log_info "用户取消操作"
            exit 0
        fi
    fi
    
    local interface=$(detect_interface "$interface_input")
    
    echo -e "${BLUE}=== HTTPS流量抓包工具 ===${NC}"
    echo ""
    echo "抓包时间: $(date)"
    echo "抓包主机: $(hostname)"
    echo ""
    
    # 执行抓包步骤
    check_permissions
    check_dependencies
    check_injection_status
    create_output_dir
    prepare_keylog_file
    start_capture "$duration" "$interface" "$filter"
    collect_ssl_keys
    verify_capture_files
    generate_report "$duration" "$interface" "$filter"
    package_files
    
    log_success "HTTPS流量抓包任务完成！"
    
    exit 0
}

# 运行主函数
main "$@"
