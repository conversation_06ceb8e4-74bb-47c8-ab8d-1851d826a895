#!/bin/bash

# 便携式SSL密钥提取器打包脚本
# 将4个独立脚本和依赖文件打包成可移植的部署包
# 
# 使用方法:
#   ./package_portable_scripts.sh

set -e

# 配置
PACKAGE_NAME="ssl_keylog_portable"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
PACKAGE_DIR="${PACKAGE_NAME}_${TIMESTAMP}"
PACKAGE_FILE="${PACKAGE_DIR}.tar.gz"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查必需文件
check_required_files() {
    log_step "检查必需文件..."
    
    local required_files=(
        "1_check_compatibility.sh"
        "2_setup_injection.sh"
        "3_capture_traffic.sh"
        "4_cleanup_injection.sh"
        "src/keylog_injector.c"
        "Makefile"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少必需文件:"
        for file in "${missing_files[@]}"; do
            echo "  • $file"
        done
        exit 1
    fi
    
    log_success "必需文件检查通过"
}

# 检查预编译库
check_precompiled_lib() {
    log_step "检查预编译库..."
    
    if [[ -f "libkeylog_injector.so" ]]; then
        if file libkeylog_injector.so | grep -q "ELF 64-bit"; then
            log_success "预编译库存在且格式正确"
            return 0
        else
            log_warn "预编译库格式不正确"
            return 1
        fi
    else
        log_warn "预编译库不存在"
        return 1
    fi
}

# 创建打包目录
create_package_dir() {
    log_step "创建打包目录..."
    
    rm -rf "$PACKAGE_DIR" 2>/dev/null || true
    mkdir -p "$PACKAGE_DIR"
    mkdir -p "$PACKAGE_DIR/src"
    mkdir -p "$PACKAGE_DIR/docs"
    mkdir -p "$PACKAGE_DIR/examples"
    
    log_success "打包目录创建完成: $PACKAGE_DIR"
}

# 复制核心脚本
copy_core_scripts() {
    log_step "复制核心脚本..."
    
    local scripts=(
        "1_check_compatibility.sh"
        "2_setup_injection.sh"
        "3_capture_traffic.sh"
        "4_cleanup_injection.sh"
    )
    
    for script in "${scripts[@]}"; do
        cp "$script" "$PACKAGE_DIR/"
        chmod +x "$PACKAGE_DIR/$script"
        log_info "复制脚本: $script"
    done
    
    log_success "核心脚本复制完成"
}

# 复制源码和编译文件
copy_source_files() {
    log_step "复制源码和编译文件..."
    
    # 复制源码
    cp src/keylog_injector.c "$PACKAGE_DIR/src/"
    cp Makefile "$PACKAGE_DIR/"
    
    # 复制预编译库（如果存在）
    if [[ -f "libkeylog_injector.so" ]]; then
        cp libkeylog_injector.so "$PACKAGE_DIR/"
        log_info "复制预编译库: libkeylog_injector.so"
    fi
    
    log_success "源码和编译文件复制完成"
}

# 复制文档文件
copy_documentation() {
    log_step "复制文档文件..."
    
    # 复制现有文档
    if [[ -f "README.md" ]]; then
        cp README.md "$PACKAGE_DIR/docs/"
    fi
    
    if [[ -f "LICENSE" ]]; then
        cp LICENSE "$PACKAGE_DIR/docs/"
    fi
    
    log_success "文档文件复制完成"
}

# 创建主部署脚本
create_main_deploy_script() {
    log_step "创建主部署脚本..."
    
    cat > "$PACKAGE_DIR/deploy.sh" << 'EOF'
#!/bin/bash

# SSL密钥提取器便携式部署脚本
# 自动检查兼容性、编译（如需要）、并提供使用指导

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 检查权限
if [[ $EUID -ne 0 ]]; then
    log_error "需要root权限运行"
    echo "请使用: sudo $0"
    exit 1
fi

echo -e "${BLUE}=== SSL密钥提取器便携式部署 ===${NC}"
echo ""

# 1. 兼容性检查（生产环境模式）
log_step "步骤1: 系统兼容性检查（生产环境模式）"
if ./1_check_compatibility.sh --production; then
    log_info "兼容性检查通过"
else
    exit_code=$?
    if [[ $exit_code -eq 1 ]]; then
        log_warn "兼容性检查有警告，但可以继续"
    else
        log_error "兼容性检查失败，请解决问题后重试"
        exit 1
    fi
fi

echo ""

# 2. 编译检查
log_step "步骤2: 检查SSL注入器库"
if [[ ! -f "libkeylog_injector.so" ]]; then
    log_warn "预编译库不存在，开始编译..."
    if make clean && make; then
        log_info "编译成功"
    else
        log_error "编译失败，请检查开发环境"
        exit 1
    fi
else
    if ldd libkeylog_injector.so &>/dev/null; then
        log_info "预编译库兼容，跳过编译"
    else
        log_warn "预编译库不兼容，重新编译..."
        if make clean && make; then
            log_info "重新编译成功"
        else
            log_error "重新编译失败"
            exit 1
        fi
    fi
fi

echo ""
log_info "部署完成！"
echo ""
echo -e "${GREEN}使用方法:${NC}"
echo "1. 设置SSL注入:     ./2_setup_injection.sh"
echo "2. 重启HTTPS服务:   systemctl restart nginx"
echo "3. 开始抓包:       ./3_capture_traffic.sh [时间]"
echo "4. 清理注入:       ./4_cleanup_injection.sh"
echo ""
echo -e "${YELLOW}注意事项:${NC}"
echo "• 步骤2和3之间必须重启HTTPS服务"
echo "• 抓包完成后建议运行清理脚本"
echo "• 所有操作都需要root权限"
EOF
    
    chmod +x "$PACKAGE_DIR/deploy.sh"
    log_success "主部署脚本创建完成"
}

# 创建使用示例
create_usage_examples() {
    log_step "创建使用示例..."
    
    # 创建快速开始示例
    cat > "$PACKAGE_DIR/examples/quick_start.sh" << 'EOF'
#!/bin/bash

# 快速开始示例 - 抓包nginx流量60秒

echo "=== SSL密钥提取器快速开始 ==="
echo ""

# 检查权限
if [[ $EUID -ne 0 ]]; then
    echo "需要root权限，请使用: sudo $0"
    exit 1
fi

# 进入脚本目录
cd "$(dirname "$0")/.."

echo "1. 检查系统兼容性..."
./1_check_compatibility.sh || exit 1

echo ""
echo "2. 设置SSL注入..."
./2_setup_injection.sh || exit 1

echo ""
echo "3. 重启nginx服务..."
if systemctl is-active nginx &>/dev/null; then
    systemctl restart nginx
    echo "nginx已重启"
else
    echo "nginx未运行，请手动启动或重启其他HTTPS服务"
fi

echo ""
echo "4. 开始抓包（60秒）..."
./3_capture_traffic.sh 60

echo ""
echo "5. 清理SSL注入..."
./4_cleanup_injection.sh -y

echo ""
echo "完成！请查看生成的抓包文件。"
EOF
    
    chmod +x "$PACKAGE_DIR/examples/quick_start.sh"
    
    # 创建nginx专用示例
    cat > "$PACKAGE_DIR/examples/nginx_capture.sh" << 'EOF'
#!/bin/bash

# Nginx HTTPS流量抓包示例

DURATION=${1:-120}  # 默认抓包2分钟

echo "=== Nginx HTTPS流量抓包 ==="
echo "抓包时间: ${DURATION}秒"
echo ""

# 检查权限
if [[ $EUID -ne 0 ]]; then
    echo "需要root权限，请使用: sudo $0 [时间]"
    exit 1
fi

cd "$(dirname "$0")/.."

# 设置注入
echo "设置SSL密钥注入..."
./2_setup_injection.sh || exit 1

echo ""
echo "重启nginx..."
systemctl restart nginx

echo ""
echo "等待5秒让服务完全启动..."
sleep 5

echo ""
echo "开始抓包..."
./3_capture_traffic.sh "$DURATION"

echo ""
echo "清理注入设置..."
./4_cleanup_injection.sh -y

echo "Nginx抓包完成！"
EOF
    
    chmod +x "$PACKAGE_DIR/examples/nginx_capture.sh"
    
    log_success "使用示例创建完成"
}

# 创建便携式README
create_portable_readme() {
    log_step "创建便携式README..."
    
    cat > "$PACKAGE_DIR/README.md" << EOF
# SSL密钥提取器便携式版本

这是一个便携式的SSL密钥提取器工具包，可以在任何兼容的Linux系统上部署和使用。

## 🚀 快速开始

### 1. 自动部署
\`\`\`bash
sudo ./deploy.sh
\`\`\`

### 2. 手动步骤
\`\`\`bash
# 检查系统兼容性（生产环境模式）
sudo ./1_check_compatibility.sh

# 设置SSL密钥注入
sudo ./2_setup_injection.sh

# 重启HTTPS服务（重要！）
sudo systemctl restart nginx

# 开始抓包（60秒）
sudo ./3_capture_traffic.sh 60

# 清理注入设置
sudo ./4_cleanup_injection.sh
\`\`\`

### 3. 开发环境模式（可选）
\`\`\`bash
# 检查系统兼容性（包含编译环境检查）
sudo ./1_check_compatibility.sh --compile
\`\`\`

## 📁 文件说明

### 核心脚本
- **1_check_compatibility.sh** - 系统兼容性检查
- **2_setup_injection.sh** - 设置SSL密钥注入
- **3_capture_traffic.sh** - 执行流量抓包
- **4_cleanup_injection.sh** - 清理注入设置

### 辅助文件
- **deploy.sh** - 自动部署脚本
- **src/keylog_injector.c** - SSL注入器源码
- **Makefile** - 编译配置
- **libkeylog_injector.so** - 预编译库（如果存在）

### 示例脚本
- **examples/quick_start.sh** - 快速开始示例
- **examples/nginx_capture.sh** - Nginx专用抓包示例

## ⚠️ 重要注意事项

1. **权限要求**: 所有操作都需要root权限
2. **服务重启**: 在步骤2之后必须重启HTTPS服务
3. **清理建议**: 抓包完成后建议运行清理脚本
4. **兼容性**: 仅支持使用OpenSSL的应用程序

## 🔧 系统要求

- Linux操作系统（x86_64）
- root权限
- tcpdump工具
- gcc编译器（如果需要编译）
- OpenSSL开发库（如果需要编译）

## 📋 支持的应用

✅ **完全支持**:
- Nginx
- Apache HTTP Server
- 使用OpenSSL的自定义应用

⚠️ **可能不支持**:
- Java应用（使用Java SSL）
- Node.js应用（使用BoringSSL）
- Go应用（使用Go crypto/tls）

## 🛠️ 故障排除

### 编译失败
\`\`\`bash
# 安装开发工具
yum install -y gcc make openssl-devel  # CentOS/RHEL
apt install -y gcc make libssl-dev     # Ubuntu/Debian
\`\`\`

### 无SSL密钥记录
1. 确认服务已重启
2. 确认有HTTPS流量
3. 确认应用使用OpenSSL

### 权限问题
\`\`\`bash
# 确保使用root权限
sudo ./deploy.sh
\`\`\`

## 📦 打包信息

- **打包时间**: $(date)
- **打包主机**: $(hostname)
- **系统信息**: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")

## 📞 技术支持

如遇问题，请检查：
1. 系统兼容性检查结果
2. 编译错误信息
3. 服务重启状态
4. 网络流量情况

---
**注意**: 此工具仅用于合法的网络调试和安全测试目的。
EOF
    
    log_success "便携式README创建完成"
}

# 记录构建信息
record_build_info() {
    log_step "记录构建信息..."
    
    cat > "$PACKAGE_DIR/BUILD_INFO.txt" << EOF
SSL密钥提取器便携式版本构建信息
================================

构建时间: $(date)
构建主机: $(hostname)
构建用户: $(whoami)

系统信息:
--------
操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")
内核版本: $(uname -r)
架构: $(uname -m)

编译器信息:
----------
$(gcc --version 2>/dev/null | head -1 || echo "GCC: 未安装")

预编译库信息:
-----------
$(if [[ -f "libkeylog_injector.so" ]]; then
    echo "状态: 已包含"
    file libkeylog_injector.so
    echo ""
    echo "依赖库:"
    ldd libkeylog_injector.so 2>/dev/null || echo "无法获取依赖信息"
else
    echo "状态: 未包含，需要在目标系统编译"
fi)

OpenSSL版本:
-----------
$(openssl version 2>/dev/null || echo "OpenSSL: 未安装")

包含文件:
--------
$(find "$PACKAGE_DIR" -type f | sort | sed 's|^'"$PACKAGE_DIR"'/|  • |')

兼容性说明:
----------
- 预编译库仅适用于相同或兼容的系统环境
- 如果预编译库不兼容，会自动触发重新编译
- 源码编译需要gcc和OpenSSL开发库

使用建议:
--------
1. 优先在目标系统上运行兼容性检查
2. 如果预编译库不兼容，确保安装编译环境
3. 测试环境验证后再在生产环境使用
EOF
    
    log_success "构建信息记录完成"
}

# 设置文件权限
set_file_permissions() {
    log_step "设置文件权限..."
    
    # 设置脚本执行权限
    find "$PACKAGE_DIR" -name "*.sh" -exec chmod +x {} \;
    
    # 设置源码文件权限
    find "$PACKAGE_DIR" -name "*.c" -exec chmod 644 {} \;
    find "$PACKAGE_DIR" -name "*.h" -exec chmod 644 {} \;
    find "$PACKAGE_DIR" -name "Makefile" -exec chmod 644 {} \;
    
    # 设置文档权限
    find "$PACKAGE_DIR/docs" -type f -exec chmod 644 {} \; 2>/dev/null || true
    
    # 设置预编译库权限
    if [[ -f "$PACKAGE_DIR/libkeylog_injector.so" ]]; then
        chmod 755 "$PACKAGE_DIR/libkeylog_injector.so"
    fi
    
    log_success "文件权限设置完成"
}

# 打包文件
package_files() {
    log_step "打包文件..."
    
    tar -czf "$PACKAGE_FILE" "$PACKAGE_DIR"
    
    local size=$(ls -lh "$PACKAGE_FILE" | awk '{print $5}')
    local md5_hash=$(md5sum "$PACKAGE_FILE" 2>/dev/null | awk '{print $1}' || echo "N/A")
    
    log_success "打包完成: $PACKAGE_FILE"
    log_info "文件大小: $size"
    log_info "MD5校验: $md5_hash"
}

# 显示部署说明
show_deployment_info() {
    echo ""
    echo -e "${BLUE}=== 便携式SSL密钥提取器打包完成 ===${NC}"
    echo ""
    echo -e "${GREEN}打包信息:${NC}"
    echo -e "  文件名: ${CYAN}$PACKAGE_FILE${NC}"
    echo -e "  大小: ${CYAN}$(ls -lh "$PACKAGE_FILE" | awk '{print $5}')${NC}"
    echo -e "  MD5: ${CYAN}$(md5sum "$PACKAGE_FILE" 2>/dev/null | awk '{print $1}' || echo "N/A")${NC}"
    echo ""
    echo -e "${GREEN}部署步骤:${NC}"
    echo -e "  1. 传输: ${CYAN}scp $PACKAGE_FILE user@target:/tmp/${NC}"
    echo -e "  2. 解压: ${CYAN}tar -xzf $PACKAGE_FILE${NC}"
    echo -e "  3. 进入: ${CYAN}cd $PACKAGE_DIR${NC}"
    echo -e "  4. 部署: ${CYAN}sudo ./deploy.sh${NC}"
    echo ""
    echo -e "${GREEN}快速使用:${NC}"
    echo -e "  • 自动化: ${CYAN}sudo ./examples/quick_start.sh${NC}"
    echo -e "  • Nginx: ${CYAN}sudo ./examples/nginx_capture.sh [时间]${NC}"
    echo ""
    echo -e "${YELLOW}重要提醒:${NC}"
    echo "• 所有操作都需要root权限"
    echo "• 设置注入后必须重启HTTPS服务"
    echo "• 抓包完成后建议运行清理脚本"
    echo "• 仅支持使用OpenSSL的应用程序"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}便携式SSL密钥提取器打包脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo ""
    echo "功能:"
    echo "• 打包4个独立的操作脚本"
    echo "• 包含源码和预编译库"
    echo "• 创建自动部署脚本"
    echo "• 生成使用示例和文档"
    echo "• 记录构建和兼容性信息"
    echo ""
    echo "输出:"
    echo "• 便携式部署包（tar.gz格式）"
    echo "• 包含完整的使用文档"
    echo "• 支持离线部署"
}

# 主函数
main() {
    # 处理命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        "")
            # 继续执行打包
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
    
    echo -e "${BLUE}=== 创建便携式SSL密钥提取器部署包 ===${NC}"
    echo ""
    
    # 执行打包步骤
    check_required_files
    check_precompiled_lib
    create_package_dir
    copy_core_scripts
    copy_source_files
    copy_documentation
    create_main_deploy_script
    create_usage_examples
    create_portable_readme
    record_build_info
    set_file_permissions
    package_files
    show_deployment_info
    
    # 清理临时目录
    rm -rf "$PACKAGE_DIR"
    
    echo ""
    log_success "便携式部署包创建完成！"
}

# 运行主函数
main "$@"
