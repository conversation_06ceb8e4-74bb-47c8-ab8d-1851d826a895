#!/bin/bash

# SSL密钥注入清理脚本
# 清理LD_PRELOAD设置、配置文件和临时文件，恢复系统到注入前状态
# 
# 使用方法:
#   ./4_cleanup_injection.sh

set -e

# 配置文件路径
CONFIG_FILE="/etc/ssl-keylog.conf"
LD_SO_PRELOAD="/etc/ld.so.preload"
GLOBAL_KEYLOG="/tmp/global_ssl_keylog.txt"
DETAILED_LOG="/tmp/keylog_injector.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要root权限运行清理脚本"
        echo "请使用: sudo $0"
        exit 1
    fi
    log_info "权限检查通过"
}

# 检查当前注入状态
check_injection_status() {
    log_step "检查当前SSL注入状态..."
    
    local injection_active=0
    
    # 检查LD_PRELOAD
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD" 2>/dev/null; then
        log_info "检测到活跃的LD_PRELOAD注入"
        injection_active=1
    fi
    
    # 检查配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "检测到SSL注入配置文件"
        injection_active=1
    fi
    
    # 检查全局密钥文件
    if [[ -f "$GLOBAL_KEYLOG" ]]; then
        local key_count=$(wc -l < "$GLOBAL_KEYLOG" 2>/dev/null || echo 0)
        log_info "检测到全局SSL密钥文件 ($key_count 条记录)"
        injection_active=1
    fi
    
    if [[ $injection_active -eq 0 ]]; then
        log_warn "未检测到活跃的SSL注入，可能已经清理过"
        return 1
    fi
    
    log_success "检测到活跃的SSL注入，需要清理"
    return 0
}

# 备份密钥文件
backup_keylog_file() {
    log_step "备份SSL密钥文件..."
    
    if [[ -f "$GLOBAL_KEYLOG" ]] && [[ -s "$GLOBAL_KEYLOG" ]]; then
        local backup_file="${GLOBAL_KEYLOG}.backup.$(date +%s)"
        cp "$GLOBAL_KEYLOG" "$backup_file"
        chmod 600 "$backup_file"
        
        local key_count=$(wc -l < "$GLOBAL_KEYLOG" 2>/dev/null || echo 0)
        log_success "SSL密钥文件已备份: $backup_file ($key_count 条记录)"
        
        # 显示备份文件信息
        echo ""
        log_info "备份文件信息:"
        echo "  • 文件路径: $backup_file"
        echo "  • 密钥数量: $key_count"
        echo "  • 文件大小: $(ls -lh "$backup_file" | awk '{print $5}')"
        echo ""
        
        return 0
    else
        log_warn "全局SSL密钥文件为空或不存在，跳过备份"
        return 1
    fi
}

# 清理LD_PRELOAD设置
cleanup_ld_preload() {
    log_step "清理LD_PRELOAD设置..."
    
    if [[ ! -f "$LD_SO_PRELOAD" ]]; then
        log_info "LD_PRELOAD文件不存在，跳过清理"
        return 0
    fi
    
    # 检查是否包含SSL注入器
    if ! grep -q "libkeylog_injector" "$LD_SO_PRELOAD" 2>/dev/null; then
        log_info "LD_PRELOAD中未找到SSL注入器，跳过清理"
        return 0
    fi
    
    # 备份原文件
    local backup_file="${LD_SO_PRELOAD}.cleanup_backup.$(date +%s)"
    cp "$LD_SO_PRELOAD" "$backup_file"
    log_info "已备份LD_PRELOAD文件: $backup_file"
    
    # 移除SSL注入器相关行
    sed -i '/libkeylog_injector/d' "$LD_SO_PRELOAD"
    
    # 如果文件为空或只包含空行，删除文件
    if [[ ! -s "$LD_SO_PRELOAD" ]] || ! grep -q '[^[:space:]]' "$LD_SO_PRELOAD" 2>/dev/null; then
        rm -f "$LD_SO_PRELOAD"
        log_success "LD_PRELOAD文件已删除（文件为空）"
    else
        log_success "已从LD_PRELOAD中移除SSL注入器"
    fi
}

# 清理配置文件
cleanup_config_file() {
    log_step "清理SSL注入配置文件..."
    
    if [[ -f "$CONFIG_FILE" ]]; then
        # 备份配置文件
        local backup_file="${CONFIG_FILE}.cleanup_backup.$(date +%s)"
        cp "$CONFIG_FILE" "$backup_file"
        log_info "已备份配置文件: $backup_file"
        
        # 删除配置文件
        rm -f "$CONFIG_FILE"
        log_success "SSL注入配置文件已删除: $CONFIG_FILE"
    else
        log_info "SSL注入配置文件不存在，跳过清理"
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_step "清理临时文件..."
    
    local cleaned_files=0
    
    # 清理全局SSL密钥文件
    if [[ -f "$GLOBAL_KEYLOG" ]]; then
        rm -f "$GLOBAL_KEYLOG"
        log_success "已删除全局SSL密钥文件: $GLOBAL_KEYLOG"
        cleaned_files=$((cleaned_files + 1))
    fi
    
    # 清理详细日志文件
    if [[ -f "$DETAILED_LOG" ]]; then
        rm -f "$DETAILED_LOG"
        log_success "已删除详细日志文件: $DETAILED_LOG"
        cleaned_files=$((cleaned_files + 1))
    fi
    
    # 清理其他可能的临时文件
    local temp_patterns=(
        "/tmp/*ssl_keylog*.txt"
        "/tmp/*keylog_injector*.log"
        "/tmp/injected_keylog*.txt"
    )
    
    for pattern in "${temp_patterns[@]}"; do
        for file in $pattern; do
            if [[ -f "$file" ]]; then
                rm -f "$file"
                log_success "已删除临时文件: $file"
                cleaned_files=$((cleaned_files + 1))
            fi
        done
    done
    
    if [[ $cleaned_files -eq 0 ]]; then
        log_info "未找到需要清理的临时文件"
    else
        log_success "共清理了 $cleaned_files 个临时文件"
    fi
}

# 验证清理结果
verify_cleanup() {
    log_step "验证清理结果..."
    
    local cleanup_issues=0
    
    # 检查LD_PRELOAD
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD" 2>/dev/null; then
        log_error "✗ LD_PRELOAD中仍存在SSL注入器"
        cleanup_issues=$((cleanup_issues + 1))
    else
        log_success "✓ LD_PRELOAD清理完成"
    fi
    
    # 检查配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        log_error "✗ SSL注入配置文件仍然存在"
        cleanup_issues=$((cleanup_issues + 1))
    else
        log_success "✓ SSL注入配置文件清理完成"
    fi
    
    # 检查全局密钥文件
    if [[ -f "$GLOBAL_KEYLOG" ]]; then
        log_error "✗ 全局SSL密钥文件仍然存在"
        cleanup_issues=$((cleanup_issues + 1))
    else
        log_success "✓ 全局SSL密钥文件清理完成"
    fi
    
    if [[ $cleanup_issues -gt 0 ]]; then
        log_error "清理验证失败，发现 $cleanup_issues 个问题"
        return 1
    else
        log_success "清理验证通过，所有注入组件已移除"
        return 0
    fi
}

# 显示清理摘要
show_cleanup_summary() {
    echo ""
    echo -e "${BLUE}=== SSL密钥注入清理完成 ===${NC}"
    echo ""
    echo -e "${GREEN}清理摘要:${NC}"
    echo "  • LD_PRELOAD设置: 已清理"
    echo "  • SSL注入配置文件: 已删除"
    echo "  • 全局SSL密钥文件: 已删除"
    echo "  • 临时文件: 已清理"
    echo ""
    
    # 显示备份文件信息
    echo -e "${CYAN}备份文件:${NC}"
    local backup_files=(
        "${LD_SO_PRELOAD}.cleanup_backup."*
        "${CONFIG_FILE}.cleanup_backup."*
        "${GLOBAL_KEYLOG}.backup."*
    )
    
    local backup_found=0
    for pattern in "${backup_files[@]}"; do
        for file in $pattern; do
            if [[ -f "$file" ]]; then
                echo "  • $(basename "$file"): $file"
                backup_found=1
            fi
        done
    done
    
    if [[ $backup_found -eq 0 ]]; then
        echo "  • 无备份文件"
    fi
    
    echo ""
    echo -e "${YELLOW}重要提醒:${NC}"
    echo "• SSL密钥注入已完全移除"
    echo "• 新启动的应用将不再被监控"
    echo "• 已运行的服务可能需要重启以完全移除注入"
    echo "• 备份文件已保留，可手动删除"
    echo ""
    echo -e "${CYAN}后续操作建议:${NC}"
    echo "1. 重启相关HTTPS服务以确保完全清理:"
    echo "   systemctl restart nginx"
    echo "   systemctl restart apache2"
    echo ""
    echo "2. 如需重新启用SSL注入:"
    echo "   ./2_setup_injection.sh"
    echo ""
    echo "3. 清理备份文件（可选）:"
    echo "   rm -f /etc/ld.so.preload.cleanup_backup.*"
    echo "   rm -f /etc/ssl-keylog.conf.cleanup_backup.*"
    echo "   rm -f /tmp/global_ssl_keylog.txt.backup.*"
}

# 强制清理模式
force_cleanup() {
    log_warn "执行强制清理模式..."
    
    # 强制删除所有相关文件
    rm -f "$LD_SO_PRELOAD" 2>/dev/null || true
    rm -f "$CONFIG_FILE" 2>/dev/null || true
    rm -f "$GLOBAL_KEYLOG" 2>/dev/null || true
    rm -f "$DETAILED_LOG" 2>/dev/null || true
    
    # 清理所有可能的临时文件
    rm -f /tmp/*ssl_keylog*.txt 2>/dev/null || true
    rm -f /tmp/*keylog_injector*.log 2>/dev/null || true
    rm -f /tmp/injected_keylog*.txt 2>/dev/null || true
    
    log_success "强制清理完成"
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}SSL密钥注入清理脚本${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -f, --force    强制清理模式（不备份，直接删除所有文件）"
    echo "  -y, --yes      自动确认所有操作"
    echo ""
    echo "功能:"
    echo "• 清理全局LD_PRELOAD设置"
    echo "• 删除SSL注入配置文件"
    echo "• 清理临时SSL密钥文件"
    echo "• 备份重要文件"
    echo "• 验证清理结果"
    echo ""
    echo "注意事项:"
    echo "• 需要root权限运行"
    echo "• 会备份重要文件后再删除"
    echo "• 建议重启相关服务以确保完全清理"
    echo "• 强制模式不会创建备份文件"
}

# 主函数
main() {
    local force_mode=0
    local auto_confirm=0
    
    # 处理命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force_mode=1
                shift
                ;;
            -y|--yes)
                auto_confirm=1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${BLUE}=== SSL密钥注入清理工具 ===${NC}"
    echo ""
    echo "清理时间: $(date)"
    echo "清理主机: $(hostname)"
    echo ""
    
    # 检查权限
    check_permissions
    
    # 检查注入状态
    if ! check_injection_status; then
        if [[ $force_mode -eq 1 ]]; then
            log_warn "强制模式：即使未检测到注入也执行清理"
        else
            echo ""
            log_info "系统已经是干净状态，无需清理"
            exit 0
        fi
    fi
    
    # 确认清理操作
    if [[ $auto_confirm -eq 0 ]] && [[ $force_mode -eq 0 ]]; then
        echo ""
        log_warn "即将清理SSL密钥注入设置，此操作将："
        echo "  • 移除全局LD_PRELOAD中的SSL注入器"
        echo "  • 删除SSL注入配置文件"
        echo "  • 清理临时SSL密钥文件"
        echo "  • 备份重要文件"
        echo ""
        echo -n "确认继续？(y/N): "
        read -r confirm
        if [[ "$confirm" != "y" ]] && [[ "$confirm" != "Y" ]]; then
            log_info "用户取消操作"
            exit 0
        fi
    fi
    
    echo ""
    
    # 执行清理
    if [[ $force_mode -eq 1 ]]; then
        force_cleanup
    else
        # 正常清理流程
        backup_keylog_file
        cleanup_ld_preload
        cleanup_config_file
        cleanup_temp_files
        
        # 验证清理结果
        if verify_cleanup; then
            show_cleanup_summary
        else
            log_error "清理过程中发现问题，请检查上述错误信息"
            exit 1
        fi
    fi
    
    echo ""
    log_success "SSL密钥注入清理完成！"
    
    exit 0
}

# 运行主函数
main "$@"
