#!/bin/bash

# 部署验证脚本 - 验证工具包完整性和功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 检查文件完整性
check_files() {
    log_step "检查文件完整性..."
    
    local required_files=(
        "portable_capture.sh"
        "quick_deploy.sh"
        "create_portable_package.sh"
        "setup_global_injection.sh"
        "src/keylog_injector.c"
        "Makefile"
        "libkeylog_injector.so"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "文件存在: $file"
        else
            log_error "文件缺失: $file"
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少 ${#missing_files[@]} 个必要文件"
        return 1
    fi
    
    log_info "所有必要文件完整"
}

# 检查脚本权限
check_permissions() {
    log_step "检查脚本权限..."
    
    local scripts=(
        "portable_capture.sh"
        "quick_deploy.sh"
        "create_portable_package.sh"
        "setup_global_injection.sh"
        "verify_deployment.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ -x "$script" ]]; then
            log_info "可执行: $script"
        else
            log_warn "设置执行权限: $script"
            chmod +x "$script"
        fi
    done
}

# 检查编译产物
check_compilation() {
    log_step "检查编译产物..."
    
    if [[ -f "libkeylog_injector.so" ]]; then
        log_info "SSL注入器库存在"
        
        # 检查库文件信息
        local lib_info=$(file libkeylog_injector.so)
        log_info "库文件信息: $lib_info"
        
        # 检查符号
        if nm libkeylog_injector.so | grep -q "SSL_CTX_new"; then
            log_info "包含必要的OpenSSL符号"
        else
            log_warn "可能缺少OpenSSL符号"
        fi
    else
        log_error "SSL注入器库不存在，需要编译"
        return 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local deps=(
        "gcc:编译器"
        "make:构建工具"
        "tcpdump:网络抓包"
    )
    
    for dep_info in "${deps[@]}"; do
        local cmd=$(echo "$dep_info" | cut -d: -f1)
        local desc=$(echo "$dep_info" | cut -d: -f2)
        
        if command -v "$cmd" &> /dev/null; then
            log_info "$desc 已安装: $cmd"
        else
            log_warn "$desc 未安装: $cmd"
        fi
    done
    
    # 检查OpenSSL开发库
    if [[ -f "/usr/include/openssl/ssl.h" ]] || [[ -f "/usr/local/include/openssl/ssl.h" ]]; then
        log_info "OpenSSL开发库已安装"
    else
        log_warn "OpenSSL开发库可能未安装"
    fi
}

# 功能测试
test_functionality() {
    log_step "进行功能测试..."
    
    # 检查是否为root
    if [[ $EUID -ne 0 ]]; then
        log_warn "需要root权限进行完整功能测试"
        return 0
    fi
    
    # 测试SSL注入器加载
    log_info "测试SSL注入器加载..."
    if LD_PRELOAD=./libkeylog_injector.so /bin/true 2>/dev/null; then
        log_info "SSL注入器可以正常加载"
    else
        log_error "SSL注入器加载失败"
        return 1
    fi
    
    # 测试抓包工具语法
    log_info "测试抓包工具语法..."
    if ./portable_capture.sh --help &>/dev/null; then
        log_info "抓包工具语法正确"
    else
        log_warn "抓包工具可能有语法问题"
    fi
}

# 生成测试报告
generate_report() {
    log_step "生成验证报告..."
    
    local report_file="deployment_verification_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
HTTPS流量抓包工具 - 部署验证报告
=====================================

验证时间: $(date)
系统信息: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2 2>/dev/null || echo "未知")
内核版本: $(uname -r)

文件检查:
--------
$(ls -la *.sh libkeylog_injector.so 2>/dev/null || echo "部分文件缺失")

编译信息:
--------
$(file libkeylog_injector.so 2>/dev/null || echo "库文件不存在")

系统依赖:
--------
GCC: $(gcc --version 2>/dev/null | head -1 || echo "未安装")
Make: $(make --version 2>/dev/null | head -1 || echo "未安装")
Tcpdump: $(tcpdump --version 2>&1 | head -1 || echo "未安装")

网络接口:
--------
$(ip addr show | grep -E "^[0-9]+:" | awk '{print $2}' | tr -d ':' | tr '\n' ' ')

部署包文件:
----------
$(ls -la https_capture_toolkit_*.tar.gz 2>/dev/null || echo "未找到部署包")

验证状态: 
--------
✓ 工具已准备就绪，可以进行现场部署
EOF
    
    log_info "验证报告已生成: $report_file"
}

# 显示使用建议
show_recommendations() {
    echo ""
    echo -e "${BLUE}=== 部署建议 ===${NC}"
    echo ""
    echo -e "${GREEN}现场部署步骤:${NC}"
    echo "1. 将部署包上传到客户服务器"
    echo "2. 解压并运行 quick_deploy.sh"
    echo "3. 使用 portable_capture.sh 进行抓包"
    echo "4. 下载结果文件进行分析"
    echo ""
    echo -e "${GREEN}最佳实践:${NC}"
    echo "• 抓包时间建议1-5分钟"
    echo "• 确保抓包期间有HTTPS流量"
    echo "• 优先测试已知使用OpenSSL的应用"
    echo "• 保留原始PCAP文件作为备份"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "• 需要root权限运行"
    echo "• 会临时修改系统LD_PRELOAD配置"
    echo "• 抓包完成后会自动清理环境"
    echo "• 密钥文件包含敏感信息，注意保护"
}

# 主函数
main() {
    echo -e "${BLUE}=== HTTPS流量抓包工具 - 部署验证 ===${NC}"
    echo ""
    
    local success=true
    
    check_files || success=false
    check_permissions
    check_compilation || success=false
    check_dependencies
    test_functionality || success=false
    generate_report
    
    echo ""
    if [[ "$success" == "true" ]]; then
        echo -e "${GREEN}=== 验证通过 ===${NC}"
        echo -e "${GREEN}工具已准备就绪，可以进行客户现场部署！${NC}"
    else
        echo -e "${RED}=== 验证失败 ===${NC}"
        echo -e "${RED}请解决上述问题后重新验证${NC}"
    fi
    
    show_recommendations
}

# 运行主函数
main "$@"
