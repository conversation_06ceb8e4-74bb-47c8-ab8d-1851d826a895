#!/bin/bash

# SSL密钥提取器全局注入设置脚本
# 实现对所有应用的透明SSL密钥提取，无需修改启动参数

set -e

# 配置
KEYLOG_LIB="$(pwd)/libkeylog_injector.so"
KEYLOG_FILE="/tmp/global_ssl_keylog.txt"
CONFIG_FILE="/etc/ssl-keylog.conf"
LD_SO_PRELOAD="/etc/ld.so.preload"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        log_error "需要root权限运行全局注入设置"
        exit 1
    fi
}

# 检查SSL注入器库
check_ssl_lib() {
    if [[ ! -f "$KEYLOG_LIB" ]]; then
        log_error "SSL注入器库不存在: $KEYLOG_LIB"
        log_info "请先编译: make"
        exit 1
    fi
    log_info "SSL注入器库检查通过: $KEYLOG_LIB"
}

# 设置全局LD_PRELOAD
setup_global_preload() {
    log_info "设置全局LD_PRELOAD..."
    
    # 备份原文件（如果存在）
    if [[ -f "$LD_SO_PRELOAD" ]]; then
        cp "$LD_SO_PRELOAD" "${LD_SO_PRELOAD}.backup.$(date +%s)" 2>/dev/null || true
        log_info "已备份原有 $LD_SO_PRELOAD"
    fi
    
    # 检查是否已经添加
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD" 2>/dev/null; then
        log_warn "SSL注入器已在全局LD_PRELOAD中，跳过添加"
    else
        # 添加SSL注入器到全局预加载
        echo "$KEYLOG_LIB" >> "$LD_SO_PRELOAD"
        log_info "✅ 已添加到全局LD_PRELOAD: $LD_SO_PRELOAD"
    fi
}

# 创建配置文件
create_config_file() {
    log_info "创建SSL密钥提取器配置文件..."
    
    cat > "$CONFIG_FILE" << EOF
# SSL KEYLOG 全局配置文件
# 此文件被 libkeylog_injector.so 读取

# SSL密钥输出文件路径
KEYLOG_FILE=$KEYLOG_FILE

# 静默模式（1=启用，0=禁用）
KEYLOG_SILENT=1

# 详细日志（仅调试时启用，生产环境建议禁用）
KEYLOG_ENABLE_DETAILED_LOG=0
EOF
    
    chmod 644 "$CONFIG_FILE"
    log_info "✅ 配置文件已创建: $CONFIG_FILE"
}

# 创建SSL密钥文件
create_keylog_file() {
    log_info "创建SSL密钥文件..."
    
    # 创建目录
    mkdir -p "$(dirname "$KEYLOG_FILE")"
    
    # 创建文件
    touch "$KEYLOG_FILE"
    chmod 600 "$KEYLOG_FILE"
    
    log_info "✅ SSL密钥文件已创建: $KEYLOG_FILE"
}

# 启用全局注入
enable_global_injection() {
    log_info "启用SSL密钥提取器全局注入..."
    
    check_ssl_lib
    setup_global_preload
    create_config_file
    create_keylog_file
    
    log_info "✅ 全局注入设置完成！"
    echo ""
    log_info "📋 配置摘要:"
    echo "   • 注入器库: $KEYLOG_LIB"
    echo "   • 全局配置: $CONFIG_FILE"
    echo "   • SSL密钥文件: $KEYLOG_FILE"
    echo "   • 全局LD_PRELOAD: $LD_SO_PRELOAD"
    echo ""
    log_info "🚀 现在所有新启动的HTTPS应用都将自动被监控！"
    echo ""
    log_info "🧪 测试全局注入:"
    echo "   • 重启nginx: systemctl restart nginx"
    echo "   • 启动任何HTTPS应用"
    echo "   • 检查密钥文件: cat $KEYLOG_FILE"
}

# 禁用全局注入
disable_global_injection() {
    log_info "禁用SSL密钥提取器全局注入..."
    
    # 从LD_PRELOAD中移除
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD"; then
        sed -i '/libkeylog_injector/d' "$LD_SO_PRELOAD"
        
        # 如果文件为空，删除它
        if [[ ! -s "$LD_SO_PRELOAD" ]]; then
            rm -f "$LD_SO_PRELOAD"
        fi
        
        log_info "已从全局LD_PRELOAD中移除SSL注入器"
    fi
    
    # 删除配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        rm -f "$CONFIG_FILE"
        log_info "已删除配置文件: $CONFIG_FILE"
    fi
    
    log_info "✅ 全局注入已禁用"
}

# 检查全局注入状态
check_global_injection() {
    log_info "检查SSL密钥提取器全局注入状态..."
    echo ""
    
    # 检查LD_PRELOAD
    if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD"; then
        log_info "✅ 全局LD_PRELOAD: 已启用"
    else
        log_warn "❌ 全局LD_PRELOAD: 未启用"
    fi
    
    # 检查配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "✅ 配置文件: 存在"
        echo "   内容预览:"
        cat "$CONFIG_FILE" | head -5 | sed 's/^/     /'
    else
        log_warn "❌ 配置文件: 不存在"
    fi
    
    # 检查SSL密钥文件
    if [[ -f "$KEYLOG_FILE" ]]; then
        local key_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
        local file_size=$(ls -lh "$KEYLOG_FILE" | awk '{print $5}')
        log_info "✅ SSL密钥文件: 存在 ($key_count 条记录, $file_size)"
        
        if [[ $key_count -gt 0 ]]; then
            echo ""
            log_info "最新的密钥记录:"
            tail -3 "$KEYLOG_FILE" | while read line; do
                echo "     ${line:0:80}..."
            done
        fi
    else
        log_warn "❌ SSL密钥文件: 不存在"
    fi
    
    echo ""
    log_info "🔍 当前状态: $(
        if [[ -f "$LD_SO_PRELOAD" ]] && grep -q "libkeylog_injector" "$LD_SO_PRELOAD" && [[ -f "$CONFIG_FILE" ]]; then
            echo "全局注入已启用"
        else
            echo "全局注入未启用"
        fi
    )"
}

# 测试全局注入
test_global_injection() {
    log_info "测试SSL密钥提取器全局注入..."
    
    # 检查是否已启用
    if [[ ! -f "$LD_SO_PRELOAD" ]] || ! grep -q "libkeylog_injector" "$LD_SO_PRELOAD"; then
        log_error "全局注入未启用，请先运行: $0 enable"
        exit 1
    fi
    
    # 记录测试前的密钥数量
    local before_count=0
    if [[ -f "$KEYLOG_FILE" ]]; then
        before_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
    fi
    
    log_info "测试前密钥数量: $before_count"
    
    # 执行HTTPS请求
    log_info "执行HTTPS测试请求..."
    timeout 10 curl -s -o /dev/null https://httpbin.org/get 2>/dev/null || true
    
    sleep 2
    
    # 检查密钥增加
    local after_count=0
    if [[ -f "$KEYLOG_FILE" ]]; then
        after_count=$(wc -l < "$KEYLOG_FILE" 2>/dev/null || echo 0)
    fi
    
    log_info "测试后密钥数量: $after_count"
    
    if [[ $after_count -gt $before_count ]]; then
        log_info "✅ 全局注入测试成功！新增 $((after_count - before_count)) 条密钥"
        
        if [[ $((after_count - before_count)) -le 5 ]]; then
            echo ""
            log_info "新增的密钥记录:"
            tail -$((after_count - before_count)) "$KEYLOG_FILE" | while read line; do
                echo "     ${line:0:80}..."
            done
        fi
    else
        log_error "❌ 全局注入测试失败，可能的原因:"
        log_error "  1. 注入器未正确加载"
        log_error "  2. 网络连接问题"
        log_error "  3. SSL配置问题"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo "SSL密钥提取器全局注入管理工具"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  enable     启用全局注入 (所有应用自动监控)"
    echo "  disable    禁用全局注入"
    echo "  status     查看全局注入状态"
    echo "  test       测试全局注入功能"
    echo "  help       显示此帮助"
    echo ""
    echo "启用后，所有HTTPS应用都会自动被监控，无需修改启动参数！"
    echo ""
    echo "特性:"
    echo "  • 完全透明的SSL密钥提取"
    echo "  • 基于 /etc/ld.so.preload 全局注入"
    echo "  • 使用配置文件，不依赖环境变量"
    echo "  • 兼容systemd服务"
    echo "  • 支持nginx、apache、任何OpenSSL应用"
    echo ""
    echo "示例:"
    echo "  $0 enable              # 启用全局注入"
    echo "  $0 status              # 查看状态"
    echo "  $0 test                # 测试功能"
    echo "  systemctl restart nginx  # 重启nginx测试"
    echo "  cat $KEYLOG_FILE       # 查看提取的密钥"
}

# 主函数
main() {
    case "${1:-help}" in
        enable)
            check_permissions
            enable_global_injection
            ;;
        disable)
            check_permissions
            disable_global_injection
            ;;
        status)
            check_global_injection
            ;;
        test)
            test_global_injection
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@" 