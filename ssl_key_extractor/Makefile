# SSL KEYLOG 注入库 - 企业级 HTTPS 解密方案

.PHONY: all clean install uninstall test help global-enable global-disable global-status global-test

all: libkeylog_injector.so

libkeylog_injector.so: src/keylog_injector.c
	@echo "🔨 编译 SSL KEYLOG 注入库..."
	gcc -shared -fPIC -ldl -lssl -lcrypto -o libkeylog_injector.so src/keylog_injector.c
	@echo "✅ 编译完成: libkeylog_injector.so"

clean:
	@echo "🧹 清理编译产物..."
	rm -f libkeylog_injector.so
	rm -f /tmp/injected_keylog.txt
	@echo "✅ 清理完成"

install: libkeylog_injector.so
	@echo "📦 安装到系统路径..."
	sudo cp libkeylog_injector.so /usr/lib64/libkeylog_injector.so
	sudo chmod 755 /usr/lib64/libkeylog_injector.so
	@echo "✅ 安装完成: /usr/lib64/libkeylog_injector.so"
	@echo "使用: export LD_PRELOAD=/usr/lib64/libkeylog_injector.so"

uninstall:
	@echo "🗑️  卸载系统安装..."
	sudo rm -f /usr/lib64/libkeylog_injector.so
	@echo "✅ 卸载完成"

test: libkeylog_injector.so
	@echo "🧪 运行功能测试..."
	@LD_PRELOAD=./libkeylog_injector.so timeout 5 openssl s_client -connect baidu.com:443 </dev/null >/dev/null 2>&1 || true
	@echo "🔍 检查密钥文件:"
	@ls -la /tmp/injected_keylog.txt 2>/dev/null || echo "密钥文件未生成"

global-enable:
	@echo "🚀 启用全局SSL密钥提取..."
	sudo ./setup_global_injection.sh enable

global-disable:
	@echo "🛑 禁用全局SSL密钥提取..."
	sudo ./setup_global_injection.sh disable

global-status:
	@echo "📊 查看全局注入状态..."
	./setup_global_injection.sh status

global-test:
	@echo "🧪 测试全局注入功能..."
	./setup_global_injection.sh test

help:
	@echo "🔧 SSL KEYLOG 注入库构建系统"
	@echo ""
	@echo "基础功能:"
	@echo "  all       - 编译注入库"
	@echo "  clean     - 清理"
	@echo "  install   - 系统安装"
	@echo "  uninstall - 卸载"
	@echo "  test      - 功能测试"
	@echo ""
	@echo "⭐ 全局注入 (无需修改启动参数):"
	@echo "  global-enable   - 启用全局SSL密钥提取"
	@echo "  global-disable  - 禁用全局SSL密钥提取"
	@echo "  global-status   - 查看全局注入状态"
	@echo "  global-test     - 测试全局注入功能"
	@echo ""
	@echo "传统使用: LD_PRELOAD=./libkeylog_injector.so your_app"
	@echo "全局使用: make global-enable  (所有HTTPS应用自动监控)"
	@echo ""
	@echo "适用场景:"
	@echo "  • nginx/apache HTTPS服务器"
	@echo "  • 任何OpenSSL应用"
	@echo "  • 客户环境无需修改启动参数"
