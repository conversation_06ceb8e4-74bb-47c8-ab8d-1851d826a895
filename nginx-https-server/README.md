# Nginx HTTPS 测试服务器

基于 Nginx + PHP-FPM 架构的 HTTPS 测试服务器，专为 eBPF HTTPS 流量分析设计。

## 📋 功能特性

### 🌟 核心功能
- **Nginx + PHP-FPM 架构**: 高性能的现代Web服务器架构
- **HTTPS 加密通信**: 使用自签名SSL证书，支持TLS 1.2/1.3
- **10个API接口**: 涵盖基础测试、复杂数据处理、用户管理等场景
- **大数据传输**: 2个API接口提供>2KB的大数据响应
- **现代化Web界面**: 响应式设计，实时时钟，交互式API测试
- **完整日志记录**: 访问日志、错误日志、API调用日志

### 🔧 技术规格
- **端口**: 9443 (HTTPS)
- **SSL协议**: TLS 1.2, TLS 1.3
- **Web服务器**: Nginx
- **后端处理**: PHP-FPM (端口9000)
- **证书类型**: 自签名证书
- **响应格式**: JSON

## 🚀 快速开始

### 1. 启动服务
```bash
cd /root/ebpf/nginx-https-server
chmod +x *.sh
./start_nginx_https.sh
```

### 2. 访问服务
- **主页面**: https://localhost:9443
- **API测试**: https://localhost:9443/api/test

### 3. API测试
```bash
# 命令行测试所有API
./test_apis.sh

# 或者在浏览器中访问主页面进行交互式测试
```

### 4. 停止服务
```bash
./stop_nginx_https.sh
```

## 📡 API接口文档

### 1. 基础测试接口
```bash
# GET请求
curl -k "https://localhost:9443/api/test?type=get&data=test_data"

# POST请求
curl -k -X POST -H "Content-Type: application/json" \
  -d '{"test_type":"POST","data":"test data"}' \
  "https://localhost:9443/api/test"
```

### 2. 复杂数据处理
```bash
curl -k -X POST -H "Content-Type: application/json" \
  -d '{"complex_data":{"field1":"value1","nested":{"data":"test"}}}' \
  "https://localhost:9443/api/complex"
```

### 3. 用户管理接口
```bash
curl -k "https://localhost:9443/api/users"
```

### 4. 大数据接口 (>2KB)
```bash
curl -k "https://localhost:9443/api/data"
```

### 5. 系统状态接口
```bash
curl -k "https://localhost:9443/api/status"
```

### 6. 配置管理接口
```bash
curl -k "https://localhost:9443/api/config"
```

### 7. 日志接口 (>2KB)
```bash
curl -k "https://localhost:9443/api/log"
```

### 8. 监控指标接口
```bash
curl -k "https://localhost:9443/api/metrics"
```

### 9. 搜索接口
```bash
curl -k "https://localhost:9443/api/search?q=nginx+test"
```

## 📂 项目结构

```
/root/ebpf/nginx-https-server/
├── nginx.conf              # Nginx配置文件
├── server.crt              # SSL证书
├── server.key              # SSL私钥
├── start_nginx_https.sh     # 启动脚本
├── stop_nginx_https.sh      # 停止脚本
├── test_apis.sh             # API测试脚本
├── README.md                # 项目文档
├── html/
│   └── index.html           # Web主页面
└── api/
    └── api.php              # PHP API处理器
```

## 🔍 日志文件

- **访问日志**: /var/log/nginx/access.log
- **错误日志**: /var/log/nginx/error.log  
- **API日志**: /var/log/nginx/api.log

```bash
# 实时查看访问日志
tail -f /var/log/nginx/access.log

# 实时查看API日志
tail -f /var/log/nginx/api.log
```

## 🛠️ 故障排除

### 服务无法启动
1. 检查端口是否被占用：`netstat -tlnp | grep 9443`
2. 检查Nginx配置：`nginx -t -c /root/ebpf/nginx-https-server/nginx.conf`
3. 检查PHP-FPM状态：`systemctl status php-fpm`

### SSL证书问题
```bash
# 重新生成证书
cd /root/ebpf/nginx-https-server
rm -f server.crt server.key
./start_nginx_https.sh
```

### API无响应
1. 检查PHP-FPM进程：`ps aux | grep php-fpm`
2. 检查API日志：`tail -f /var/log/nginx/api.log`
3. 测试PHP语法：`php -l api/api.php`

## 🆚 与Python版本对比

| 特性 | Nginx版本 | Python版本 |
|------|-----------|------------|
| Web服务器 | Nginx + PHP-FPM | Python内置HTTP服务器 |
| 性能 | 高性能，生产级别 | 适中，测试级别 |
| 并发处理 | 优秀 | 有限 |
| 资源占用 | 低 | 中等 |
| 配置复杂度 | 中等 | 简单 |
| 扩展性 | 优秀 | 良好 |
| SSL性能 | 优化的SSL处理 | 基础SSL支持 |

## 🔐 安全说明

⚠️ **警告**: 本服务器使用自签名证书，仅用于测试目的。生产环境请使用正式的SSL证书。

- 证书信息：
  - 颁发者：TestOrg
  - 使用者：localhost
  - 有效期：365天
  - 算法：RSA 2048位

## 📋 系统要求

- **操作系统**: CentOS 8/RHEL 8 或更高版本
- **最低内存**: 512MB
- **磁盘空间**: 100MB
- **软件依赖**: 
  - Nginx
  - PHP-FPM
  - OpenSSL
  - curl (用于测试)

## 🤝 使用场景

1. **eBPF开发**: HTTPS流量分析和监控
2. **性能测试**: 大数据传输性能测试
3. **API开发**: RESTful API开发和测试
4. **SSL调试**: HTTPS连接问题排查
5. **网络分析**: 网络流量分析工具开发

## 📞 技术支持

如有问题或建议，请查看日志文件或联系开发团队。

---

**🎯 用途**: eBPF HTTPS 流量分析测试服务器  
**🏗️ 架构**: Nginx + PHP-FPM  
**🔧 版本**: 1.0  
**📅 更新**: 2024 