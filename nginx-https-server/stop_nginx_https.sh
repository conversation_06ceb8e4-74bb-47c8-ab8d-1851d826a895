#!/bin/bash

# Nginx HTTPS 服务停止脚本

# 颜色输出函数
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

print_header() {
    echo -e "\033[1;36m========================================\033[0m"
    echo -e "\033[1;36m  Nginx HTTPS 测试服务停止脚本\033[0m"
    echo -e "\033[1;36m========================================\033[0m"
}

# 停止Nginx进程
stop_nginx() {
    print_status "停止 Nginx 进程..."
    
    # 优雅停止Nginx
    if pgrep nginx > /dev/null; then
        pkill nginx
        sleep 2
        
        # 如果还有进程，强制杀死
        if pgrep nginx > /dev/null; then
            pkill -9 nginx
            sleep 1
        fi
        
        if ! pgrep nginx > /dev/null; then
            print_status "✅ Nginx 进程已停止"
        else
            print_error "❌ Nginx 进程停止失败"
        fi
    else
        print_status "Nginx 进程未运行"
    fi
}

# 停止PHP-FPM服务
stop_php_fpm() {
    print_status "停止 PHP-FPM 服务..."
    
    if systemctl is-active --quiet php-fpm; then
        systemctl stop php-fpm
        if ! systemctl is-active --quiet php-fpm; then
            print_status "✅ PHP-FPM 服务已停止"
        else
            print_error "❌ PHP-FPM 服务停止失败"
        fi
    else
        print_status "PHP-FPM 服务未运行"
    fi
}

# 检查端口状态
check_ports() {
    print_status "检查端口状态..."
    
    if netstat -tlnp | grep :9443 > /dev/null 2>&1; then
        print_error "⚠️  端口 9443 仍在监听"
    else
        print_status "✅ 端口 9443 已释放"
    fi
    
    if netstat -tlnp | grep :9000 > /dev/null 2>&1; then
        print_error "⚠️  端口 9000 仍在监听"
    else
        print_status "✅ 端口 9000 已释放"
    fi
}

# 显示停止结果
show_result() {
    echo ""
    print_header
    print_status "🛑 Nginx HTTPS 服务已停止"
    echo ""
    print_status "状态信息:"
    echo "   • Nginx 进程: 已停止" 
    echo "   • PHP-FPM 服务: 已停止"
    echo "   • 端口 9443: 已释放"
    echo "   • 端口 9000: 已释放"
    echo ""
    print_status "重新启动服务请运行:"
    echo "   ./start_nginx_https.sh"
    echo -e "\033[1;36m========================================\033[0m"
}

# 主函数
main() {
    print_header
    
    stop_nginx
    stop_php_fpm
    
    sleep 2
    check_ports
    show_result
}

# 运行主函数
main "$@"

