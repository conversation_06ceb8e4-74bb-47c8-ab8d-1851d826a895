<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx HTTPS 测试服务</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 40px; 
            border-radius: 20px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 { 
            color: #2c3e50; 
            text-align: center; 
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 300;
        }
        .subtitle {
            text-align: center;
            color: #7f8c8d;
            margin-bottom: 30px;
            font-size: 1.1em;
        }
        .info-box { 
            background: linear-gradient(135deg, #e8f5e8, #d4edda); 
            padding: 25px; 
            border-radius: 10px; 
            margin: 30px 0; 
            border-left: 5px solid #28a745;
        }
        .test-section { 
            margin: 30px 0; 
            padding: 30px; 
            border: 1px solid #e9ecef; 
            border-radius: 15px; 
            background: #f8f9fa;
        }
        .section-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 20px;
        }
        button { 
            background: linear-gradient(135deg, #007bff, #0056b3); 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,123,255,0.3);
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,123,255,0.4);
        }
        .result { 
            margin-top: 15px; 
            padding: 15px; 
            border-radius: 8px; 
            display: none;
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .log { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            max-height: 300px; 
            overflow-y: auto; 
            border: 1px solid #dee2e6;
            font-size: 13px;
            line-height: 1.6;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .status-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .status-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Nginx HTTPS 测试服务</h1>
        <div class="subtitle">基于 Nginx + PHP-FPM 的高性能 HTTPS 测试环境</div>
        
        <div class="info-box">
            <div class="section-title">✅ HTTPS 连接成功！</div>
            <p>您已经成功通过 HTTPS 协议访问基于 Nginx 的测试服务器。</p>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-value">Nginx</div>
                    <div class="status-label">Web服务器</div>
                </div>
                <div class="status-item">
                    <div class="status-value">9443</div>
                    <div class="status-label">HTTPS端口</div>
                </div>
                <div class="status-item">
                    <div class="status-value">SSL/TLS</div>
                    <div class="status-label">加密协议</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="currentTime">--:--:--</div>
                    <div class="status-label">访问时间</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="section-title">🧪 HTTPS 功能测试 (10个API接口)</div>
            <p style="color: #666; margin-bottom: 15px;">
                <strong>可用接口：</strong>
                /api/test (基础测试) • /api/complex (复杂数据) • /api/users (用户管理) • 
                /api/data (大数据-2KB+) • /api/status (系统状态) • /api/config (配置管理) • 
                /api/log (日志-2KB+) • /api/metrics (监控指标) • /api/search (搜索)
            </p>
            
            <div class="api-grid">
                <button onclick="testGet()">GET 请求测试</button>
                <button onclick="testPost()">POST 请求测试</button>
                <button onclick="testJson()">JSON 数据测试</button>
                <button onclick="testUsers()">用户管理API</button>
                <button onclick="testLargeData()">大数据API</button>
                <button onclick="testStatus()">系统状态API</button>
                <button onclick="testConfig()">配置管理API</button>
                <button onclick="testLogs()">日志API</button>
                <button onclick="testMetrics()">监控指标API</button>
                <button onclick="testSearch()">搜索API</button>
                <button onclick="clearLog()">清空日志</button>
            </div>
            
            <div id="testResult" class="result"></div>
        </div>

        <div class="test-section">
            <div class="section-title">📊 请求日志</div>
            <div id="requestLog" class="log">
                <div style="text-align: center; color: #6c757d; padding: 20px;">
                    等待API请求...
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="section-title">🔐 SSL证书信息</div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <p><strong>颁发者：</strong> TestOrg</p>
                <p><strong>使用者：</strong> localhost</p>
                <p><strong>有效期：</strong> 365 天</p>
                <p><strong>算法：</strong> RSA</p>
                <p><strong>用途：</strong> eBPF HTTPS 流量分析测试</p>
            </div>
        </div>
    </div>

    <script>
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        let logCounter = 1;

        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }

        function addLog(message) {
            const logDiv = document.getElementById('requestLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = '<div><strong>[' + timestamp + '] #' + logCounter++ + '</strong> ' + message + '</div>';
            
            if (logDiv.innerHTML.includes('等待API请求...')) {
                logDiv.innerHTML = '';
            }
            
            logDiv.innerHTML += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testGet() {
            addLog('🔍 发起 GET 请求测试...');
            fetch('/api/test?type=get&data=test_data')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ GET 请求测试成功！服务器返回: ' + JSON.stringify(data), true);
                    addLog('✅ GET 请求测试成功');
                })
                .catch(error => {
                    showResult('❌ GET 请求测试失败: ' + error.message, false);
                    addLog('❌ GET 请求测试失败');
                });
        }

        function testPost() {
            addLog('📤 发起 POST 请求测试...');
            var testData = {
                test_type: 'POST',
                timestamp: new Date().toISOString(),
                data: 'This is test data for eBPF analysis',
                server: 'Nginx + PHP-FPM'
            };

            fetch('/api/test', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('✅ POST 请求测试成功！服务器返回: ' + JSON.stringify(data), true);
                addLog('✅ POST 请求测试成功');
            })
            .catch(error => {
                showResult('❌ POST 请求测试失败: ' + error.message, false);
                addLog('❌ POST 请求测试失败');
            });
        }

        function testJson() { testAPI('complex', '🔧 发起 JSON 数据测试...', 'POST'); }
        function testUsers() { testAPI('users', '👥 测试用户管理API...', 'GET'); }
        function testLargeData() { testAPI('data', '📊 测试大数据API...', 'GET'); }
        function testStatus() { testAPI('status', '⚡ 测试系统状态API...', 'GET'); }
        function testConfig() { testAPI('config', '⚙️ 测试配置管理API...', 'GET'); }
        function testLogs() { testAPI('log', '📝 测试日志API...', 'GET'); }
        function testMetrics() { testAPI('metrics', '📈 测试监控指标API...', 'GET'); }
        function testSearch() { testAPI('search?q=nginx+test', '🔍 测试搜索API...', 'GET'); }

        function testAPI(endpoint, startMsg, method) {
            addLog(startMsg);
            var options = {method: method};
            if (method === 'POST') {
                options.headers = {'Content-Type': 'application/json'};
                options.body = JSON.stringify({test: 'data', server: 'nginx'});
            }
            
            fetch('/api/' + endpoint, options)
                .then(response => response.json())
                .then(data => {
                    showResult('✅ ' + endpoint + ' API测试成功！' + JSON.stringify(data), true);
                    addLog('✅ ' + endpoint + ' API测试成功');
                })
                .catch(error => {
                    showResult('❌ ' + endpoint + ' API测试失败: ' + error.message, false);
                    addLog('❌ ' + endpoint + ' API测试失败');
                });
        }

        function clearLog() {
            document.getElementById('requestLog').innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">日志已清空，等待新的API请求...</div>';
            document.getElementById('testResult').style.display = 'none';
            logCounter = 1;
            addLog('🧹 日志已清空');
        }
    </script>
</body>
</html>

