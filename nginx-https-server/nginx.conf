user root;
worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    sendfile on;
    keepalive_timeout 65;

    upstream php_backend {
        server 127.0.0.1:9000;
    }

    server {
        listen 0.0.0.0:9443 ssl http2;
        server_name localhost ***************;

        ssl_certificate /root/coding/sslkeylog/nginx-https-server/server.crt;
        ssl_certificate_key /root/coding/sslkeylog/nginx-https-server/server.key;

        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        root /root/coding/sslkeylog/nginx-https-server/html;
        index index.html index.htm index.php;

        location / {
            try_files $uri $uri/ /index.html;
        }
        
        location ~ \.php$ {
            include /etc/nginx/fastcgi_params;
            fastcgi_pass php_backend;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTPS on;
        }

        location ~ ^/api/(.*)$ {
            include /etc/nginx/fastcgi_params;
            fastcgi_pass php_backend;
            fastcgi_param SCRIPT_FILENAME /root/coding/sslkeylog/nginx-https-server/api/api.php;
            fastcgi_param PATH_INFO $1;
            fastcgi_param REQUEST_URI $request_uri;
            fastcgi_param QUERY_STRING $query_string;
            fastcgi_param REQUEST_METHOD $request_method;
            fastcgi_param CONTENT_TYPE $content_type;
            fastcgi_param CONTENT_LENGTH $content_length;
            fastcgi_param DOCUMENT_ROOT /root/coding/sslkeylog/nginx-https-server/api;
            fastcgi_param REMOTE_ADDR $remote_addr;
            fastcgi_param SERVER_NAME $server_name;
            fastcgi_param HTTPS on;
            fastcgi_param HTTPS on;
        }

        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;

        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /root/coding/sslkeylog/nginx-https-server/html;
        }
    }
} 