<?php
// Nginx HTTPS API 接口处理
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

function log_request($message) {
    $log_file = '/var/log/nginx/api.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] {$message}\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);
$api_path = str_replace('/api/', '', $path);
$api_path = trim($api_path, '/');

log_request("API Request: {$_SERVER['REQUEST_METHOD']} {$api_path} from {$_SERVER['REMOTE_ADDR']}");

switch ($api_path) {
    case 'test':
        handle_test_api();
        break;
    case 'complex':
        handle_complex_api();
        break;
    case 'users':
        handle_users_api();
        break;
    case 'data':
        handle_large_data_api();
        break;
    case 'status':
        handle_status_api();
        break;
    case 'config':
        handle_config_api();
        break;
    case 'log':
        handle_log_api();
        break;
    case 'metrics':
        handle_metrics_api();
        break;
    case 'search':
        handle_search_api();
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'API endpoint not found', 'path' => $api_path]);
        break;
}

function handle_test_api() {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        $response = [
            'status' => 'success',
            'message' => 'GET request received',
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => 'Nginx + PHP-FPM',
            'data' => $_GET,
            'test_id' => uniqid('test_')
        ];
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $response = [
            'status' => 'success',
            'message' => 'POST request received',
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => 'Nginx + PHP-FPM',
            'received_data' => $input,
            'test_id' => uniqid('post_')
        ];
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function handle_complex_api() {
    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $response = [
            'status' => 'success',
            'message' => 'Complex data processed',
            'timestamp' => date('Y-m-d H:i:s'),
            'server' => 'Nginx + PHP-FPM',
            'processed_data' => [
                'input_size' => strlen(json_encode($input)),
                'processed_at' => microtime(true),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'client_ip' => $_SERVER['REMOTE_ADDR'],
                'request_id' => uniqid('complex_')
            ],
            'original_data' => $input
        ];
    } else {
        $response = [
            'status' => 'success',
            'message' => 'Complex API endpoint',
            'supported_methods' => ['POST'],
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function handle_users_api() {
    $mock_users = [
        [
            'id' => 1,
            'name' => 'nginx_user_1',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'created_at' => '2024-01-01 10:00:00'
        ],
        [
            'id' => 2,
            'name' => 'nginx_user_2',
            'email' => '<EMAIL>',
            'role' => 'user',
            'created_at' => '2024-01-02 11:00:00'
        ],
        [
            'id' => 3,
            'name' => 'nginx_user_3',
            'email' => '<EMAIL>',
            'role' => 'moderator',
            'created_at' => '2024-01-03 12:00:00'
        ]
    ];
    
    $response = [
        'status' => 'success',
        'message' => 'Users retrieved successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'total_users' => count($mock_users),
        'users' => $mock_users
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function handle_large_data_api() {
    $large_data = [];
    for ($i = 1; $i <= 100; $i++) {
        $large_data[] = [
            'id' => $i,
            'name' => "nginx_data_record_{$i}",
            'description' => "这是一个用于测试大数据传输的记录项目，编号为 {$i}。包含各种测试数据，用于验证Nginx+PHP-FPM在处理大量数据时的性能表现。",
            'data' => [
                'field1' => str_repeat("test_data_{$i}_", 10),
                'field2' => range(1, 20),
                'field3' => [
                    'nested_data' => "这是嵌套的测试数据 {$i}",
                    'timestamp' => date('Y-m-d H:i:s'),
                    'random_value' => rand(1000, 9999)
                ]
            ],
            'created_at' => date('Y-m-d H:i:s', time() - rand(0, 86400))
        ];
    }
    
    $response = [
        'status' => 'success',
        'message' => 'Large data retrieved successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'data_size' => strlen(json_encode($large_data)) . ' bytes',
        'records_count' => count($large_data),
        'data' => $large_data
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function handle_status_api() {
    $status_data = [
        'status' => 'success',
        'message' => 'System status retrieved',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'system_info' => [
            'php_version' => phpversion(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Nginx',
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true)
            ]
        ],
        'service_status' => [
            'nginx' => 'running',
            'php-fpm' => 'running',
            'ssl' => 'enabled'
        ]
    ];
    
    echo json_encode($status_data, JSON_UNESCAPED_UNICODE);
}

function handle_config_api() {
    $config_data = [
        'status' => 'success',
        'message' => 'Configuration retrieved',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'config' => [
            'ssl_enabled' => true,
            'port' => 9443,
            'max_connections' => 1024,
            'timeout' => 60,
            'compression' => 'gzip',
            'api_version' => '1.0',
            'supported_methods' => ['GET', 'POST'],
            'rate_limit' => '100/minute'
        ]
    ];
    
    echo json_encode($config_data, JSON_UNESCAPED_UNICODE);
}

function handle_log_api() {
    $log_entries = [];
    $log_levels = ['INFO', 'WARN', 'ERROR', 'DEBUG'];
    
    for ($i = 1; $i <= 50; $i++) {
        $log_entries[] = [
            'id' => $i,
            'timestamp' => date('Y-m-d H:i:s', time() - rand(0, 3600)),
            'level' => $log_levels[array_rand($log_levels)],
            'message' => "这是日志条目 {$i}：Nginx + PHP-FPM 系统运行正常，处理HTTPS请求成功。客户端IP: " . $_SERVER['REMOTE_ADDR'],
            'source' => 'nginx-api',
            'details' => [
                'request_id' => uniqid('req_'),
                'processing_time' => rand(10, 500) . 'ms',
                'memory_usage' => rand(1024, 10240) . 'KB'
            ]
        ];
    }
    
    $response = [
        'status' => 'success',
        'message' => 'Log data retrieved successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'log_size' => 'Large (>2KB)',
        'entries_count' => count($log_entries),
        'logs' => $log_entries
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function handle_metrics_api() {
    $metrics_data = [
        'status' => 'success',
        'message' => 'Metrics retrieved successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'metrics' => [
            'requests_per_second' => rand(50, 200),
            'response_time_avg' => rand(50, 200) . 'ms',
            'cpu_usage' => rand(10, 80) . '%',
            'memory_usage' => rand(30, 70) . '%',
            'active_connections' => rand(10, 100),
            'total_requests' => rand(10000, 50000)
        ]
    ];
    
    echo json_encode($metrics_data, JSON_UNESCAPED_UNICODE);
}

function handle_search_api() {
    $query = $_GET['q'] ?? '';
    
    $search_results = [
        [
            'id' => 1,
            'title' => 'Nginx HTTPS 配置指南',
            'content' => '详细介绍如何配置Nginx的HTTPS服务',
            'relevance' => 0.95
        ],
        [
            'id' => 2,
            'title' => 'PHP-FPM 性能优化',
            'content' => 'PHP-FPM的配置和性能调优技巧',
            'relevance' => 0.87
        ]
    ];
    
    $response = [
        'status' => 'success',
        'message' => 'Search completed successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'server' => 'Nginx + PHP-FPM',
        'query' => $query,
        'results_count' => count($search_results),
        'results' => $search_results
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>

