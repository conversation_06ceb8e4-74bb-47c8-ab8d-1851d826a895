server:
  port: 1443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
    key-alias: tomcat
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,text/css,text/javascript
  error:
    include-message: always
    include-binding-errors: always
  tomcat:
    max-threads: 200
    max-connections: 8192

spring:
  application:
    name: https-test-server
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      indent-output: true
  security:
    user:
      name: admin
      password: admin
      roles: ADMIN

logging:
  level:
    com.ebpf.test: INFO
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 自定义配置
app:
  name: Java HTTPS Test Server
  version: 1.0.0
  description: Java HTTPS服务器用于eBPF流量分析测试
  features:
    - SSL/TLS加密
    - JSON API
    - 大数据传输
    - 系统监控
    - 请求日志 