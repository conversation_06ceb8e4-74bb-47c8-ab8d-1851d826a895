package com.ebpf.test;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * HTTPS 测试服务器主应用类
 * 用于eBPF HTTPS流量分析测试
 */
@SpringBootApplication
@EnableConfigurationProperties
public class HttpsTestServerApplication {

    public static void main(String[] args) {
        System.out.println("🔒 启动 Java HTTPS 测试服务器...");
        System.out.println("用于eBPF HTTPS流量分析测试");
        SpringApplication.run(HttpsTestServerApplication.class, args);
    }
} 