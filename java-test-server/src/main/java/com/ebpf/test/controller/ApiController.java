package com.ebpf.test.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * API控制器
 * 提供10个不同的API接口用于eBPF流量分析测试
 */
@RestController
@RequestMapping("/api")
public class ApiController {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final List<String> REQUEST_LOG = Collections.synchronizedList(new ArrayList<>());

    /**
     * 1. 基础测试API - 支持GET和POST
     */
    @GetMapping("/test")
    public Map<String, Object> testGet(@RequestParam Map<String, String> params, HttpServletRequest request) {
        logRequest("GET /api/test", request);
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("method", "GET");
        response.put("message", "Java HTTPS 测试服务器 - GET 请求成功");
        response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("parameters", params);
        response.put("server_info", Map.of(
            "technology", "Java + Spring Boot",
            "container", "Apache Tomcat Embedded",
            "port", 1443
        ));
        
        return response;
    }

    @PostMapping("/test")
    public Map<String, Object> testPost(@RequestBody(required = false) JsonNode body, HttpServletRequest request) {
        logRequest("POST /api/test", request);
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("method", "POST");
        response.put("message", "Java HTTPS 测试服务器 - POST 请求成功");
        response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("received_data", body);
        response.put("data_processing", Map.of(
            "processed_by", "Jackson ObjectMapper",
            "content_type", "application/json",
            "server_framework", "Spring Boot"
        ));
        
        return response;
    }

    /**
     * 2. 复杂数据API
     */
    @PostMapping("/complex")
    public Map<String, Object> complexData(@RequestBody JsonNode body, HttpServletRequest request) {
        logRequest("POST /api/complex", request);
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "复杂数据处理完成");
        response.put("original_data", body);
        response.put("processing_info", Map.of(
            "processor", "Java Spring Boot",
            "json_library", "Jackson",
            "processing_time", System.currentTimeMillis(),
            "thread_name", Thread.currentThread().getName()
        ));
        
        // 添加一些处理后的数据
        Map<String, Object> processedData = new HashMap<>();
        processedData.put("data_hash", body.toString().hashCode());
        processedData.put("field_count", countFields(body));
        processedData.put("processed_at", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("processed_data", processedData);
        
        return response;
    }

    /**
     * 3. 用户管理API
     */
    @GetMapping("/users")
    public Map<String, Object> getUsers(HttpServletRequest request) {
        logRequest("GET /api/users", request);
        
        List<Map<String, Object>> users = Arrays.asList(
            Map.of("id", 1, "name", "张三", "role", "admin", "status", "active", "create_time", "2024-01-01 10:00:00"),
            Map.of("id", 2, "name", "李四", "role", "user", "status", "active", "create_time", "2024-01-02 11:00:00"),
            Map.of("id", 3, "name", "王五", "role", "user", "status", "inactive", "create_time", "2024-01-03 12:00:00"),
            Map.of("id", 4, "name", "赵六", "role", "moderator", "status", "active", "create_time", "2024-01-04 13:00:00"),
            Map.of("id", 5, "name", "钱七", "role", "user", "status", "active", "create_time", "2024-01-05 14:00:00")
        );
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("users", users);
        response.put("total_count", users.size());
        response.put("active_count", users.stream().mapToInt(u -> "active".equals(u.get("status")) ? 1 : 0).sum());
        response.put("server_info", "Java Spring Boot User Management API");
        
        return response;
    }

    /**
     * 4. 大数据API (>2KB)
     */
    @GetMapping("/data")
    public Map<String, Object> getLargeData(HttpServletRequest request) {
        logRequest("GET /api/data", request);
        
        // 生成大量数据
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", i);
            item.put("name", "数据项_" + i);
            item.put("description", "这是第" + i + "个数据项的详细描述，用于eBPF流量分析测试。包含足够的文本内容以增加数据传输量。");
            item.put("value", Math.random() * 1000);
            item.put("timestamp", LocalDateTime.now().minusMinutes(i).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            item.put("metadata", Map.of(
                "category", "test_data",
                "priority", i % 3 == 0 ? "high" : "normal",
                "tags", Arrays.asList("tag_" + (i % 5), "category_" + (i % 3), "test"),
                "properties", Map.of(
                    "size", "large",
                    "format", "json",
                    "encoding", "utf-8",
                    "server", "Java Spring Boot"
                )
            ));
            dataList.add(item);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "大数据API - 数据量超过2KB");
        response.put("data_count", dataList.size());
        response.put("data", dataList);
        response.put("generation_info", Map.of(
            "generated_by", "Java Spring Boot",
            "generation_time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            "estimated_size", "大于2KB",
            "purpose", "eBPF HTTPS流量分析测试"
        ));
        
        return response;
    }

    /**
     * 5. 系统状态API
     */
    @GetMapping("/status")
    public Map<String, Object> getSystemStatus(HttpServletRequest request) {
        logRequest("GET /api/status", request);
        
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("server_status", "running");
        response.put("uptime", runtimeBean.getUptime());
        
        // CPU信息（模拟）
        Map<String, Object> cpu = new HashMap<>();
        cpu.put("usage", Math.round(Math.random() * 100 * 100.0) / 100.0);
        cpu.put("cores", Runtime.getRuntime().availableProcessors());
        response.put("cpu", cpu);
        
        // 内存信息
        Map<String, Object> memory = new HashMap<>();
        long totalMemory = memoryBean.getHeapMemoryUsage().getMax();
        long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
        memory.put("total", totalMemory);
        memory.put("used", usedMemory);
        memory.put("free", totalMemory - usedMemory);
        memory.put("usage", Math.round((double) usedMemory / totalMemory * 10000.0) / 100.0);
        response.put("memory", memory);
        
        // JVM信息
        Map<String, Object> jvm = new HashMap<>();
        jvm.put("version", runtimeBean.getVmVersion());
        jvm.put("vendor", runtimeBean.getVmVendor());
        jvm.put("name", runtimeBean.getVmName());
        response.put("jvm", jvm);
        
        response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        return response;
    }

    /**
     * 6. 配置管理API
     */
    @GetMapping("/config")
    public Map<String, Object> getConfig(HttpServletRequest request) {
        logRequest("GET /api/config", request);
        
        Map<String, Object> config = new HashMap<>();
        config.put("server_name", "Java HTTPS Test Server");
        config.put("version", "1.0.0");
        config.put("port", 1443);
        config.put("ssl_enabled", true);
        config.put("max_connections", 1000);
        config.put("timeout", 30000);
        config.put("charset", "UTF-8");
        config.put("log_level", "INFO");
        config.put("features", Arrays.asList("SSL", "JSON", "REST", "Logging", "Metrics"));
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("config", config);
        response.put("config_source", "Java Application Properties");
        response.put("last_updated", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        return response;
    }

    /**
     * 7. 日志API (>2KB)
     */
    @GetMapping("/log")
    public Map<String, Object> getLogs(HttpServletRequest request) {
        logRequest("GET /api/log", request);
        
        List<Map<String, Object>> logs = new ArrayList<>();
        String[] logLevels = {"INFO", "WARN", "ERROR", "DEBUG"};
        String[] logMessages = {
            "Java HTTPS服务器启动成功",
            "接收到新的HTTPS连接请求",
            "SSL握手完成，连接建立",
            "API请求处理完成",
            "JSON数据解析成功",
            "大数据API调用，返回数据超过2KB",
            "系统状态检查正常",
            "配置文件加载完成",
            "Spring Boot应用初始化完成",
            "Apache Tomcat容器启动成功"
        };
        
        for (int i = 1; i <= 100; i++) {
            Map<String, Object> logEntry = new HashMap<>();
            logEntry.put("id", i);
            logEntry.put("timestamp", LocalDateTime.now().minusMinutes(i).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            logEntry.put("level", logLevels[i % logLevels.length]);
            logEntry.put("message", logMessages[i % logMessages.length]);
            logEntry.put("thread", "http-nio-1443-exec-" + (i % 10 + 1));
            logEntry.put("logger", "com.ebpf.test.HttpsTestServer");
            logEntry.put("details", Map.of(
                "request_id", "req_" + i,
                "session_id", "sess_" + (i % 20),
                "user_agent", "eBPF-Test-Client/1.0",
                "processing_time", Math.round(Math.random() * 1000)
            ));
            logs.add(logEntry);
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "日志API - 返回数据超过2KB");
        response.put("logs", logs);
        response.put("total_count", logs.size());
        response.put("log_info", Map.of(
            "server", "Java Spring Boot",
            "log_framework", "Logback",
            "estimated_size", "大于2KB",
            "generation_time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        ));
        
        return response;
    }

    /**
     * 8. 监控指标API
     */
    @GetMapping("/metrics")
    public Map<String, Object> getMetrics(HttpServletRequest request) {
        logRequest("GET /api/metrics", request);
        
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("requests_total", Math.round(Math.random() * 10000));
        metrics.put("requests_success", Math.round(Math.random() * 9500));
        metrics.put("requests_error", Math.round(Math.random() * 500));
        metrics.put("response_time_avg", Math.round(Math.random() * 1000 * 100.0) / 100.0);
        metrics.put("connections_active", Math.round(Math.random() * 100));
        metrics.put("ssl_handshakes", Math.round(Math.random() * 1000));
        metrics.put("data_transferred_mb", Math.round(Math.random() * 1000 * 100.0) / 100.0);
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("metrics", metrics);
        response.put("collection_time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        response.put("server_info", Map.of(
            "technology", "Java + Spring Boot",
            "monitoring", "Spring Boot Actuator",
            "jvm_metrics", "Available"
        ));
        
        return response;
    }

    /**
     * 9. 搜索API
     */
    @GetMapping("/search")
    public Map<String, Object> search(@RequestParam(required = false) String q, 
                                     @RequestParam(required = false) String category,
                                     HttpServletRequest request) {
        logRequest("GET /api/search", request);
        
        List<Map<String, Object>> results = new ArrayList<>();
        
        if (q != null && !q.isEmpty()) {
            // 模拟搜索结果
            for (int i = 1; i <= 20; i++) {
                Map<String, Object> result = new HashMap<>();
                result.put("id", i);
                result.put("title", "搜索结果 " + i + " - " + q);
                result.put("description", "这是关于'" + q + "'的搜索结果描述，来自Java HTTPS测试服务器");
                result.put("url", "/api/result/" + i);
                result.put("score", Math.round(Math.random() * 100 * 100.0) / 100.0);
                result.put("category", category != null ? category : "general");
                result.put("timestamp", LocalDateTime.now().minusHours(i).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
                results.add(result);
            }
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("query", q);
        response.put("category", category);
        response.put("results", results);
        response.put("total_found", results.size());
        response.put("search_time", Math.round(Math.random() * 100 * 10.0) / 10.0);
        response.put("server_info", "Java Spring Boot Search API");
        
        return response;
    }

    /**
     * 工具方法：统计JSON字段数量
     */
    private int countFields(JsonNode node) {
        if (node == null) return 0;
        if (node.isObject()) {
            int count = 0;
            node.fieldNames().forEachRemaining(field -> {});
            node.fields().forEachRemaining(entry -> {});
            return node.size();
        }
        return 0;
    }

    /**
     * 记录请求日志
     */
    private void logRequest(String endpoint, HttpServletRequest request) {
        String logEntry = String.format("[%s] %s - %s:%d - %s", 
            LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
            endpoint,
            request.getRemoteAddr(),
            request.getRemotePort(),
            request.getHeader("User-Agent")
        );
        REQUEST_LOG.add(logEntry);
        
        // 保持日志数量在合理范围内
        if (REQUEST_LOG.size() > 1000) {
            REQUEST_LOG.subList(0, 500).clear();
        }
        
        System.out.println("🔗 " + logEntry);
    }
} 