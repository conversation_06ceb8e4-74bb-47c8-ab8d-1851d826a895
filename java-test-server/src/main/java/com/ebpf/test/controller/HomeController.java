package com.ebpf.test.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 主页控制器
 * 提供HTML界面和基础功能
 */
@Controller
public class HomeController {

    @GetMapping("/")
    @ResponseBody
    public String home(HttpServletRequest request) {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String serverInfo = request.getServerName() + ":" + request.getServerPort();
        
        return "<html><head><title>Java HTTPS 测试服务</title></head><body>" +
               "<h1>🔒 Java HTTPS 测试服务</h1>" +
               "<h2>✅ HTTPS 连接成功！</h2>" +
               "<p>您已经成功通过 HTTPS 协议访问本 Java 服务器。</p>" +
               "<h3>服务器信息：</h3>" +
               "<ul>" +
               "<li>协议：HTTPS (TLS/SSL)</li>" +
               "<li>端口：1443</li>" +
               "<li>技术栈：Java + Spring Boot + Apache Tomcat</li>" +
               "<li>证书：自签名证书</li>" +
               "<li>API接口：10个 (其中2个返回>2KB数据)</li>" +
               "<li>服务器地址：" + serverInfo + "</li>" +
               "<li>访问时间：" + currentTime + "</li>" +
               "</ul>" +
               "<h3>API测试：</h3>" +
               "<p><a href='/api/test'>基础测试API</a></p>" +
               "<p><a href='/api/users'>用户管理API</a></p>" +
               "<p><a href='/api/data'>大数据API (>2KB)</a></p>" +
               "<p><a href='/api/status'>系统状态API</a></p>" +
               "<p><a href='/api/config'>配置管理API</a></p>" +
               "<p><a href='/api/log'>日志API (>2KB)</a></p>" +
               "<p><a href='/api/metrics'>监控指标API</a></p>" +
               "<p><a href='/api/search?q=java'>搜索API</a></p>" +
               "</body></html>";
    }
} 