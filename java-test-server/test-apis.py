#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import urllib3
import json
import time
from datetime import datetime

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

BASE_URL = "https://localhost:1443"

def print_separator(title):
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print('='*60)

def test_api(method, endpoint, data=None, params=None):
    """测试API接口"""
    url = f"{BASE_URL}{endpoint}"
    headers = {"Content-Type": "application/json"} if data else {}
    
    try:
        start_time = time.time()
        
        if method.upper() == "GET":
            response = requests.get(url, params=params, headers=headers, verify=False, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=headers, verify=False, timeout=10)
        else:
            return False, f"不支持的HTTP方法: {method}"
        
        end_time = time.time()
        response_time = round((end_time - start_time) * 1000, 2)
        
        if response.status_code == 200:
            response_data = response.json()
            data_size = len(response.content)
            return True, {
                "status_code": response.status_code,
                "response_time": f"{response_time}ms",
                "data_size": f"{data_size} bytes",
                "data": response_data
            }
        else:
            return False, f"HTTP {response.status_code}: {response.text}"
            
    except requests.exceptions.ConnectionError:
        return False, "连接失败 - 请确保服务器正在运行"
    except requests.exceptions.Timeout:
        return False, "请求超时"
    except Exception as e:
        return False, f"请求异常: {str(e)}"

def main():
    print("🔒 Java HTTPS 测试服务器 API 测试")
    print(f"测试服务器: {BASE_URL}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试用例
    test_cases = [
        {
            "name": "基础测试API (GET)",
            "method": "GET",
            "endpoint": "/api/test",
            "params": {"type": "get", "data": "java_test_data"}
        },
        {
            "name": "基础测试API (POST)",
            "method": "POST", 
            "endpoint": "/api/test",
            "data": {
                "test_type": "POST",
                "timestamp": datetime.now().isoformat(),
                "data": "This is test data for eBPF analysis from Java server",
                "server": "Java Spring Boot"
            }
        },
        {
            "name": "复杂数据API",
            "method": "POST",
            "endpoint": "/api/complex",
            "data": {
                "user": {
                    "id": 12345,
                    "name": "java_test_user",
                    "permissions": ["read", "write", "admin"]
                },
                "session": {
                    "token": "java_abc123def456",
                    "expires": datetime.now().isoformat()
                },
                "metadata": {
                    "test_purpose": "eBPF HTTPS流量分析 - Java服务器",
                    "server_type": "Spring Boot + Apache Tomcat",
                    "timestamp": time.time()
                }
            }
        },
        {
            "name": "用户管理API",
            "method": "GET",
            "endpoint": "/api/users"
        },
        {
            "name": "大数据API (>2KB)",
            "method": "GET", 
            "endpoint": "/api/data"
        },
        {
            "name": "系统状态API",
            "method": "GET",
            "endpoint": "/api/status"
        },
        {
            "name": "配置管理API",
            "method": "GET",
            "endpoint": "/api/config"
        },
        {
            "name": "日志API (>2KB)",
            "method": "GET",
            "endpoint": "/api/log"
        },
        {
            "name": "监控指标API",
            "method": "GET",
            "endpoint": "/api/metrics"
        },
        {
            "name": "搜索API",
            "method": "GET",
            "endpoint": "/api/search",
            "params": {"q": "java", "category": "test"}
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print_separator(f"{i}/{total_count} {test_case['name']}")
        
        success, result = test_api(
            test_case["method"],
            test_case["endpoint"],
            test_case.get("data"),
            test_case.get("params")
        )
        
        if success:
            print(f"✅ 测试成功")
            print(f"📊 状态码: {result['status_code']}")
            print(f"⏱️  响应时间: {result['response_time']}")
            print(f"📦 数据大小: {result['data_size']}")
            
            # 对于大数据API，显示更多信息
            if "大数据" in test_case['name'] or "日志" in test_case['name']:
                data_size_bytes = int(result['data_size'].split()[0])
                if data_size_bytes > 2048:
                    print(f"🎯 大数据验证: ✅ 数据量超过2KB ({data_size_bytes} bytes)")
                else:
                    print(f"⚠️  大数据验证: 数据量未超过2KB ({data_size_bytes} bytes)")
            
            # 显示部分响应数据
            if isinstance(result['data'], dict):
                if 'status' in result['data']:
                    print(f"📝 服务器状态: {result['data']['status']}")
                if 'message' in result['data']:
                    print(f"📝 服务器消息: {result['data']['message']}")
                    
            success_count += 1
        else:
            print(f"❌ 测试失败: {result}")
        
        time.sleep(0.5)  # 避免请求过快
    
    # 测试总结
    print_separator("测试总结")
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"❌ 失败: {total_count - success_count}/{total_count}")
    print(f"📊 成功率: {round(success_count/total_count*100, 1)}%")
    
    if success_count == total_count:
        print("\n🎉 所有API测试通过！Java HTTPS服务器运行正常")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个API测试失败")
    
    # 最后测试主页
    print_separator("主页访问测试")
    try:
        response = requests.get(BASE_URL, verify=False, timeout=10)
        if response.status_code == 200:
            print("✅ 主页访问成功")
            print(f"📦 页面大小: {len(response.content)} bytes")
            if "Java HTTPS 测试服务" in response.text:
                print("✅ 页面内容验证通过")
            else:
                print("⚠️  页面内容验证失败")
        else:
            print(f"❌ 主页访问失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 主页访问异常: {str(e)}")

if __name__ == "__main__":
    main() 