# Java HTTPS 测试服务器

这是一个基于Java和Spring Boot的HTTPS测试服务器，用于eBPF HTTPS流量分析测试。

## 功能特性

- 🔒 完整的HTTPS服务器实现 (Java + Spring Boot + Apache Tomcat)
- 📊 10个不同的API接口
- 🌐 支持GET和POST请求
- 📈 包含大数据接口(>2KB)
- 🛡️ SSL/TLS加密通信
- 📝 完整的请求日志记录
- 🔧 系统状态监控
- ☕ JVM性能指标
- 🚀 Spring Boot自动配置

## 技术栈

- **Java**: 11+
- **框架**: Spring Boot 2.7.18
- **Web容器**: Apache Tomcat (嵌入式)
- **安全**: Spring Security
- **JSON**: Jackson
- **构建工具**: Maven
- **SSL**: 自签名证书 (PKCS12)

## 文件说明

- `src/main/java/` - Java源代码
  - `HttpsTestServerApplication.java` - 主应用类
  - `controller/HomeController.java` - 主页控制器
  - `controller/ApiController.java` - API控制器
  - `config/SecurityConfig.java` - 安全配置
- `src/main/resources/` - 资源文件
  - `application.yml` - 应用配置
  - `keystore.p12` - SSL证书 (自动生成)
- `pom.xml` - Maven项目配置
- `generate-ssl-cert.sh` - SSL证书生成脚本
- `start-https-server.sh` - 服务器启动脚本
- `test-apis.py` - API接口测试脚本

## 快速开始

### 环境要求

- Java 11 或更高版本
- Maven 3.6+
- 所需Python库（用于测试）：requests, urllib3

### 安装依赖

```bash
# 安装Python测试依赖
pip3 install requests urllib3
```

### 启动服务器

```bash
# 方法1：使用启动脚本（推荐）
chmod +x start-https-server.sh
./start-https-server.sh

# 方法2：手动编译和运行
mvn clean compile
mvn spring-boot:run

# 方法3：生成jar包运行
mvn clean package
java -jar target/https-test-server-1.0.0.jar
```

### 测试API

```bash
# 运行API测试脚本
python3 test-apis.py
```

## API接口

| 接口 | 方法 | 描述 | 数据量 |
|------|------|------|---------|
| `/api/test` | GET/POST | 基础测试API | 标准 |
| `/api/complex` | POST | 复杂数据处理API | 标准 |
| `/api/users` | GET | 用户管理API | 标准 |
| `/api/data` | GET | 大数据API | >2KB |
| `/api/status` | GET | 系统状态API | 标准 |
| `/api/config` | GET | 配置管理API | 标准 |
| `/api/log` | GET | 日志API | >2KB |
| `/api/metrics` | GET | 监控指标API | 标准 |
| `/api/search` | GET | 搜索API | 标准 |

## 访问地址

- `https://localhost:1443`
- `https://127.0.0.1:1443`
- `https://YOUR_SERVER_IP:1443`

## SSL证书

服务器使用自签名SSL证书，证书信息：

- **类型**: PKCS12
- **文件**: `src/main/resources/keystore.p12`
- **密码**: `changeit`
- **别名**: `tomcat`
- **有效期**: 365天
- **CN**: localhost
- **SAN**: localhost, 127.0.0.1

### 生成新证书

```bash
chmod +x generate-ssl-cert.sh
./generate-ssl-cert.sh
```

## 配置说明

主要配置文件：`src/main/resources/application.yml`

```yaml
server:
  port: 1443                    # HTTPS端口
  ssl:
    enabled: true               # 启用SSL
    key-store: classpath:keystore.p12
    key-store-password: changeit
    key-store-type: PKCS12
```

## 开发说明

### 添加新API

1. 在 `ApiController.java` 中添加新方法
2. 使用 `@GetMapping` 或 `@PostMapping` 注解
3. 返回 `Map<String, Object>` 类型的JSON响应

### 修改配置

1. 编辑 `application.yml` 文件
2. 重启服务器生效

### 自定义SSL证书

1. 替换 `src/main/resources/keystore.p12`
2. 修改 `application.yml` 中的证书配置
3. 重启服务器

## 注意事项

- 使用自签名证书，浏览器会显示安全警告
- 需要开放1443端口防火墙
- 生产环境请使用正式SSL证书
- JVM堆内存建议512MB以上

## 用途

本项目主要用于：
- eBPF HTTPS流量分析测试
- SSL/TLS协议研究
- Java Web应用性能测试
- Spring Boot应用开发学习
- 网络安全工具开发

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   ss -tlnp | grep 1443
   # 终止占用进程
   pkill -f "java.*https-test-server"
   ```

2. **SSL证书错误**
   ```bash
   # 重新生成证书
   ./generate-ssl-cert.sh
   ```

3. **Maven依赖问题**
   ```bash
   # 重新下载依赖
   mvn clean install -U
   ```

4. **Java版本问题**
   ```bash
   # 检查Java版本
   java -version
   # 需要Java 11+
   ```

## 性能指标

- **启动时间**: 约5-10秒
- **内存占用**: 约100-200MB
- **并发连接**: 支持200个线程
- **响应时间**: 平均10-50ms

## 许可证

本项目仅用于学习和测试目的。 