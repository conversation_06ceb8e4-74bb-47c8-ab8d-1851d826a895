#!/bin/bash

echo "🔒 Java HTTPS 测试服务器启动脚本"
echo "=================================="

# 确保在正确的目录
cd "$(dirname "$0")"

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安装！请先安装Java 11或更高版本"
    exit 1
fi

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven 未安装！正在尝试使用包装器..."
    if [ ! -f "./mvnw" ]; then
        echo "❌ Maven包装器也不存在！请安装Maven"
        exit 1
    fi
    MVN_CMD="./mvnw"
else
    MVN_CMD="mvn"
fi

# 检查SSL证书是否存在
if [ ! -f "src/main/resources/keystore.p12" ]; then
    echo "🔐 SSL证书不存在，正在生成..."
    chmod +x generate-ssl-cert.sh
    ./generate-ssl-cert.sh
    if [ $? -ne 0 ]; then
        echo "❌ SSL证书生成失败！"
        exit 1
    fi
fi

# 获取本机IP
LOCAL_IP=$(ip route get 1 2>/dev/null | awk '{print $7; exit}' 2>/dev/null || echo "获取失败")

# 检查端口是否被占用
if ss -tlnp 2>/dev/null | grep -q ":1443 "; then
    echo "⚠️  端口 1443 已被占用"
    echo "正在尝试停止现有服务..."
    pkill -f "java.*https-test-server"
    sleep 3
fi

echo "🔧 编译Java项目..."
$MVN_CMD clean compile -q

if [ $? -ne 0 ]; then
    echo "❌ 项目编译失败！"
    exit 1
fi

echo ""
echo "🚀 启动 Java HTTPS 测试服务器..."
echo ""
echo "📍 访问地址："
echo "   https://localhost:1443"
echo "   https://127.0.0.1:1443"
if [ "$LOCAL_IP" != "获取失败" ]; then
    echo "   https://${LOCAL_IP}:1443"
fi
echo ""
echo "🔒 证书信息："
echo "   - 类型: 自签名证书 (PKCS12)"
echo "   - 有效期: 365天"
echo "   - CN: localhost"
echo "   - 技术栈: Java + Spring Boot + Apache Tomcat"
echo ""
echo "⚠️  浏览器安全提示："
echo "   由于使用自签名证书，浏览器会显示安全警告"
echo "   请点击 '高级' → '继续访问' 来访问测试服务"
echo ""
echo "🛑 停止服务: 按 Ctrl+C"
echo "=================================="
echo ""

# 启动服务器
$MVN_CMD spring-boot:run 