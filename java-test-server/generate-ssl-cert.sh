#!/bin/bash

echo "🔐 生成Java HTTPS服务器SSL证书..."
echo "=================================="

# 确保在正确的目录
cd "$(dirname "$0")"

# 创建resources目录如果不存在
mkdir -p src/main/resources

# 生成keystore文件
echo "📜 生成自签名证书和keystore..."

# 删除已存在的keystore文件
if [ -f "src/main/resources/keystore.p12" ]; then
    rm src/main/resources/keystore.p12
    echo "删除已存在的keystore文件"
fi

# 生成PKCS12格式的keystore
keytool -genkeypair \
    -alias tomcat \
    -keyalg RSA \
    -keysize 2048 \
    -storetype PKCS12 \
    -keystore src/main/resources/keystore.p12 \
    -storepass changeit \
    -keypass changeit \
    -validity 365 \
    -dname "CN=localhost,OU=TestOrg,O=TestOrg,L=Beijing,ST=Beijing,C=CN" \
    -ext SAN=dns:localhost,ip:127.0.0.1

if [ $? -eq 0 ]; then
    echo "✅ SSL证书生成成功!"
    echo ""
    echo "📋 证书信息:"
    echo "   - 文件位置: src/main/resources/keystore.p12"
    echo "   - 密码: changeit"
    echo "   - 别名: tomcat"
    echo "   - 类型: PKCS12"
    echo "   - 有效期: 365天"
    echo "   - CN: localhost"
    echo "   - SAN: localhost, 127.0.0.1"
    echo ""
    echo "🔍 查看证书详情:"
    keytool -list -v -keystore src/main/resources/keystore.p12 -storepass changeit -alias tomcat
else
    echo "❌ SSL证书生成失败!"
    exit 1
fi 