#!/bin/bash

echo "🚀 Node.js HTTPS 测试服务器启动脚本"
echo "======================================="

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 未找到 package.json 文件！"
    echo "请确保在包含 package.json 的目录中运行此脚本"
    exit 1
fi

# 检查 Node.js 是否可用
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装！"
    echo "请先安装 Node.js: https://nodejs.org/"
    exit 1
fi

# 检查 npm 是否可用
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装！"
    echo "请先安装 npm"
    exit 1
fi

# 安装依赖
echo "📦 检查并安装依赖..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖包..."
    npm install --registry=https://registry.npmmirror.com
    if [ $? -ne 0 ]; then
        echo "尝试使用默认源安装..."
        npm install
    fi
fi

# 生成 SSL 证书（如果不存在）
if [ ! -f "server.crt" ] || [ ! -f "server.key" ]; then
    echo "🔒 生成自签名 SSL 证书..."
    
    # 检查 openssl 是否可用
    if ! command -v openssl &> /dev/null; then
        echo "❌ OpenSSL 未安装！请先安装 OpenSSL"
        exit 1
    fi
    
    # 生成私钥
    openssl genrsa -out server.key 2048
    
    # 生成证书签名请求配置
    cat > server.conf << EOF
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_req
prompt = no

[req_distinguished_name]
C = CN
ST = Beijing
L = Beijing
O = Node.js Test Server
OU = IT Department
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = 127.0.0.1
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
    
    # 生成自签名证书
    openssl req -new -x509 -key server.key -out server.crt -days 365 -config server.conf
    
    # 清理临时文件
    rm server.conf
    
    echo "✅ SSL 证书生成完成"
    echo "   - 私钥: server.key"
    echo "   - 证书: server.crt"
else
    echo "✅ SSL 证书已存在"
fi

# 检查端口是否已被占用
if ss -tlnp | grep -q ":8443 "; then
    echo "⚠️  端口 8443 已被占用"
    echo "正在尝试停止现有服务..."
    pkill -f "node.*https_server.js"
    sleep 2
fi

# 获取本机IP
LOCAL_IP=$(ip route get 1 2>/dev/null | awk '{print $7; exit}' || echo "localhost")

echo ""
echo "🚀 启动 Node.js HTTPS 测试服务器..."
echo ""
echo "📍 访问地址："
echo "   https://localhost:8443"
echo "   https://127.0.0.1:8443"
if [ "$LOCAL_IP" != "localhost" ]; then
    echo "   https://${LOCAL_IP}:8443"
fi
echo ""
echo "🔒 证书信息："
echo "   - 类型: 自签名证书"
echo "   - 有效期: 365天"
echo "   - CN: localhost"
echo "   - 端口: 8443"
echo ""
echo "⚠️  浏览器安全提示："
echo "   由于使用自签名证书，浏览器会显示安全警告"
echo "   请点击 '高级' → '继续访问' 来访问测试服务"
echo ""
echo "📊 API 接口："
echo "   - 10个测试接口已就绪"
echo "   - 包含2个大数据接口 (>2KB)"
echo "   - 支持 GET/POST 请求"
echo ""
echo "🛑 停止服务: 按 Ctrl+C"
echo "======================================="
echo ""

# 启动服务器
node https_server.js 