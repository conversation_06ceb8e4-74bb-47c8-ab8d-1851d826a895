<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Node.js HTTPS 测试服务</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { 
            max-width: 1000px; 
            margin: 0 auto; 
            background: white; 
            padding: 30px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 { 
            color: #2c3e50; 
            text-align: center; 
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .info-box { 
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); 
            padding: 20px; 
            border-radius: 10px; 
            margin: 20px 0; 
            border-left: 5px solid #3498db;
        }
        .test-section { 
            margin: 30px 0; 
            padding: 25px; 
            border: 2px solid #e1e8ed; 
            border-radius: 10px; 
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-top: 0;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        button { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        button:hover { 
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }
        button:active {
            transform: translateY(0);
        }
        .clear-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }
        .result { 
            margin-top: 15px; 
            padding: 15px; 
            border-radius: 8px; 
            border-left: 4px solid #3498db;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            border-left-color: #dc3545;
        }
        .log { 
            background: #f8f9fa; 
            padding: 15px; 
            border-radius: 8px; 
            font-family: 'Courier New', monospace; 
            max-height: 300px; 
            overflow-y: auto; 
            border: 1px solid #dee2e6;
            font-size: 12px;
            line-height: 1.4;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .api-description {
            color: #666; 
            margin-bottom: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .badge {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }
        .large-data-badge {
            background: #e74c3c;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #3498db;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Node.js HTTPS 测试服务</h1>
        
        <div class="info-box">
            <h3>✅ HTTPS 连接成功！</h3>
            <p>您已经成功通过 HTTPS 协议访问本 Node.js 服务器。</p>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">HTTPS</div>
                    <div class="stat-label">协议</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8443</div>
                    <div class="stat-label">端口</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10</div>
                    <div class="stat-label">API接口</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="currentTime"></div>
                    <div class="stat-label">访问时间</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 HTTPS 功能测试 (10个API接口)</h3>
            <div class="api-description">
                <strong>可用接口：</strong><br>
                <span class="badge">/api/test</span> 基础测试
                <span class="badge">/api/complex</span> 复杂数据处理
                <span class="badge">/api/users</span> 用户管理
                <span class="badge large-data-badge">/api/data</span> 大数据API (>2KB)
                <span class="badge">/api/status</span> 系统状态<br>
                <span class="badge">/api/config</span> 配置管理
                <span class="badge large-data-badge">/api/logs</span> 日志API (>2KB)
                <span class="badge">/api/metrics</span> 监控指标
                <span class="badge">/api/search</span> 搜索功能
                <span class="badge">/api/health</span> 健康检查
            </div>
            
            <div class="api-grid">
                <button onclick="testGet()">🔍 GET 请求测试</button>
                <button onclick="testPost()">📤 POST 请求测试</button>
                <button onclick="testJson()">📊 JSON 数据测试</button>
                <button onclick="testUsers()">👥 用户管理API</button>
                <button onclick="testLargeData()">💾 大数据API</button>
                <button onclick="testStatus()">📈 系统状态API</button>
                <button onclick="testConfig()">⚙️ 配置管理API</button>
                <button onclick="testLogs()">📝 日志API</button>
                <button onclick="testMetrics()">📊 监控指标API</button>
                <button onclick="testSearch()">🔎 搜索API</button>
                <button onclick="testHealth()">❤️ 健康检查API</button>
                <button onclick="clearLog()" class="clear-btn">🗑️ 清空日志</button>
            </div>
            
            <div id="testResult" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 请求日志</h3>
            <div id="requestLog" class="log">
                <p>等待请求...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 证书信息</h3>
            <div class="log">
                <p><strong>颁发者：</strong> Node.js Test Server</p>
                <p><strong>使用者：</strong> localhost</p>
                <p><strong>有效期：</strong> 365 天</p>
                <p><strong>算法：</strong> RSA-2048</p>
                <p><strong>端口：</strong> 8443</p>
                <p><strong>用途：</strong> eBPF HTTPS 流量分析测试</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 服务器信息</h3>
            <div class="log" id="serverInfo">
                <p>正在获取服务器信息...</p>
            </div>
        </div>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);

        let logCounter = 1;

        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }

        function addLog(message) {
            const logDiv = document.getElementById('requestLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] #${logCounter++} ${message}`;
            
            if (logDiv.innerHTML.includes('等待请求...')) {
                logDiv.innerHTML = '';
            }
            
            logDiv.innerHTML += logEntry + '<br>';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('requestLog').innerHTML = '<p>日志已清空</p>';
            logCounter = 1;
        }

        // 获取服务器信息
        function getServerInfo() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    const serverInfoDiv = document.getElementById('serverInfo');
                    serverInfoDiv.innerHTML = `
                        <p><strong>服务器名称：</strong> ${data.server.name}</p>
                        <p><strong>版本：</strong> ${data.server.version}</p>
                        <p><strong>运行时间：</strong> ${Math.floor(data.server.uptime)}秒</p>
                        <p><strong>Node.js版本：</strong> ${data.system.node_version}</p>
                        <p><strong>平台：</strong> ${data.system.platform}</p>
                        <p><strong>架构：</strong> ${data.system.arch}</p>
                        <p><strong>内存使用：</strong> ${Math.round(data.system.memory.heapUsed / 1024 / 1024)}MB</p>
                    `;
                })
                .catch(error => {
                    console.error('获取服务器信息失败:', error);
                });
        }

        // 页面加载时获取服务器信息
        window.onload = function() {
            getServerInfo();
        };

        function testGet() {
            addLog('发起 GET 请求测试...');
            fetch('/api/test?type=get&data=test_data&timestamp=' + Date.now())
                .then(response => response.json())
                .then(data => {
                    showResult('✅ GET 请求测试成功！服务器返回: ' + JSON.stringify(data, null, 2), true);
                    addLog('GET 请求测试成功');
                })
                .catch(error => {
                    showResult('❌ GET 请求测试失败: ' + error.message, false);
                    addLog('GET 请求测试失败');
                });
        }

        function testPost() {
            addLog('发起 POST 请求测试...');
            const testData = {
                test_type: 'POST',
                timestamp: new Date().toISOString(),
                data: 'This is test data for eBPF analysis',
                user_agent: navigator.userAgent
            };

            fetch('/api/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('✅ POST 请求测试成功！服务器返回: ' + JSON.stringify(data, null, 2), true);
                addLog('POST 请求测试成功');
            })
            .catch(error => {
                showResult('❌ POST 请求测试失败: ' + error.message, false);
                addLog('POST 请求测试失败');
            });
        }

        function testJson() {
            addLog('发起 JSON 数据测试...');
            const complexData = {
                user: {
                    id: 12345,
                    name: 'test_user',
                    permissions: ['read', 'write', 'admin']
                },
                session: {
                    token: 'abc123def456',
                    expires: new Date(Date.now() + 3600000).toISOString()
                },
                metadata: {
                    browser: navigator.userAgent,
                    timestamp: Date.now(),
                    test_purpose: 'eBPF HTTPS流量分析'
                }
            };

            fetch('/api/complex', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(complexData)
            })
            .then(response => response.json())
            .then(data => {
                showResult('✅ JSON 数据测试成功！数据大小: ' + data.processed_data.analysis.data_size + ' 字节', true);
                addLog('JSON 数据测试成功');
            })
            .catch(error => {
                showResult('❌ JSON 数据测试失败: ' + error.message, false);
                addLog('JSON 数据测试失败');
            });
        }

        function testUsers() {
            addLog('发起用户管理API测试...');
            fetch('/api/users')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ 用户管理API测试成功！获取到 ' + data.total + ' 个用户', true);
                    addLog('用户管理API测试成功');
                })
                .catch(error => {
                    showResult('❌ 用户管理API测试失败: ' + error.message, false);
                    addLog('用户管理API测试失败');
                });
        }

        function testLargeData() {
            addLog('发起大数据API测试...');
            fetch('/api/data')
                .then(response => response.json())
                .then(data => {
                    const dataSize = JSON.stringify(data).length;
                    showResult('✅ 大数据API测试成功！数据大小: ' + dataSize + ' 字节 (>2KB)', true);
                    addLog('大数据API测试成功 - ' + dataSize + ' 字节');
                })
                .catch(error => {
                    showResult('❌ 大数据API测试失败: ' + error.message, false);
                    addLog('大数据API测试失败');
                });
        }

        function testStatus() {
            addLog('发起系统状态API测试...');
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ 系统状态API测试成功！服务器运行时间: ' + Math.floor(data.server.uptime) + ' 秒', true);
                    addLog('系统状态API测试成功');
                })
                .catch(error => {
                    showResult('❌ 系统状态API测试失败: ' + error.message, false);
                    addLog('系统状态API测试失败');
                });
        }

        function testConfig() {
            addLog('发起配置管理API测试...');
            fetch('/api/config')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ 配置管理API测试成功！端口: ' + data.config.server.port, true);
                    addLog('配置管理API测试成功');
                })
                .catch(error => {
                    showResult('❌ 配置管理API测试失败: ' + error.message, false);
                    addLog('配置管理API测试失败');
                });
        }

        function testLogs() {
            addLog('发起日志API测试...');
            fetch('/api/logs')
                .then(response => response.json())
                .then(data => {
                    const dataSize = JSON.stringify(data).length;
                    showResult('✅ 日志API测试成功！获取到 ' + data.logs.length + ' 条日志，数据大小: ' + dataSize + ' 字节', true);
                    addLog('日志API测试成功 - ' + dataSize + ' 字节');
                })
                .catch(error => {
                    showResult('❌ 日志API测试失败: ' + error.message, false);
                    addLog('日志API测试失败');
                });
        }

        function testMetrics() {
            addLog('发起监控指标API测试...');
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ 监控指标API测试成功！总请求数: ' + data.metrics.requests.total, true);
                    addLog('监控指标API测试成功');
                })
                .catch(error => {
                    showResult('❌ 监控指标API测试失败: ' + error.message, false);
                    addLog('监控指标API测试失败');
                });
        }

        function testSearch() {
            addLog('发起搜索API测试...');
            fetch('/api/search?q=test')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ 搜索API测试成功！找到 ' + data.total + ' 个结果', true);
                    addLog('搜索API测试成功');
                })
                .catch(error => {
                    showResult('❌ 搜索API测试失败: ' + error.message, false);
                    addLog('搜索API测试失败');
                });
        }

        function testHealth() {
            addLog('发起健康检查API测试...');
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    showResult('✅ 健康检查API测试成功！服务器状态: ' + data.status, true);
                    addLog('健康检查API测试成功');
                })
                .catch(error => {
                    showResult('❌ 健康检查API测试失败: ' + error.message, false);
                    addLog('健康检查API测试失败');
                });
        }
    </script>
</body>
</html> 