#!/usr/bin/env node
// -*- coding: utf-8 -*-

const express = require('express');
const https = require('https');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 8443;

// 中间件
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// 请求日志中间件
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`);
    next();
});

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API 路由 1: 基础测试接口
app.get('/api/test', (req, res) => {
    const response = {
        status: 'success',
        message: 'GET请求测试成功',
        timestamp: new Date().toISOString(),
        query: req.query,
        server: 'Node.js HTTPS Test Server',
        port: PORT
    };
    res.json(response);
});

app.post('/api/test', (req, res) => {
    const response = {
        status: 'success',
        message: 'POST请求测试成功',
        timestamp: new Date().toISOString(),
        received_data: req.body,
        server: 'Node.js HTTPS Test Server',
        port: PORT
    };
    res.json(response);
});

// API 路由 2: 复杂数据接口
app.post('/api/complex', (req, res) => {
    const response = {
        status: 'success',
        message: '复杂数据处理成功',
        timestamp: new Date().toISOString(),
        processed_data: {
            original: req.body,
            analysis: {
                data_size: JSON.stringify(req.body).length,
                has_user: !!req.body.user,
                has_session: !!req.body.session,
                fields_count: Object.keys(req.body).length
            }
        },
        server_info: {
            node_version: process.version,
            platform: process.platform,
            memory_usage: process.memoryUsage()
        }
    };
    res.json(response);
});

// API 路由 3: 用户管理接口
app.get('/api/users', (req, res) => {
    const users = [
        { id: 1, name: 'admin', role: 'administrator', last_login: new Date().toISOString() },
        { id: 2, name: 'user1', role: 'user', last_login: new Date(Date.now() - 86400000).toISOString() },
        { id: 3, name: 'test_user', role: 'tester', last_login: new Date(Date.now() - 3600000).toISOString() },
        { id: 4, name: 'api_user', role: 'api', last_login: new Date(Date.now() - 7200000).toISOString() },
        { id: 5, name: 'guest', role: 'guest', last_login: new Date(Date.now() - 172800000).toISOString() }
    ];
    
    res.json({
        status: 'success',
        data: users,
        total: users.length,
        timestamp: new Date().toISOString()
    });
});

app.post('/api/users', (req, res) => {
    const newUser = {
        id: Math.floor(Math.random() * 10000),
        ...req.body,
        created_at: new Date().toISOString(),
        status: 'active'
    };
    
    res.json({
        status: 'success',
        message: '用户创建成功',
        data: newUser,
        timestamp: new Date().toISOString()
    });
});

// API 路由 4: 大数据接口 (>2KB)
app.get('/api/data', (req, res) => {
    const largeData = {
        status: 'success',
        message: '大数据API测试',
        timestamp: new Date().toISOString(),
        data: {
            records: [],
            metadata: {
                total_records: 100,
                page_size: 100,
                generated_at: new Date().toISOString(),
                server_info: {
                    node_version: process.version,
                    platform: process.platform,
                    arch: process.arch,
                    memory_usage: process.memoryUsage(),
                    uptime: process.uptime()
                }
            }
        }
    };
    
    // 生成大量数据以确保响应大于2KB
    for (let i = 1; i <= 100; i++) {
        largeData.data.records.push({
            id: i,
            name: `Record_${i}`,
            type: ['Type_A', 'Type_B', 'Type_C'][i % 3],
            value: Math.random() * 1000,
            description: `这是第${i}条测试记录，用于生成大量数据进行eBPF流量分析测试。包含中文字符以增加数据复杂性。`,
            tags: [`tag_${i}`, `category_${i % 10}`, `group_${i % 5}`],
            created_at: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
            updated_at: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
            metadata: {
                priority: Math.floor(Math.random() * 5) + 1,
                status: ['active', 'inactive', 'pending'][Math.floor(Math.random() * 3)],
                extra_data: `额外数据_${i}_${Math.random().toString(36).substring(2, 15)}`
            }
        });
    }
    
    res.json(largeData);
});

// API 路由 5: 系统状态接口
app.get('/api/status', (req, res) => {
    const status = {
        status: 'success',
        server: {
            name: 'Node.js HTTPS Test Server',
            version: '1.0.0',
            port: PORT,
            protocol: 'HTTPS',
            uptime: process.uptime(),
            timestamp: new Date().toISOString()
        },
        system: {
            platform: process.platform,
            arch: process.arch,
            node_version: process.version,
            memory: process.memoryUsage(),
            cpu_usage: process.cpuUsage()
        },
        ssl: {
            enabled: true,
            certificate_type: 'self-signed',
            key_algorithm: 'RSA',
            validity_days: 365
        },
        endpoints: {
            total: 10,
            active: 10,
            list: [
                '/api/test',
                '/api/complex',
                '/api/users',
                '/api/data',
                '/api/status',
                '/api/config',
                '/api/logs',
                '/api/metrics',
                '/api/search',
                '/api/health'
            ]
        }
    };
    
    res.json(status);
});

// API 路由 6: 配置管理接口
app.get('/api/config', (req, res) => {
    const config = {
        status: 'success',
        config: {
            server: {
                port: PORT,
                protocol: 'https',
                max_connections: 1000,
                timeout: 30000,
                keep_alive: true
            },
            ssl: {
                enabled: true,
                certificate_path: './server.crt',
                key_path: './server.key',
                cipher_suites: ['TLS_AES_256_GCM_SHA384', 'TLS_CHACHA20_POLY1305_SHA256'],
                min_version: 'TLSv1.2'
            },
            logging: {
                level: 'info',
                format: 'json',
                file: './logs/server.log',
                max_size: '10MB',
                max_files: 5
            },
            security: {
                cors_enabled: true,
                rate_limiting: {
                    enabled: true,
                    max_requests: 100,
                    window_ms: 60000
                }
            }
        },
        timestamp: new Date().toISOString()
    };
    
    res.json(config);
});

// API 路由 7: 日志接口 (>2KB)
app.get('/api/logs', (req, res) => {
    const logs = {
        status: 'success',
        message: '日志获取成功',
        timestamp: new Date().toISOString(),
        logs: []
    };
    
    // 生成大量日志数据
    const logLevels = ['INFO', 'WARN', 'ERROR', 'DEBUG'];
    const logSources = ['AUTH', 'API', 'DB', 'CACHE', 'NETWORK'];
    
    for (let i = 1; i <= 50; i++) {
        const level = logLevels[Math.floor(Math.random() * logLevels.length)];
        const source = logSources[Math.floor(Math.random() * logSources.length)];
        
        logs.logs.push({
            id: i,
            timestamp: new Date(Date.now() - Math.random() * 86400000).toISOString(),
            level: level,
            source: source,
            message: `${level} 级别日志消息 #${i} - 来自 ${source} 模块的详细信息，用于eBPF流量分析测试。`,
            details: {
                request_id: `req_${Math.random().toString(36).substring(2, 15)}`,
                user_id: Math.floor(Math.random() * 1000),
                ip_address: `192.168.1.${Math.floor(Math.random() * 255)}`,
                response_time: Math.floor(Math.random() * 1000) + 'ms',
                status_code: [200, 201, 400, 401, 404, 500][Math.floor(Math.random() * 6)]
            }
        });
    }
    
    res.json(logs);
});

// API 路由 8: 监控指标接口
app.get('/api/metrics', (req, res) => {
    const metrics = {
        status: 'success',
        timestamp: new Date().toISOString(),
        metrics: {
            server: {
                uptime: process.uptime(),
                memory_usage: process.memoryUsage(),
                cpu_usage: process.cpuUsage()
            },
            requests: {
                total: Math.floor(Math.random() * 10000),
                success: Math.floor(Math.random() * 9000),
                errors: Math.floor(Math.random() * 1000)
            },
            ssl: {
                handshakes_total: Math.floor(Math.random() * 5000),
                certificate_expiry_days: 364
            }
        }
    };
    
    res.json(metrics);
});

// API 路由 9: 搜索接口
app.get('/api/search', (req, res) => {
    const query = req.query.q || '';
    const results = [];
    
    if (query) {
        const categories = ['用户', '文档', '配置', '日志', '系统'];
        for (let i = 1; i <= 20; i++) {
            const category = categories[i % categories.length];
            results.push({
                id: i,
                title: `${category}搜索结果 ${i} - ${query}`,
                description: `这是关于"${query}"的搜索结果描述。`,
                category: category,
                score: Math.random(),
                created_at: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString()
            });
        }
    }
    
    res.json({
        status: 'success',
        query: query,
        results: results,
        total: results.length,
        timestamp: new Date().toISOString()
    });
});

// API 路由 10: 健康检查接口
app.get('/api/health', (req, res) => {
    const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        server: {
            name: 'Node.js HTTPS Test Server',
            version: '1.0.0',
            uptime: process.uptime(),
            pid: process.pid
        },
        checks: {
            memory: {
                status: 'ok',
                usage: process.memoryUsage()
            },
            ssl: {
                status: 'ok',
                certificate_valid: true,
                expires_in_days: 364
            },
            network: {
                status: 'ok',
                port: PORT,
                protocol: 'HTTPS'
            }
        }
    };
    
    res.json(health);
});

// 404 处理
app.use((req, res) => {
    res.status(404).json({
        status: 'error',
        message: '接口不存在',
        path: req.path,
        timestamp: new Date().toISOString()
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        status: 'error',
        message: '服务器内部错误',
        timestamp: new Date().toISOString()
    });
});

// 创建 HTTPS 服务器
function createHTTPSServer() {
    try {
        const options = {
            key: fs.readFileSync(path.join(__dirname, 'server.key')),
            cert: fs.readFileSync(path.join(__dirname, 'server.crt'))
        };
        

        
        const server = https.createServer(options, app);
        
        server.listen(PORT, () => {
            console.log('🔒 Node.js HTTPS 测试服务器已启动');
            console.log('================================');
            console.log(`📍 服务器地址: https://localhost:${PORT}`);
            console.log(`📍 服务器地址: https://127.0.0.1:${PORT}`);
            console.log('🔒 使用自签名证书');
            console.log('📊 10个API接口已就绪');
            console.log('⚠️  浏览器会显示安全警告，请选择继续访问');
            console.log('🛑 停止服务: 按 Ctrl+C');
            console.log('================================');
        });
        
        return server;
    } catch (error) {
        console.error('❌ 启动服务器失败:', error.message);
        console.error('请确保 server.key 和 server.crt 文件存在');
        process.exit(1);
    }
}

// 启动服务器
if (require.main === module) {
    createHTTPSServer();
}

module.exports = app; 