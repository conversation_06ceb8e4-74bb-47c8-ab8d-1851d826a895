# Node.js HTTPS 测试服务器

一个基于 Node.js 的 HTTPS 测试服务器，专门用于 eBPF HTTPS 流量分析测试。

## 🌟 特性

- ✅ **HTTPS 加密通信** - 使用自签名证书提供完整的 SSL/TLS 加密
- 🚀 **10个丰富的API接口** - 涵盖各种测试场景
- 📊 **大数据支持** - 包含2个返回>2KB数据的接口
- 🎨 **现代化Web界面** - 美观的测试界面，支持实时测试
- 🔧 **自动化脚本** - 一键启动、停止和测试
- 📈 **实时监控** - 请求日志和系统状态监控

## 🏗️ 系统要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- OpenSSL (用于生成SSL证书)
- Linux/macOS/Windows

## 📦 快速开始

### 1. 安装和启动

```bash
# 进入项目目录
cd nodejs-test-server

# 给脚本添加执行权限
chmod +x start_https_server.sh stop_https_server.sh

# 启动服务器 (会自动安装依赖和生成证书)
./start_https_server.sh
```

### 2. 访问服务

服务器启动后，通过以下地址访问：

- **主页**: https://localhost:8443
- **API根路径**: https://localhost:8443/api/

⚠️ **注意**: 由于使用自签名证书，浏览器会显示安全警告，请选择"高级" → "继续访问"。

### 3. 测试API

```bash
# 运行自动化测试
node test_apis.js

# 或者使用 npm 脚本
npm test
```

### 4. 停止服务

```bash
./stop_https_server.sh
```

## 🔌 API 接口

服务器提供以下10个API接口：

| 接口 | 方法 | 描述 | 数据大小 |
|------|------|------|----------|
| `/api/test` | GET/POST | 基础测试接口 | < 1KB |
| `/api/complex` | POST | 复杂数据处理 | < 1KB |
| `/api/users` | GET/POST | 用户管理 | < 1KB |
| `/api/data` | GET | **大数据接口** | **> 2KB** |
| `/api/status` | GET | 系统状态 | < 1KB |
| `/api/config` | GET | 配置管理 | < 1KB |
| `/api/logs` | GET | **日志接口** | **> 2KB** |
| `/api/metrics` | GET | 监控指标 | < 1KB |
| `/api/search` | GET | 搜索功能 | < 1KB |
| `/api/health` | GET | 健康检查 | < 1KB |

## 📊 使用示例

### 命令行测试

```bash
# GET 请求测试
curl -k https://localhost:8443/api/test?type=get&data=test

# POST 请求测试
curl -k -X POST https://localhost:8443/api/test \
  -H "Content-Type: application/json" \
  -d '{"test_type":"POST","data":"test data"}'

# 获取大数据
curl -k https://localhost:8443/api/data > large_response.json
```

### JavaScript 测试

```javascript
// 忽略自签名证书 (仅测试环境)
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const https = require('https');

const options = {
  hostname: 'localhost',
  port: 8443,
  path: '/api/test',
  method: 'GET',
  rejectUnauthorized: false
};

const req = https.request(options, (res) => {
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  res.on('end', () => {
    console.log(JSON.parse(data));
  });
});

req.end();
```

## 🔒 SSL 证书

服务器使用自签名证书，证书信息：

- **颁发者**: Node.js Test Server
- **使用者**: localhost
- **有效期**: 365天
- **算法**: RSA-2048
- **支持域名**: localhost, 127.0.0.1

证书文件：
- `server.key` - 私钥
- `server.crt` - 证书

## 📁 项目结构

```
nodejs-test-server/
├── https_server.js          # 主服务器文件
├── package.json             # 项目配置
├── start_https_server.sh    # 启动脚本
├── stop_https_server.sh     # 停止脚本
├── test_apis.js            # API测试脚本
├── public/                 # 静态文件目录
│   └── index.html          # Web界面
├── server.key              # SSL私钥 (自动生成)
├── server.crt              # SSL证书 (自动生成)
└── README.md               # 说明文档
```

## 🛠️ 开发模式

```bash
# 安装开发依赖
npm install

# 使用 nodemon 自动重启
npm run dev
```

## 🐛 故障排除

### 端口占用

如果端口8443被占用：

```bash
# 查看端口占用
ss -tlnp | grep :8443

# 强制停止服务
./stop_https_server.sh
```

### 证书问题

重新生成证书：

```bash
# 删除旧证书
rm server.key server.crt

# 重新启动服务器会自动生成新证书
./start_https_server.sh
```

### 依赖问题

```bash
# 清理并重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

## 🎯 eBPF 测试场景

此服务器特别适用于以下 eBPF 测试场景：

1. **HTTPS 流量捕获** - 测试 eBPF 程序捕获加密流量的能力
2. **SSL/TLS 握手分析** - 观察SSL握手过程
3. **大数据传输** - 测试大包处理能力
4. **请求响应模式** - 分析HTTP请求响应模式
5. **性能测试** - 压力测试和性能分析

## 📝 日志

服务器会在控制台输出详细的请求日志：

```
[2024-01-01T12:00:00.000Z] GET /api/test - ::1
[2024-01-01T12:00:01.000Z] POST /api/users - ::1
```

## 🤝 贡献

欢迎提交问题和改进建议！

## �� 许可证

MIT License 