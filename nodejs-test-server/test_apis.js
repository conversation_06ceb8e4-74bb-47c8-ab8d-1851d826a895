#!/usr/bin/env node

const https = require('https');
const fs = require('fs');

// 忽略自签名证书警告
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const BASE_URL = 'https://localhost:8443';
const TEST_DELAY = 500; // 每个测试之间的延迟（毫秒）

console.log('🧪 Node.js HTTPS API 测试工具');
console.log('===============================');
console.log(`目标服务器: ${BASE_URL}`);
console.log('');

// 通用请求函数
function makeRequest(method, path, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
        const url = new URL(BASE_URL + path);
        
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'User-Agent': 'Node.js Test Client/1.0',
                ...headers
            },
            rejectUnauthorized: false // 忽略自签名证书
        };
        
        if (data) {
            const postData = JSON.stringify(data);
            options.headers['Content-Type'] = 'application/json';
            options.headers['Content-Length'] = Buffer.byteLength(postData);
        }
        
        const req = https.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(body);
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: jsonData,
                        size: Buffer.byteLength(body)
                    });
                } catch (e) {
                    resolve({
                        status: res.statusCode,
                        headers: res.headers,
                        data: body,
                        size: Buffer.byteLength(body)
                    });
                }
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (data) {
            req.write(JSON.stringify(data));
        }
        
        req.end();
    });
}

// 测试函数
async function runTest(name, testFunc) {
    try {
        console.log(`🔍 测试: ${name}`);
        const startTime = Date.now();
        const result = await testFunc();
        const duration = Date.now() - startTime;
        
        console.log(`✅ ${name} - 成功 (${duration}ms, ${result.size} bytes)`);
        if (result.status !== 200) {
            console.log(`   状态码: ${result.status}`);
        }
        return true;
    } catch (error) {
        console.log(`❌ ${name} - 失败: ${error.message}`);
        return false;
    }
}

// 测试用例
const tests = [
    {
        name: 'GET /api/test - 基础测试',
        func: () => makeRequest('GET', '/api/test?type=get&data=test_data')
    },
    {
        name: 'POST /api/test - 基础测试',
        func: () => makeRequest('POST', '/api/test', {
            test_type: 'POST',
            timestamp: new Date().toISOString(),
            data: 'This is test data for eBPF analysis'
        })
    },
    {
        name: 'POST /api/complex - 复杂数据处理',
        func: () => makeRequest('POST', '/api/complex', {
            user: {
                id: 12345,
                name: 'test_user',
                permissions: ['read', 'write', 'admin']
            },
            session: {
                token: 'abc123def456',
                expires: new Date(Date.now() + 3600000).toISOString()
            },
            metadata: {
                client: 'Node.js Test Client',
                timestamp: Date.now(),
                test_purpose: 'eBPF HTTPS流量分析'
            }
        })
    },
    {
        name: 'GET /api/users - 用户管理',
        func: () => makeRequest('GET', '/api/users')
    },
    {
        name: 'POST /api/users - 创建用户',
        func: () => makeRequest('POST', '/api/users', {
            name: 'test_user_' + Date.now(),
            email: '<EMAIL>',
            role: 'tester'
        })
    },
    {
        name: 'GET /api/data - 大数据API (>2KB)',
        func: () => makeRequest('GET', '/api/data')
    },
    {
        name: 'GET /api/status - 系统状态',
        func: () => makeRequest('GET', '/api/status')
    },
    {
        name: 'GET /api/config - 配置管理',
        func: () => makeRequest('GET', '/api/config')
    },
    {
        name: 'GET /api/logs - 日志API (>2KB)',
        func: () => makeRequest('GET', '/api/logs')
    },
    {
        name: 'GET /api/metrics - 监控指标',
        func: () => makeRequest('GET', '/api/metrics')
    },
    {
        name: 'GET /api/search - 搜索功能',
        func: () => makeRequest('GET', '/api/search?q=test')
    },
    {
        name: 'GET /api/health - 健康检查',
        func: () => makeRequest('GET', '/api/health')
    },
    {
        name: 'GET /nonexistent - 404 测试',
        func: () => makeRequest('GET', '/nonexistent')
    }
];

// 运行所有测试
async function runAllTests() {
    console.log(`开始运行 ${tests.length} 个测试...\n`);
    
    let passed = 0;
    let failed = 0;
    
    for (let i = 0; i < tests.length; i++) {
        const test = tests[i];
        const success = await runTest(test.name, test.func);
        
        if (success) {
            passed++;
        } else {
            failed++;
        }
        
        // 添加延迟，避免请求过于密集
        if (i < tests.length - 1) {
            await new Promise(resolve => setTimeout(resolve, TEST_DELAY));
        }
    }
    
    console.log('\n===============================');
    console.log('📊 测试结果统计:');
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`📈 成功率: ${((passed / tests.length) * 100).toFixed(1)}%`);
    console.log('===============================');
    
    if (failed === 0) {
        console.log('🎉 所有测试通过！服务器运行正常。');
        process.exit(0);
    } else {
        console.log('⚠️  部分测试失败，请检查服务器状态。');
        process.exit(1);
    }
}

// 检查服务器是否运行
async function checkServer() {
    try {
        console.log('🔍 检查服务器连接...');
        await makeRequest('GET', '/api/health');
        console.log('✅ 服务器连接正常\n');
        return true;
    } catch (error) {
        console.log('❌ 无法连接到服务器:', error.message);
        console.log('请确保服务器正在运行在', BASE_URL);
        console.log('使用命令启动服务器: ./start_https_server.sh');
        return false;
    }
}

// 主函数
async function main() {
    const serverReady = await checkServer();
    if (!serverReady) {
        process.exit(1);
    }
    
    await runAllTests();
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
} 