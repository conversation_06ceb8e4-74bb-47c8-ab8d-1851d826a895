#!/bin/bash

echo "🛑 Node.js HTTPS 测试服务器停止脚本"
echo "======================================"

# 查找运行中的 Node.js HTTPS 服务器进程
PIDS=$(pgrep -f "node.*https_server.js")

if [ -z "$PIDS" ]; then
    echo "📋 未找到运行中的 Node.js HTTPS 测试服务器进程"
    echo ""
    
    # 检查端口占用情况
    if ss -tlnp | grep -q ":8443 "; then
        echo "⚠️  但是端口 8443 仍被占用，正在查找占用进程..."
        PORT_PIDS=$(ss -tlnp | grep ":8443 " | awk '{print $6}' | cut -d',' -f2 | cut -d'=' -f2)
        if [ ! -z "$PORT_PIDS" ]; then
            echo "发现占用端口 8443 的进程: $PORT_PIDS"
            echo "正在终止这些进程..."
            for pid in $PORT_PIDS; do
                if kill -0 $pid 2>/dev/null; then
                    echo "终止进程 $pid..."
                    kill -TERM $pid
                    sleep 2
                    if kill -0 $pid 2>/dev/null; then
                        echo "强制终止进程 $pid..."
                        kill -KILL $pid
                    fi
                fi
            done
        fi
    else
        echo "✅ 端口 8443 未被占用"
    fi
else
    echo "📋 找到以下 Node.js HTTPS 测试服务器进程:"
    for pid in $PIDS; do
        echo "   PID: $pid - $(ps -p $pid -o cmd --no-headers)"
    done
    echo ""
    
    echo "🔄 正在停止服务器..."
    
    # 先尝试优雅关闭
    for pid in $PIDS; do
        if kill -0 $pid 2>/dev/null; then
            echo "发送 SIGTERM 信号到进程 $pid..."
            kill -TERM $pid
        fi
    done
    
    # 等待进程结束
    sleep 3
    
    # 检查是否还有进程在运行
    REMAINING_PIDS=$(pgrep -f "node.*https_server.js")
    if [ ! -z "$REMAINING_PIDS" ]; then
        echo "⚠️  部分进程仍在运行，强制终止..."
        for pid in $REMAINING_PIDS; do
            if kill -0 $pid 2>/dev/null; then
                echo "强制终止进程 $pid..."
                kill -KILL $pid
            fi
        done
        sleep 1
    fi
    
    # 最终检查
    FINAL_CHECK=$(pgrep -f "node.*https_server.js")
    if [ -z "$FINAL_CHECK" ]; then
        echo "✅ 所有 Node.js HTTPS 测试服务器进程已成功停止"
    else
        echo "❌ 部分进程可能仍在运行: $FINAL_CHECK"
        echo "请手动检查并终止这些进程"
    fi
fi

echo ""

# 检查端口状态
if ss -tlnp | grep -q ":8443 "; then
    echo "⚠️  端口 8443 仍被占用"
    echo "占用详情:"
    ss -tlnp | grep ":8443 "
else
    echo "✅ 端口 8443 已释放"
fi

echo ""

# 清理相关文件（可选）
echo "🧹 清理选项:"
echo "1. 保留所有文件（推荐）"
echo "2. 删除 SSL 证书文件"
echo "3. 删除 node_modules 目录"
echo "4. 删除 SSL 证书和 node_modules"
echo ""
read -p "请选择 (1-4, 默认为1): " choice

case $choice in
    2)
        if [ -f "server.crt" ] || [ -f "server.key" ]; then
            echo "删除 SSL 证书文件..."
            rm -f server.crt server.key server.conf
            echo "✅ SSL 证书文件已删除"
        else
            echo "未找到 SSL 证书文件"
        fi
        ;;
    3)
        if [ -d "node_modules" ]; then
            echo "删除 node_modules 目录..."
            rm -rf node_modules
            echo "✅ node_modules 目录已删除"
        else
            echo "未找到 node_modules 目录"
        fi
        ;;
    4)
        echo "删除 SSL 证书文件和 node_modules 目录..."
        rm -f server.crt server.key server.conf
        rm -rf node_modules
        echo "✅ SSL 证书文件和 node_modules 目录已删除"
        ;;
    *)
        echo "保留所有文件"
        ;;
esac

echo ""
echo "🏁 停止脚本执行完毕"
echo "======================================" 