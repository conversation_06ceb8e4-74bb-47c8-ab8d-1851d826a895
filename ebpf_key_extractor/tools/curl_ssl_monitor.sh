#!/bin/bash

# curl SSL监控脚本
# 使用ftrace uprobe监控curl的SSL函数调用

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🚀 curl SSL监控器 v1.0"
echo "====================="

# 检查权限
if [[ $EUID -ne 0 ]]; then
   echo "❌ 需要root权限运行"
   exit 1
fi

# 检查内核调试支持
if [[ ! -d "/sys/kernel/debug/tracing" ]]; then
    echo "❌ 内核调试文件系统未挂载"
    echo "请运行: mount -t debugfs debugfs /sys/kernel/debug"
    exit 1
fi

# 清理函数
cleanup() {
    echo ""
    echo "🧹 清理uprobe事件..."
    echo 0 > /sys/kernel/debug/tracing/tracing_on 2>/dev/null || true
    echo 0 > /sys/kernel/debug/tracing/events/uprobes/curl_ssl_ctx_new/enable 2>/dev/null || true
    echo 0 > /sys/kernel/debug/tracing/events/uprobes/curl_ssl_write/enable 2>/dev/null || true
    echo '-:curl_ssl_ctx_new' >> /sys/kernel/debug/tracing/uprobe_events 2>/dev/null || true
    echo '-:curl_ssl_write' >> /sys/kernel/debug/tracing/uprobe_events 2>/dev/null || true
    echo "✅ 清理完成"
}

# 注册清理函数
trap cleanup EXIT INT TERM

# 获取SSL库路径
SSL_LIB="/lib64/libssl.so.1.1"
if [[ ! -f "$SSL_LIB" ]]; then
    SSL_LIB="/usr/lib64/libssl.so.1.1"
    if [[ ! -f "$SSL_LIB" ]]; then
        echo "❌ 找不到SSL库文件"
        exit 1
    fi
fi

echo "✅ 使用SSL库: $SSL_LIB"

# 获取函数偏移地址
SSL_CTX_NEW_OFFSET=$(objdump -T "$SSL_LIB" | grep "SSL_CTX_new" | awk '{print $1}')
SSL_WRITE_OFFSET=$(objdump -T "$SSL_LIB" | grep -w "SSL_write" | awk '{print $1}')

if [[ -z "$SSL_CTX_NEW_OFFSET" ]] || [[ -z "$SSL_WRITE_OFFSET" ]]; then
    echo "❌ 无法获取SSL函数偏移地址"
    exit 1
fi

echo "✅ SSL_CTX_new偏移: 0x$SSL_CTX_NEW_OFFSET"
echo "✅ SSL_write偏移: 0x$SSL_WRITE_OFFSET"

# 停止追踪并清空缓冲区
echo 0 > /sys/kernel/debug/tracing/tracing_on
echo > /sys/kernel/debug/tracing/trace

# 创建uprobe事件
echo "📡 创建uprobe事件..."
echo "p:curl_ssl_ctx_new $SSL_LIB:0x$SSL_CTX_NEW_OFFSET" >> /sys/kernel/debug/tracing/uprobe_events
echo "p:curl_ssl_write $SSL_LIB:0x$SSL_WRITE_OFFSET" >> /sys/kernel/debug/tracing/uprobe_events

# 启用事件
echo 1 > /sys/kernel/debug/tracing/events/uprobes/curl_ssl_ctx_new/enable
echo 1 > /sys/kernel/debug/tracing/events/uprobes/curl_ssl_write/enable

# 启用追踪
echo 1 > /sys/kernel/debug/tracing/tracing_on

echo "✅ uprobe设置完成"
echo ""
echo "🎯 开始监听curl SSL事件..."
echo "🧪 在另一个终端运行: curl -k https://httpbin.org/get"
echo "💡 使用 Ctrl+C 停止监听"
echo ""

# 监听trace输出
cat /sys/kernel/debug/tracing/trace_pipe | while read line; do
    if [[ "$line" == *"curl_ssl_"* ]]; then
        timestamp=$(echo "$line" | awk '{print $4}')
        pid=$(echo "$line" | awk '{print $2}' | cut -d'-' -f2)
        event=$(echo "$line" | awk '{print $5}' | cut -d':' -f1)
        
        case "$event" in
            "curl_ssl_ctx_new")
                echo "🔐 [$timestamp] PID=$pid curl调用SSL_CTX_new - SSL上下文创建"
                ;;
            "curl_ssl_write")
                echo "📤 [$timestamp] PID=$pid curl调用SSL_write - 发送加密数据"
                ;;
        esac
    fi
done 