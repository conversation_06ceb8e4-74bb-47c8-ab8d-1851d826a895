# 密钥派生拦截器可行性分析报告

## 🎯 分析目标

基于《思路.txt》中的密钥派生拦截方案，探索在当前系统环境下的实际可行性。

## 📊 环境分析

### 系统环境
- **操作系统**: CentOS 8 (Linux 4.18.0-408.el8.x86_64)
- **SSL库**: `/lib64/libssl.so.1.1` (OpenSSL 1.1.x)
- **编译环境**: gcc + clang 可用
- **BPF支持**: 内核支持BPF，但缺少现代libbpf用户空间库

### 技术栈限制
- ❌ **缺少libbpf-devel包**: 系统仓库中不提供现代libbpf开发包
- ✅ **内核BPF支持**: 支持基础BPF功能和uprobe
- ✅ **SSL库可用**: OpenSSL 1.1.x库可访问

## 🔍 目标函数分析

### 理想目标函数（思路.txt中提出的）

| 函数类型 | 函数名 | 在当前SSL库中的状态 | 可行性 |
|---------|--------|------------------|--------|
| **HKDF (TLS 1.3)** | `HKDF_extract`, `HKDF_expand` | ❌ 未找到导出符号 | 低 |
| **PRF (TLS 1.2)** | `tls1_PRF`, `PRF` | ❌ 未找到导出符号 | 低 |
| **P_hash** | `tls1_P_hash`, `P_hash` | ❌ 未找到导出符号 | 低 |

**原因分析**: 这些函数可能是OpenSSL的内部实现函数，没有导出为公共API。

### 实际可用的替代函数

| 函数类型 | 函数名 | 状态 | 拦截价值 | 可行性 |
|---------|--------|------|----------|--------|
| **随机数获取** | `SSL_get_client_random` | ✅ 存在 | ⭐⭐⭐⭐⭐ | 高 |
| **随机数获取** | `SSL_get_server_random` | ✅ 存在 | ⭐⭐⭐⭐⭐ | 高 |
| **密钥日志** | `SSL_CTX_get_keylog_callback` | ✅ 存在 | ⭐⭐⭐⭐ | 中 |
| **会话管理** | `SSL_CTX_add_session` | ✅ 存在 | ⭐⭐⭐ | 中 |
| **握手随机数** | `SSL_client_hello_get0_random` | ✅ 存在 | ⭐⭐⭐⭐ | 中 |

## 💡 修正后的技术方案

### 方案1: SSL API层拦截 ⭐⭐⭐⭐ **推荐**

**核心思路**: 拦截SSL库的公共API函数，这些函数在密钥协商过程中会被调用

```c
// 主要拦截点
1. SSL_get_client_random() - 获取客户端随机数
2. SSL_get_server_random() - 获取服务端随机数  
3. SSL_CTX_get_keylog_callback() - 获取密钥日志回调
4. SSL_do_handshake() - 监控握手完成
```

**优势**:
- ✅ 函数确实存在于SSL库中
- ✅ 技术成熟，uprobe拦截API函数风险较低
- ✅ 可以获取到关键的随机数数据
- ✅ 结合SSL keylog机制，可能获取完整密钥

**劣势**:
- ❌ 不能直接获取master secret
- ❌ 需要额外的密钥推导计算

### 方案2: 内存模式识别 ⭐⭐

**核心思路**: 在SSL握手完成后，扫描进程内存寻找特定的密钥模式

**优势**:
- ✅ 不依赖特定函数存在
- ✅ 理论上可以找到任何存储的密钥

**劣势**:
- ❌ 技术复杂度高
- ❌ 容易误判和漏判
- ❌ 性能开销大

### 方案3: 混合拦截策略 ⭐⭐⭐

**核心思路**: 结合API拦截和关键数据结构访问

```c
// 拦截策略
1. SSL_get_*_random() -> 获取随机数
2. SSL_do_handshake() -> 触发时机
3. 内存扫描 -> 查找master secret
4. 自动生成keylog -> 标准输出
```

## 🧪 可行性测试结果

### 编译环境测试
- **结果**: ❌ 失败
- **原因**: 缺少libbpf-devel包
- **解决方案**: 
  1. 使用内核提供的基础BPF接口
  2. 或手动编译安装libbpf库

### 函数存在性验证
- **SSL_get_client_random**: ✅ 存在
- **SSL_get_server_random**: ✅ 存在  
- **密钥派生函数**: ❌ 不存在公共导出

### 技术路线调整建议

基于分析结果，建议采用以下技术路线：

#### 阶段1: API层拦截验证 (1周)
```bash
目标: 验证SSL API拦截的可行性
任务:
1. 解决libbpf编译环境问题
2. 实现SSL_get_*_random函数拦截  
3. 验证能否获取到随机数数据
```

#### 阶段2: 密钥推导实现 (2周)
```bash
目标: 基于随机数推导出master secret
任务:
1. 实现TLS PRF算法（用户态）
2. 结合pre-master secret（如果能获取）
3. 生成标准keylog格式
```

#### 阶段3: 生产优化 (1周)
```bash
目标: 性能优化和稳定性改进
任务:
1. 减少拦截开销
2. 提高密钥提取成功率
3. 异常处理和恢复机制
```

## 📊 最终可行性评估

| 评估维度 | 评分 | 说明 |
|---------|------|------|
| **技术可行性** | ⭐⭐⭐ | SSL API拦截技术成熟，但需要额外的密钥推导 |
| **实现复杂度** | ⭐⭐⭐ | 中等复杂度，需要理解TLS协议细节 |
| **部署便利性** | ⭐⭐ | 需要解决编译环境问题 |
| **兼容性** | ⭐⭐⭐⭐ | SSL API相对稳定，兼容性较好 |
| **性能影响** | ⭐⭐⭐⭐ | API拦截性能开销较小 |

**综合评分: ⭐⭐⭐ (3/5) - 部分可行**

## 🎯 结论和建议

### 主要结论
1. **原始方案调整**: 《思路.txt》中的HKDF/PRF函数拦截方案在当前环境下不可行，因为这些函数未导出
2. **替代方案可行**: SSL API层拦截方案具有可行性，可以获取到关键的随机数数据
3. **技术挑战**: 主要挑战在于从随机数推导出完整的master secret

### 建议方案

**短期方案**: 实现SSL API拦截器
- 拦截`SSL_get_client_random`和`SSL_get_server_random`
- 结合SSLKEYLOGFILE机制补充master secret
- 生成标准keylog格式

**长期方案**: 探索内部函数拦截
- 通过静态分析找到内部密钥派生函数
- 使用符号表或内存地址直接拦截
- 提高密钥提取的完整性

### 技术路线推荐

根据当前分析结果，推荐采用 **"SSL API拦截 + 密钥推导"** 的混合方案：

1. **立即可行**: 拦截SSL_get_*_random函数获取随机数
2. **中期目标**: 实现用户态TLS密钥推导算法  
3. **长期优化**: 探索更底层的密钥拦截方法

这个方案虽然不如原始的密钥派生拦截方案直接，但在当前技术约束下是最实际可行的选择。 