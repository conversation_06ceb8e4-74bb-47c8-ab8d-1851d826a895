# 密钥派生拦截器 Makefile

CC = gcc
CLANG = clang
CFLAGS = -O2 -g -Wall
BPFCFLAGS = -O2 -g -target bpf -D__TARGET_ARCH_x86
LIBS = -lbpf -lelf -lz

# 目标程序
TARGET = key_derivation_interceptor

# 源文件
BPF_SOURCE = src/key_derivation_interceptor.bpf.c
BPF_OBJECT = src/key_derivation_interceptor.bpf.o
USER_SOURCE = src/key_derivation_interceptor.c

# 默认目标
all: $(TARGET)

# 编译eBPF程序  
$(BPF_OBJECT): $(BPF_SOURCE)
	$(CLANG) $(BPFCFLAGS) -c $< -o $@

# 编译用户态程序
$(TARGET): $(USER_SOURCE) $(BPF_OBJECT)
	$(CC) $(CFLAGS) -o $@ $< $(LIBS)

# 测试目标（编译并运行基本检查）
test: $(TARGET)
	@echo "🔍 检查eBPF程序是否正确编译..."
	@file $(BPF_OBJECT)
	@echo "✅ eBPF程序编译成功"
	@echo "🔍 检查用户态程序是否正确编译..."
	@file $(TARGET)
	@echo "✅ 用户态程序编译成功"
	@echo "🎯 密钥派生拦截器编译完成!"

# 运行密钥派生拦截器
run: $(TARGET)
	@echo "🚀 启动密钥派生拦截器..."
	sudo ./$(TARGET)

# 分析SSL库中的密钥派生函数
analyze-ssl:
	@echo "🔍 分析系统中的SSL库..."
	@echo "═══════════════════════════════════════════════════════════════"
	@for lib in /lib64/libssl.so.* /usr/lib64/libssl.so.* /lib/x86_64-linux-gnu/libssl.so.* /usr/lib/x86_64-linux-gnu/libssl.so.*; do \
		if [ -f "$$lib" ]; then \
			echo "📚 SSL库: $$lib"; \
			echo "🔑 HKDF函数:"; \
			nm -D "$$lib" 2>/dev/null | grep -i hkdf || echo "  ❌ 未找到HKDF函数"; \
			echo "🔑 PRF函数:"; \
			nm -D "$$lib" 2>/dev/null | grep -i prf || echo "  ❌ 未找到PRF函数"; \
			echo "🔑 P_hash函数:"; \
			nm -D "$$lib" 2>/dev/null | grep -i p_hash || echo "  ❌ 未找到P_hash函数"; \
			echo "───────────────────────────────────────────────────────────"; \
		fi; \
	done

# 清理
clean:
	rm -f $(TARGET) $(BPF_OBJECT)

# 检查依赖
check-deps:
	@echo "🔍 检查编译依赖..."
	@which $(CC) >/dev/null || (echo "❌ 缺少 $(CC)" && exit 1)
	@which $(CLANG) >/dev/null || (echo "❌ 缺少 $(CLANG)" && exit 1)
	@pkg-config --exists libbpf || (echo "❌ 缺少 libbpf 开发包" && exit 1)
	@echo "✅ 所有依赖已满足"

# 安装依赖（CentOS/RHEL）
install-deps:
	@echo "📦 安装编译依赖..."
	sudo yum install -y gcc clang libbpf-devel elfutils-libelf-devel zlib-devel
	@echo "✅ 依赖安装完成"

.PHONY: all test run analyze-ssl clean check-deps install-deps 