#!/bin/bash

# Curl SSL密钥提取测试脚本
# 测试curl等OpenSSL程序的SSL密钥提取功能

set -e

echo "🧪 Curl SSL密钥提取测试"
echo "=" * 50

# 检查环境
echo "📋 环境检查:"
echo "  操作系统: $(uname -r)"
echo "  Python版本: $(python3 --version)"
echo "  用户权限: $(whoami)"

# 检查必要的工具
echo ""
echo "🔧 工具检查:"

if command -v curl &> /dev/null; then
    echo "  ✅ curl: $(curl --version | head -n1)"
else
    echo "  ❌ curl 未安装"
    exit 1
fi

if command -v wget &> /dev/null; then
    echo "  ✅ wget: $(wget --version | head -n1)"
else
    echo "  ⚠️  wget 未安装，跳过wget测试"
fi

# 检查OpenSSL库
echo ""
echo "📚 OpenSSL库检查:"
OPENSSL_LIBS=(
    "/usr/lib64/libssl.so.1.1"
    "/usr/lib64/libssl.so.3"
    "/usr/lib64/libssl.so"
    "/usr/lib/x86_64-linux-gnu/libssl.so.1.1"
    "/usr/lib/x86_64-linux-gnu/libssl.so.3"
    "/usr/lib/x86_64-linux-gnu/libssl.so"
)

FOUND_LIB=""
for lib in "${OPENSSL_LIBS[@]}"; do
    if [ -f "$lib" ]; then
        echo "  ✅ 找到OpenSSL库: $lib"
        FOUND_LIB="$lib"
        break
    fi
done

if [ -z "$FOUND_LIB" ]; then
    echo "  ❌ 未找到OpenSSL库文件"
    echo "  💡 请安装OpenSSL开发包:"
    echo "     CentOS/RHEL: yum install openssl-devel"
    echo "     Ubuntu/Debian: apt install libssl-dev"
    exit 1
fi

# 检查BCC
echo ""
echo "🔨 BCC工具检查:"
if python3 -c "from bcc import BPF" 2>/dev/null; then
    echo "  ✅ BCC已安装且可用"
else
    echo "  ❌ BCC未安装或不可用"
    echo "  💡 请安装BCC:"
    echo "     yum install bcc bcc-tools python3-bcc"
    exit 1
fi

# 检查权限
echo ""
echo "🔐 权限检查:"
if [ "$EUID" -eq 0 ]; then
    echo "  ✅ 具有root权限"
else
    echo "  ❌ 需要root权限运行"
    echo "  请使用: sudo $0"
    exit 1
fi

echo ""
echo "🚀 开始SSL密钥提取测试..."
echo "=" * 50

# 设置测试文件
KEYLOG_FILE="/tmp/curl_ssl_test_keylog.txt"
LOG_FILE="/tmp/curl_test.log"

# 清理之前的文件
rm -f "$KEYLOG_FILE" "$LOG_FILE"

echo "📝 测试配置:"
echo "  密钥文件: $KEYLOG_FILE"
echo "  日志文件: $LOG_FILE"
echo ""

# 启动SSL密钥提取器（后台运行）
echo "🔧 启动SSL密钥提取器..."
python3 src/curl_ssl_keylog_extractor.py --output "$KEYLOG_FILE" > "$LOG_FILE" 2>&1 &
EXTRACTOR_PID=$!

echo "  PID: $EXTRACTOR_PID"
echo "  等待3秒让提取器初始化..."
sleep 3

# 检查提取器是否正常启动
if ! kill -0 $EXTRACTOR_PID 2>/dev/null; then
    echo "❌ SSL密钥提取器启动失败"
    echo "错误日志:"
    cat "$LOG_FILE"
    exit 1
fi

echo "✅ SSL密钥提取器已启动"
echo ""

# 测试函数
run_curl_test() {
    local url="$1"
    local desc="$2"
    
    echo "🌐 测试: $desc"
    echo "  URL: $url"
    
    # 运行curl命令
    echo "  执行: curl -k -s \"$url\""
    if curl -k -s "$url" > /dev/null 2>&1; then
        echo "  ✅ curl请求成功"
    else
        echo "  ⚠️  curl请求失败，但继续测试"
    fi
    
    # 等待一秒让数据处理
    sleep 1
}

# 执行多个测试
echo "📡 执行SSL请求测试..."

run_curl_test "https://httpbin.org/get" "HTTPBin API测试"
run_curl_test "https://www.google.com" "Google首页测试"
run_curl_test "https://github.com" "GitHub首页测试"

# 如果有wget，也测试一下
if command -v wget &> /dev/null; then
    echo ""
    echo "🔗 wget测试:"
    echo "  执行: wget --no-check-certificate -q -O /dev/null https://httpbin.org/get"
    if wget --no-check-certificate -q -O /dev/null https://httpbin.org/get 2>/dev/null; then
        echo "  ✅ wget请求成功"
    else
        echo "  ⚠️  wget请求失败"
    fi
    sleep 1
fi

echo ""
echo "⏱️  等待5秒以确保所有数据处理完成..."
sleep 5

# 停止提取器
echo "🛑 停止SSL密钥提取器..."
kill -TERM $EXTRACTOR_PID 2>/dev/null || true
sleep 2

# 如果进程还在运行，强制杀死
if kill -0 $EXTRACTOR_PID 2>/dev/null; then
    echo "  强制停止..."
    kill -KILL $EXTRACTOR_PID 2>/dev/null || true
fi

echo ""
echo "📊 测试结果分析:"
echo "=" * 50

# 检查提取器日志
echo "🔍 提取器日志:"
if [ -f "$LOG_FILE" ]; then
    echo "日志文件大小: $(wc -c < "$LOG_FILE") 字节"
    
    # 显示关键信息
    if grep -q "成功附加.*个探针" "$LOG_FILE"; then
        attached_probes=$(grep "成功附加.*个探针" "$LOG_FILE" | tail -1)
        echo "✅ 探针附加: $attached_probes"
    else
        echo "❌ 未找到探针附加信息"
    fi
    
    # 检查SSL事件
    ssl_events=$(grep -c "SSL" "$LOG_FILE" 2>/dev/null || echo "0")
    echo "SSL事件数: $ssl_events"
    
    # 检查密钥提取
    if grep -q "成功提取SSL密钥" "$LOG_FILE"; then
        key_extractions=$(grep -c "成功提取SSL密钥" "$LOG_FILE")
        echo "✅ 密钥提取事件: $key_extractions"
    else
        echo "❌ 未检测到密钥提取事件"
    fi
else
    echo "❌ 提取器日志文件不存在"
fi

echo ""
echo "🔑 密钥文件分析:"
if [ -f "$KEYLOG_FILE" ]; then
    file_size=$(wc -c < "$KEYLOG_FILE")
    echo "文件大小: $file_size 字节"
    
    # 统计行数
    total_lines=$(wc -l < "$KEYLOG_FILE")
    comment_lines=$(grep -c "^#" "$KEYLOG_FILE" 2>/dev/null || echo "0")
    key_lines=$(grep -c "^CLIENT_RANDOM" "$KEYLOG_FILE" 2>/dev/null || echo "0")
    
    echo "总行数: $total_lines"
    echo "注释行: $comment_lines"
    echo "密钥条目: $key_lines"
    
    if [ "$key_lines" -gt 0 ]; then
        echo ""
        echo "🎉 成功提取SSL密钥!"
        echo "密钥条目示例:"
        grep "^CLIENT_RANDOM" "$KEYLOG_FILE" | head -3 | while read line; do
            # 截取前80个字符显示
            echo "  ${line:0:80}..."
        done
        
        echo ""
        echo "✅ Wireshark使用方法:"
        echo "   1. 打开Wireshark"
        echo "   2. Edit → Preferences → Protocols → TLS"
        echo "   3. 设置 (Pre)-Master-Secret log filename: $KEYLOG_FILE"
        echo "   4. 重新加载HTTPS数据包即可解密"
        
    else
        echo ""
        echo "❌ 未提取到有效的SSL密钥"
        echo "文件内容:"
        head -10 "$KEYLOG_FILE" | sed 's/^/    /'
    fi
else
    echo "❌ 密钥文件不存在"
fi

echo ""
echo "📋 故障排除指南:"
if [ ! -f "$KEYLOG_FILE" ] || [ "$(grep -c "^CLIENT_RANDOM" "$KEYLOG_FILE" 2>/dev/null || echo "0")" -eq 0 ]; then
    echo "❌ 密钥提取失败，可能的原因："
    echo "   1. OpenSSL版本不兼容 - 当前使用: $FOUND_LIB"
    echo "   2. curl使用的SSL实现不是标准OpenSSL"
    echo "   3. SSL函数符号名称已改变"
    echo "   4. eBPF权限不足"
    echo ""
    echo "💡 调试建议："
    echo "   1. 运行调试模式："
    echo "      sudo python3 src/curl_ssl_keylog_extractor.py --debug"
    echo "   2. 检查curl的SSL实现："
    echo "      curl --version | grep -i ssl"
    echo "   3. 检查OpenSSL符号："
    echo "      nm -D $FOUND_LIB | grep SSL_get"
else
    echo "✅ 密钥提取成功!"
    echo "   已成功拦截和提取SSL密钥"
    echo "   可以用于Wireshark解密HTTPS流量"
fi

echo ""
echo "🔚 测试完成"
echo "=" * 50

# 清理临时文件
echo "🧹 清理测试文件..."
read -p "是否删除测试文件? [y/N] " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    rm -f "$LOG_FILE"
    if [ "$(grep -c "^CLIENT_RANDOM" "$KEYLOG_FILE" 2>/dev/null || echo "0")" -eq 0 ]; then
        rm -f "$KEYLOG_FILE"
        echo "已删除测试文件"
    else
        echo "保留密钥文件: $KEYLOG_FILE (包含有效密钥)"
    fi
else
    echo "保留测试文件:"
    echo "  密钥文件: $KEYLOG_FILE"
    echo "  日志文件: $LOG_FILE"
fi

echo "感谢使用Curl SSL密钥提取器测试脚本!" 