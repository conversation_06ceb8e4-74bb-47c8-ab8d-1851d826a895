#ifndef __BPF_HELPERS_H
#define __BPF_HELPERS_H

#include <linux/bpf.h>

/* Map definition structure */
struct bpf_map_def {
    unsigned int type;
    unsigned int key_size;
    unsigned int value_size;
    unsigned int max_entries;
    unsigned int map_flags;
};

/* Helper macro to place programs, maps, license in different sections
 * in elf_bpf file. Section names are interpreted by elf_bpf loader
 */
#define SEC(NAME) __attribute__((section(NAME), used))

/* helper functions called from eBPF programs written in C */
static long (*bpf_map_lookup_elem)(void *map, const void *key) = (void *) BPF_FUNC_map_lookup_elem;
static long (*bpf_map_update_elem)(void *map, const void *key, const void *value, __u64 flags) = (void *) BPF_FUNC_map_update_elem;
static long (*bpf_map_delete_elem)(void *map, const void *key) = (void *) BPF_FUNC_map_delete_elem;
static long (*bpf_probe_read)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) BPF_FUNC_probe_read;
static long (*bpf_probe_read_user)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) BPF_FUNC_probe_read_user;
static long (*bpf_probe_read_kernel)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) BPF_FUNC_probe_read_kernel;
static long (*bpf_probe_read_kernel_str)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) BPF_FUNC_probe_read_kernel_str;
static long (*bpf_ktime_get_ns)(void) = (void *) BPF_FUNC_ktime_get_ns;
static long (*bpf_get_current_pid_tgid)(void) = (void *) BPF_FUNC_get_current_pid_tgid;
static long (*bpf_get_current_task)(void) = (void *) BPF_FUNC_get_current_task;
static long (*bpf_perf_event_output)(void *ctx, void *map, __u64 flags, void *data, __u64 size) = (void *) BPF_FUNC_perf_event_output;
static long (*bpf_printk)(const char *fmt, __u32 fmt_size, ...) = (void *) BPF_FUNC_trace_printk;
static long (*bpf_get_current_comm)(void *buf, __u32 size_of_buf) = (void *) BPF_FUNC_get_current_comm;

/* Map definition macros */
#define __uint(X, Y) unsigned int X; Y X##_value
#define __type(X, Y) typeof(Y) X
#define __array(X, Y) typeof(Y) X[]

/* BPF map types */
#ifndef BPF_MAP_TYPE_HASH
#define BPF_MAP_TYPE_HASH 1
#endif

#ifndef BPF_MAP_TYPE_ARRAY
#define BPF_MAP_TYPE_ARRAY 2
#endif

#ifndef BPF_MAP_TYPE_PERF_EVENT_ARRAY
#define BPF_MAP_TYPE_PERF_EVENT_ARRAY 4
#endif

/* BPF map flags */
#ifndef BPF_ANY
#define BPF_ANY 0
#endif

#ifndef BPF_F_CURRENT_CPU
#define BPF_F_CURRENT_CPU 0xffffffffULL
#endif

#endif /* __BPF_HELPERS_H */ 