#ifndef __KEYLOG_COMMON_H__
#define __KEYLOG_COMMON_H__

#include <linux/types.h>

// 函数类型常量
#define FUNC_HKDF_EXTRACT    1
#define FUNC_HKDF_EXPAND     2
#define FUNC_PRF             3
#define FUNC_P_HASH          4
#define FUNC_HMAC            5
#define FUNC_SSL_NEW         6
#define FUNC_SSL_WRITE       7

// 密钥类型常量
#define KEY_TYPE_PRK         1  // Pseudo-Random Key (HKDF)
#define KEY_TYPE_MASTER      2  // Master Secret (TLS 1.2)
#define KEY_TYPE_TRAFFIC     3  // Traffic Keys (TLS 1.3)
#define KEY_TYPE_HANDSHAKE   4  // Handshake Keys
#define KEY_TYPE_FINISHED    5  // Finished Keys

// TLS版本常量
#define TLS_VERSION_12       12
#define TLS_VERSION_13       13

// 会话状态常量
#define SESSION_HANDSHAKE    1
#define SESSION_ESTABLISHED  2
#define SESSION_CLOSED       3

// 最大数据长度
#define MAX_KEY_DATA_SIZE    256
#define MAX_CONTEXT_SIZE     128
#define MAX_PROCESS_NAME     16

// 通用密钥事件结构
struct key_event {
    __u64 timestamp;              // 时间戳
    __u32 pid;                    // 进程ID
    __u32 tid;                    // 线程ID
    __u8 function_type;           // 函数类型
    __u8 tls_version;             // TLS版本
    __u8 key_type;                // 密钥类型
    __u8 data_len;                // 密钥数据长度
    __u8 key_data[MAX_KEY_DATA_SIZE];    // 密钥数据
    __u8 context_data[MAX_CONTEXT_SIZE]; // 上下文数据
    char process_name[MAX_PROCESS_NAME]; // 进程名称
};

// SSL会话状态结构
struct ssl_session {
    __u64 session_id;             // 会话ID
    __u32 pid;                    // 进程ID
    __u8 client_random[32];       // 客户端随机数
    __u8 server_random[32];       // 服务端随机数
    __u8 master_secret[48];       // 主密钥
    __u16 cipher_suite;           // 加密套件
    __u8 tls_version;             // TLS版本
    __u8 session_state;           // 会话状态
    __u64 create_time;            // 创建时间
    __u64 last_update;            // 最后更新时间
};

// SSL函数地址结构
struct ssl_function {
    char name[64];                // 函数名
    __u64 address;                // 函数地址
    __u32 pid;                    // 进程ID
    __u8 function_type;           // 函数类型
};

// 统计信息结构
struct keylog_stats {
    __u64 total_events;           // 总事件数
    __u64 key_extractions;        // 密钥提取次数
    __u64 sessions_tracked;       // 跟踪的会话数
    __u64 errors;                 // 错误次数
    __u64 last_update;            // 最后更新时间
};

// eBPF Map最大条目数
#define MAX_SESSIONS     1024
#define MAX_FUNCTIONS    256
#define MAX_EVENTS       8192

#endif /* __KEYLOG_COMMON_H__ */ 