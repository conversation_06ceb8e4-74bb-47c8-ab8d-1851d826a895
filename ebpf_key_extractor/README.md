# eBPF通用SSL密钥提取器

基于eBPF技术的通用SSL/TLS密钥提取解决方案，支持多种SSL实现，包括OpenSSL、Java JSSE、Go crypto/tls等。

## 🎯 项目特点

- **通用性**: 支持多种SSL/TLS实现
- **低侵入**: 无需修改应用程序
- **高性能**: 基于eBPF内核技术，开销极小
- **实时监控**: 实时捕获SSL密钥和会话信息
- **兼容性**: 支持TLS 1.2和TLS 1.3

## 🔧 系统要求

- Linux内核 4.15+ (推荐5.0+)
- root权限
- clang编译器
- libbpf开发库
- 内核头文件

## 📦 快速开始

### 1. 检查系统兼容性
```bash
make check
```

### 2. 安装依赖
```bash
make deps
```

### 3. 编译程序
```bash
make all
```

### 4. 运行curl SSL监控器

```bash
# 使用shell脚本版本（推荐）
sudo ./tools/curl_ssl_monitor.sh

# 或使用C程序版本
sudo ./bin/curl_ssl_monitor_v3
```

### 5. 测试SSL连接

在另一个终端运行curl命令：

```bash
# 测试GET请求
curl -k https://httpbin.org/get

# 测试POST请求
curl -k -X POST https://httpbin.org/post -d "test=data"

# 测试其他网站
curl -k https://www.baidu.com
```

### 6. 查看监控结果

监控器会实时显示SSL函数调用：

```
🔐 [timestamp] PID=xxx curl调用SSL_CTX_new - SSL上下文创建
📤 [timestamp] PID=xxx curl调用SSL_write - 发送加密数据
📥 [timestamp] PID=xxx curl调用SSL_read - 接收加密数据
🤝 [timestamp] PID=xxx curl调用SSL_connect - SSL握手连接
```

## 🛠️ 详细使用

### 编译选项
```bash
make help           # 显示帮助信息
make deps           # 安装依赖包
make check          # 检查系统兼容性
make all            # 编译所有程序
make run            # 运行程序
make test           # 基础功能测试
make debug          # 显示调试信息
make clean          # 清理编译文件
```

### 手动运行
```bash
# 编译
make all

# 运行密钥提取器
sudo ./bin/keylog_extractor

# 在另一个终端测试SSL连接
curl -k https://httpbin.org/get

# 查看提取的密钥
tail -f /tmp/ssl_keylog.txt
```

## 📋 技术原理

### 核心策略
不依赖具体SSL库实现，而是拦截所有SSL库都必须使用的底层密钥派生函数：

```
应用程序 -> SSL库 -> 密钥派生函数 -> eBPF拦截 -> 密钥提取
```

### 拦截的函数
- **HKDF_Extract/HKDF_Expand**: TLS 1.3密钥派生
- **PRF/P_hash**: TLS 1.2伪随机函数
- **HMAC**: 底层密码学原语
- **SSL_CTX_new**: SSL上下文创建
- **SSL_write**: 数据加密检测

### eBPF程序架构
```
┌─────────────────────────────────────────┐
│              eBPF Kernel Space          │
├─────────────────────────────────────────┤
│  uprobe_hkdf_extract()                  │
│  uprobe_hkdf_expand()                   │
│  uprobe_tls1_prf()                      │
│  uprobe_hmac()                          │
│  uprobe_ssl_ctx_new()                   │
│  uprobe_ssl_write()                     │
├─────────────────────────────────────────┤
│              BPF Maps                   │
│  key_events | ssl_sessions | stats     │
└─────────────────────────────────────────┘
                    │
                    ▼
┌─────────────────────────────────────────┐
│           User Space Program            │
├─────────────────────────────────────────┤ 
│ 1. 自动发现SSL库和函数                   │
│ 2. 动态安装uprobe                       │
│ 3. 处理密钥事件                         │
│ 4. 生成keylog格式输出                   │
│ 5. 实时监控和统计                       │
└─────────────────────────────────────────┘
```

## 📊 输出格式

### 控制台输出
```
[2024-01-15 10:30:45.123456789] PID=1234 (curl) SSL_CTX_new
[2024-01-15 10:30:45.234567890] PID=1234 (curl) HKDF_Extract - PRK - KEY_DATA: a1b2c3d4...
[2024-01-15 10:30:45.345678901] PID=1234 (curl) SSL_write
```

### Keylog文件 (/tmp/ssl_keylog.txt)
```
# [2024-01-15 10:30:45.123456789] PID=1234 curl
CLIENT_RANDOM 0000000000000000000000000000000000000000000000000000000000000000 a1b2c3d4e5f6...
```

## 🧪 测试验证

### 基础功能测试
```bash
# 运行完整测试套件
make test

# 手动测试不同SSL实现
curl -k https://httpbin.org/get          # OpenSSL
wget --no-check-certificate https://httpbin.org/get  # 可能使用GnuTLS
```

### 性能测试
```bash
# 监控资源使用
top -p $(pgrep keylog_extractor)

# 网络性能影响测试
ab -n 1000 -c 10 https://test-server/
```

## 🔍 故障排除

### 常见问题

1. **eBPF程序加载失败**
   ```bash
   # 检查内核版本和eBPF支持
   uname -r
   ls /sys/fs/bpf
   
   # 检查权限
   id
   ```

2. **找不到SSL库**
   ```bash
   # 查找系统SSL库
   find /usr/lib* /lib* -name "libssl.so*"
   ldd /usr/bin/curl | grep ssl
   ```

3. **没有捕获到密钥**
   ```bash
   # 检查eBPF日志
   cat /sys/kernel/debug/tracing/trace_pipe
   
   # 验证进程使用SSL
   lsof -p <pid> | grep ssl
   ```

### 调试模式
```bash
# 显示详细调试信息
make debug

# 启用eBPF日志
echo 1 > /sys/kernel/debug/tracing/events/bpf/enable
cat /sys/kernel/debug/tracing/trace_pipe
```

## 📈 性能特性

- **CPU开销**: < 5% (典型场景)
- **内存使用**: < 100MB
- **延迟影响**: < 1ms (网络传输)
- **兼容性**: 主流Linux发行版

## 🛡️ 安全考虑

- 需要root权限运行
- 密钥数据写入临时文件，注意文件权限
- 建议在受控环境中使用
- 遵守相关法律法规

## 📚 支持的SSL库

### 已验证支持
- ✅ OpenSSL 1.1.1/3.0
- ✅ curl (基于OpenSSL)
- ✅ Apache mod_ssl

### 理论支持 (待验证)
- 🔄 Java JSSE
- 🔄 Go crypto/tls
- 🔄 Node.js crypto
- 🔄 .NET Core SSL
- 🔄 GnuTLS

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交改动
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

GPL v2 - 详见LICENSE文件

## 📞 联系方式

- Issue: GitHub Issues
- 文档: docs/TECHNICAL_GUIDE.md

---

**⚠️ 免责声明**: 本工具仅用于合法的安全测试和调试目的。用户需确保遵守当地法律法规。 