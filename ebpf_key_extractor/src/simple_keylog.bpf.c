#include <linux/bpf.h>
#include <linux/ptrace.h>
#include "../include/bpf_helpers.h"
#include "../include/keylog_common.h"

char LICENSE[] SEC("license") = "GPL";

// 简化的Map定义
struct bpf_map_def SEC("maps") key_events = {
    .type = BPF_MAP_TYPE_PERF_EVENT_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(__u32),
    .max_entries = 256,
};

// 简化的事件结构
struct simple_event {
    __u64 timestamp;
    __u32 pid;
    __u32 function_type;
    char process_name[16];
};

// SSL_CTX_new 函数拦截
SEC("uprobe/SSL_CTX_new")
int uprobe_ssl_ctx_new(struct pt_regs *ctx)
{
    struct simple_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = FUNC_SSL_NEW;
    
    bpf_get_current_comm(event.process_name, sizeof(event.process_name));
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    return 0;
}

// SSL_write 函数拦截
SEC("uprobe/SSL_write")
int uprobe_ssl_write(struct pt_regs *ctx)
{
    struct simple_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = FUNC_SSL_WRITE;
    
    bpf_get_current_comm(event.process_name, sizeof(event.process_name));
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    return 0;
}

// 通用uprobe
SEC("uprobe/generic_ssl_function")
int uprobe_generic_ssl_function(struct pt_regs *ctx)
{
    struct simple_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 0; // 通用类型
    
    bpf_get_current_comm(event.process_name, sizeof(event.process_name));
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    return 0;
} 