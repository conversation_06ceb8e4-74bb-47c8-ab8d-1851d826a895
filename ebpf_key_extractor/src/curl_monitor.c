#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <linux/perf_event.h>
#include <linux/bpf.h>
#include <sys/syscall.h>

// curl事件结构（与eBPF程序匹配）
struct curl_event {
    __u64 timestamp;
    __u32 pid;
    __u32 function_type;
    char comm[16];
};

static volatile int running = 1;

// 信号处理函数
static void signal_handler(int sig)
{
    running = 0;
    printf("\n收到信号 %d，正在退出...\n", sig);
}

// 格式化时间戳
static void format_timestamp(__u64 ns, char *buf, size_t buf_size)
{
    time_t sec = ns / 1000000000;
    long nsec = ns % 1000000000;
    struct tm *tm = localtime(&sec);
    
    snprintf(buf, buf_size, "%04d-%02d-%02d %02d:%02d:%02d.%09ld",
             tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday,
             tm->tm_hour, tm->tm_min, tm->tm_sec, nsec);
}

// BPF系统调用包装
static int bpf_syscall(int cmd, union bpf_attr *attr, unsigned int size)
{
    return syscall(__NR_bpf, cmd, attr, size);
}

// 创建uprobe
static int create_uprobe(const char *lib_path, const char *symbol, int prog_fd)
{
    char filename[256];
    FILE *fp;
    int ret;
    
    // 创建uprobe事件
    snprintf(filename, sizeof(filename), "p:ssl_%s %s:%s", symbol, lib_path, symbol);
    
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (!fp) {
        perror("无法打开uprobe_events");
        return -1;
    }
    
    ret = fprintf(fp, "%s\n", filename);
    fclose(fp);
    
    if (ret < 0) {
        perror("写入uprobe事件失败");
        return -1;
    }
    
    printf("✅ 创建uprobe: %s\n", filename);
    
    // 获取事件ID
    snprintf(filename, sizeof(filename), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/id", symbol);
    fp = fopen(filename, "r");
    if (!fp) {
        perror("无法获取事件ID");
        return -1;
    }
    
    int event_id;
    if (fscanf(fp, "%d", &event_id) != 1) {
        fclose(fp);
        perror("读取事件ID失败");
        return -1;
    }
    fclose(fp);
    
    printf("✅ 事件ID: %d\n", event_id);
    return event_id;
}

// 启用uprobe事件
static int enable_uprobe_event(const char *symbol)
{
    char filename[256];
    FILE *fp;
    
    snprintf(filename, sizeof(filename), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", symbol);
    fp = fopen(filename, "w");
    if (!fp) {
        perror("无法打开事件enable文件");
        return -1;
    }
    
    if (fprintf(fp, "1\n") < 0) {
        fclose(fp);
        perror("启用事件失败");
        return -1;
    }
    
    fclose(fp);
    printf("✅ 启用uprobe事件: ssl_%s\n", symbol);
    return 0;
}

// 清理uprobe事件
static void cleanup_uprobe(const char *symbol)
{
    char command[256];
    FILE *fp;
    
    // 禁用事件
    snprintf(command, sizeof(command), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", symbol);
    fp = fopen(command, "w");
    if (fp) {
        fprintf(fp, "0\n");
        fclose(fp);
    }
    
    // 删除事件
    snprintf(command, sizeof(command), "-:ssl_%s", symbol);
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (fp) {
        fprintf(fp, "%s\n", command);
        fclose(fp);
    }
}

// 读取trace管道
static void read_trace_pipe()
{
    FILE *fp;
    char line[1024];
    
    fp = fopen("/sys/kernel/debug/tracing/trace_pipe", "r");
    if (!fp) {
        perror("无法打开trace_pipe");
        return;
    }
    
    printf("🎯 开始监听curl SSL事件...\n");
    printf("💡 使用 Ctrl+C 停止监听\n\n");
    
    while (running && fgets(line, sizeof(line), fp)) {
        // 解析trace输出
        if (strstr(line, "ssl_") && strstr(line, "curl")) {
            printf("📡 %s", line);
        }
    }
    
    fclose(fp);
}

int main(int argc, char **argv)
{
    printf("🚀 curl SSL监控器 v1.0\n");
    printf("=====================\n");
    
    // 检查权限
    if (geteuid() != 0) {
        fprintf(stderr, "❌ 需要root权限运行\n");
        return 1;
    }
    
    // 注册信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查eBPF程序
    if (access("/sys/fs/bpf/curl_keylog", F_OK) != 0) {
        fprintf(stderr, "❌ eBPF程序未加载，请先运行：\n");
        fprintf(stderr, "bpftool prog load obj/curl_keylog.bpf.o /sys/fs/bpf/curl_keylog\n");
        return 1;
    }
    
    printf("✅ 找到已加载的eBPF程序\n");
    
    // 创建uprobe事件
    int ssl_ctx_new_id = create_uprobe("/lib64/libssl.so.1.1", "SSL_CTX_new", 0);
    if (ssl_ctx_new_id < 0) {
        fprintf(stderr, "❌ 创建SSL_CTX_new uprobe失败\n");
        return 1;
    }
    
    int ssl_write_id = create_uprobe("/lib64/libssl.so.1.1", "SSL_write", 0);
    if (ssl_write_id < 0) {
        fprintf(stderr, "❌ 创建SSL_write uprobe失败\n");
        cleanup_uprobe("SSL_CTX_new");
        return 1;
    }
    
    // 启用事件
    if (enable_uprobe_event("SSL_CTX_new") < 0 || enable_uprobe_event("SSL_write") < 0) {
        fprintf(stderr, "❌ 启用uprobe事件失败\n");
        cleanup_uprobe("SSL_CTX_new");
        cleanup_uprobe("SSL_write");
        return 1;
    }
    
    printf("\n📋 uprobe设置完成，现在可以测试curl了\n");
    printf("🧪 在另一个终端运行: curl -k https://httpbin.org/get\n\n");
    
    // 读取trace事件
    read_trace_pipe();
    
    // 清理
    printf("\n🧹 清理uprobe事件...\n");
    cleanup_uprobe("SSL_CTX_new");
    cleanup_uprobe("SSL_write");
    
    printf("🛑 监控已停止\n");
    return 0;
} 