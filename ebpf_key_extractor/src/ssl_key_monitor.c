#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <errno.h>
#include <stdint.h>

// 定义内核类型
typedef uint64_t __u64;
typedef uint32_t __u32;
typedef uint8_t __u8;

static volatile int running = 1;

// SSL密钥事件结构（与eBPF程序匹配）
struct ssl_key_event {
    __u64 timestamp;
    __u32 pid;
    __u32 tid;
    __u32 function_type;
    __u32 key_length;
    char comm[16];
    __u64 ssl_ptr;
    __u8 key_data[64];
    __u8 success;
};

// 信号处理函数
static void signal_handler(int sig)
{
    running = 0;
    printf("\n收到信号 %d，正在退出...\n", sig);
}

// 十六进制打印函数
static void print_hex(const char *prefix, const __u8 *data, size_t len)
{
    printf("%s: ", prefix);
    for (size_t i = 0; i < len; i++) {
        printf("%02x", data[i]);
        if ((i + 1) % 16 == 0) printf("\n    ");
        else if ((i + 1) % 8 == 0) printf("  ");
        else printf(" ");
    }
    printf("\n");
}

// 获取函数名称
static const char* get_function_name(int type)
{
    switch (type) {
        case 1: return "SSL_CTX_new";
        case 2: return "SSL_write";
        case 3: return "SSL_read";
        case 4: return "SSL_connect";
        case 5: return "SSL_new";
        case 6: return "密钥提取";
        default: return "Unknown";
    }
}

// 获取函数图标
static const char* get_function_icon(int type)
{
    switch (type) {
        case 1: return "🔐";
        case 2: return "📤";
        case 3: return "📥";
        case 4: return "🤝";
        case 5: return "🆕";
        case 6: return "🔑";
        default: return "❓";
    }
}

// 格式化时间戳
static void format_timestamp(__u64 ns, char *buf, size_t buf_size)
{
    time_t sec = ns / 1000000000;
    long nsec = ns % 1000000000;
    struct tm *tm = localtime(&sec);
    
    snprintf(buf, buf_size, "%04d-%02d-%02d %02d:%02d:%02d.%09ld",
             tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday,
             tm->tm_hour, tm->tm_min, tm->tm_sec, nsec);
}

// 创建uprobe事件
static int create_uprobe_events()
{
    FILE *fp;
    const char *ssl_lib = "/lib64/libssl.so.1.1";
    
    // 清空现有事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (!fp) {
        perror("无法清空uprobe_events");
        return -1;
    }
    fclose(fp);
    
    // 创建SSL函数uprobe事件
    const char* functions[][2] = {
        {"SSL_CTX_new", "0x3b310"},
        {"SSL_write", "0x39b90"},
        {"SSL_read", "0x398d0"}
    };
    
    for (int i = 0; i < 3; i++) {
        fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "a");
        if (!fp) {
            perror("无法打开uprobe_events");
            return -1;
        }
        fprintf(fp, "p:ssl_%s %s:%s\n", functions[i][0], ssl_lib, functions[i][1]);
        fclose(fp);
        printf("✅ 创建uprobe: ssl_%s\n", functions[i][0]);
    }
    
    return 0;
}

// 启用uprobe事件
static int enable_uprobe_events()
{
    FILE *fp;
    const char* events[] = {"SSL_CTX_new", "SSL_write", "SSL_read"};
    
    for (int i = 0; i < 3; i++) {
        char path[256];
        snprintf(path, sizeof(path), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", events[i]);
        
        fp = fopen(path, "w");
        if (!fp) {
            printf("❌ 无法启用事件: %s\n", events[i]);
            continue;
        }
        fprintf(fp, "1\n");
        fclose(fp);
        printf("✅ 启用事件: ssl_%s\n", events[i]);
    }
    
    return 0;
}

// 清理uprobe事件
static void cleanup_uprobe_events()
{
    FILE *fp;
    const char* events[] = {"SSL_CTX_new", "SSL_write", "SSL_read"};
    
    printf("🧹 清理uprobe事件...\n");
    
    // 禁用事件
    for (int i = 0; i < 3; i++) {
        char path[256];
        snprintf(path, sizeof(path), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", events[i]);
        
        fp = fopen(path, "w");
        if (fp) {
            fprintf(fp, "0\n");
            fclose(fp);
        }
    }
    
    // 清空uprobe事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (fp) {
        fclose(fp);
    }
}

// 分析SSL数据
static void analyze_ssl_data(struct ssl_key_event *event)
{
    char timestamp[64];
    format_timestamp(event->timestamp, timestamp, sizeof(timestamp));
    
    printf("\n%s [%s] PID=%u TID=%u %s调用%s\n",
           get_function_icon(event->function_type),
           timestamp,
           event->pid,
           event->tid,
           event->comm,
           get_function_name(event->function_type));
    
    if (event->ssl_ptr) {
        printf("   📍 SSL指针: 0x%016llx\n", (unsigned long long)event->ssl_ptr);
    }
    
    if (event->success && event->key_length > 0) {
        printf("   🔑 成功提取%u字节数据:\n", event->key_length);
        print_hex("   数据", event->key_data, event->key_length);
        
        // 分析数据模式
        int all_zero = 1;
        int has_pattern = 0;
        
        for (int i = 0; i < event->key_length; i++) {
            if (event->key_data[i] != 0) {
                all_zero = 0;
                break;
            }
        }
        
        if (all_zero) {
            printf("   ⚠️  警告: 提取的数据全为零，可能SSL结构体尚未初始化\n");
        } else {
            printf("   ✅ 提取到非零数据，可能包含有效信息\n");
            
            // 查找可能的密钥模式
            if (event->key_length >= 32) {
                printf("   🔍 分析中... (前32字节可能是client_random或密钥数据)\n");
            }
        }
    } else if (event->function_type == 1) {
        printf("   ℹ️  SSL上下文创建 - 尚无密钥数据\n");
    } else {
        printf("   ❌ 未能提取SSL数据\n");
    }
    
    printf("   ─────────────────────────────────────────\n");
}

// 读取trace管道并解析SSL事件
static void read_trace_pipe()
{
    FILE *fp;
    char line[1024];
    
    fp = fopen("/sys/kernel/debug/tracing/trace_pipe", "r");
    if (!fp) {
        perror("无法打开trace_pipe");
        return;
    }
    
    printf("\n🎯 开始监听curl SSL密钥提取...\n");
    printf("💡 使用 Ctrl+C 停止监听\n");
    printf("═══════════════════════════════════════════\n");
    
    while (running && fgets(line, sizeof(line), fp)) {
        // 解析trace输出并显示基本信息
        if (strstr(line, "ssl_")) {
            char timestamp[32];
            char pid_str[16];
            char event_name[64];
            
            if (sscanf(line, "%*s-%15s %*s %*s %31s %63s", pid_str, timestamp, event_name) == 3) {
                printf("📡 [%s] PID=%s %s\n", timestamp, pid_str, event_name);
                
                // 创建模拟的SSL事件进行分析展示
                struct ssl_key_event mock_event = {};
                mock_event.timestamp = 0; // 使用当前时间
                mock_event.pid = atoi(pid_str);
                mock_event.tid = mock_event.pid;
                strncpy(mock_event.comm, "curl", sizeof(mock_event.comm));
                mock_event.ssl_ptr = 0x7f1234567890ULL; // 模拟SSL指针
                
                if (strstr(event_name, "SSL_CTX_new")) {
                    mock_event.function_type = 1;
                } else if (strstr(event_name, "SSL_write")) {
                    mock_event.function_type = 2;
                    // 模拟提取到的SSL结构体数据
                    mock_event.success = 1;
                    mock_event.key_length = 64;
                    // 用一些示例数据填充（实际应该从eBPF获取）
                    for (int i = 0; i < 64; i++) {
                        mock_event.key_data[i] = (i % 256);
                    }
                } else if (strstr(event_name, "SSL_read")) {
                    mock_event.function_type = 3;
                    mock_event.success = 1;
                    mock_event.key_length = 32;
                    // 模拟client_random数据
                    for (int i = 0; i < 32; i++) {
                        mock_event.key_data[i] = 0xa0 + (i % 16);
                    }
                }
                
                // 显示详细分析（注意：这是模拟数据，用于演示）
                if (mock_event.function_type > 1) {
                    printf("   🔬 模拟SSL数据分析 (实际需要eBPF程序配合):\n");
                    analyze_ssl_data(&mock_event);
                }
            }
        }
    }
    
    fclose(fp);
}

int main(int argc, char **argv)
{
    printf("🚀 SSL密钥提取监控器 v4.0\n");
    printf("============================\n");
    printf("⚠️  注意: 当前版本演示SSL函数监控和数据分析框架\n");
    printf("   真正的密钥提取需要eBPF程序配合寄存器访问\n");
    printf("============================\n");
    
    // 检查权限
    if (geteuid() != 0) {
        fprintf(stderr, "❌ 需要root权限运行\n");
        return 1;
    }
    
    // 注册信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查SSL库
    if (access("/lib64/libssl.so.1.1", F_OK) != 0) {
        fprintf(stderr, "❌ 找不到SSL库文件\n");
        return 1;
    }
    
    printf("✅ 使用SSL库: /lib64/libssl.so.1.1\n");
    
    // 停止追踪并清空缓冲区
    FILE *fp = fopen("/sys/kernel/debug/tracing/tracing_on", "w");
    if (fp) {
        fprintf(fp, "0\n");
        fclose(fp);
    }
    
    fp = fopen("/sys/kernel/debug/tracing/trace", "w");
    if (fp) {
        fprintf(fp, "\n");
        fclose(fp);
    }
    
    // 创建uprobe事件
    printf("📡 创建uprobe事件...\n");
    if (create_uprobe_events() < 0) {
        fprintf(stderr, "❌ 创建uprobe事件失败\n");
        return 1;
    }
    
    // 启用事件
    enable_uprobe_events();
    
    // 启用追踪
    fp = fopen("/sys/kernel/debug/tracing/tracing_on", "w");
    if (fp) {
        fprintf(fp, "1\n");
        fclose(fp);
    }
    
    printf("\n📋 SSL监控设置完成!\n");
    printf("🧪 在另一个终端运行: curl -k https://httpbin.org/get\n");
    
    // 读取trace事件
    read_trace_pipe();
    
    // 清理
    cleanup_uprobe_events();
    
    printf("\n🛑 监控已停止\n");
    return 0;
} 