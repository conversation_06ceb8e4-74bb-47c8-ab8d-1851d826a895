#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用SSL密钥拦截器 (Universal SSL Key Interceptor)
基于eBPF/BCC实现，支持多种SSL实现的密钥提取

支持的SSL实现：
1. OpenSSL (C/C++, Python, Node.js等)
2. Java JSSE (通过JNI调用监控)
3. Go crypto/tls (通过系统调用监控)
4. 其他SSL实现
"""

from bcc import BPF
import argparse
import signal
import sys
import time
import binascii
import os
import subprocess
from threading import Event

# 全局退出信号
exit_event = Event()

def signal_handler(sig, frame):
    global exit_event
    print("\n🛑 接收到退出信号，正在停止...")
    exit_event.set()

# eBPF程序代码
BPF_PROGRAM = """
#include <uapi/linux/ptrace.h>
#include <linux/sched.h>
#include <linux/socket.h>

// SSL密钥数据结构
struct ssl_key_data {
    u32 pid;
    u64 timestamp;
    u8 type;  // 1=client_random, 2=server_random, 3=master_secret
    u8 data[64];  // 密钥数据
    u32 data_len;
    char comm[TASK_COMM_LEN];
};

// 输出事件
BPF_PERF_OUTPUT(ssl_events);

// SSL_get_client_random拦截
int trace_ssl_get_client_random(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data data = {};
    data.pid = pid;
    data.timestamp = bpf_ktime_get_ns();
    data.type = 1;  // client_random
    bpf_get_current_comm(&data.comm, sizeof(data.comm));
    
    // 获取函数返回值（随机数长度）
    u64 ret = PT_REGS_RC(ctx);
    if (ret > 0 && ret <= 32) {
        data.data_len = ret;
        bpf_trace_printk("[%d] SSL_get_client_random: %d bytes\\n", pid, ret);
        ssl_events.perf_submit(ctx, &data, sizeof(data));
    }
    
    return 0;
}

// SSL_get_server_random拦截
int trace_ssl_get_server_random(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data data = {};
    data.pid = pid;
    data.timestamp = bpf_ktime_get_ns();
    data.type = 2;  // server_random
    bpf_get_current_comm(&data.comm, sizeof(data.comm));
    
    u64 ret = PT_REGS_RC(ctx);
    if (ret > 0 && ret <= 32) {
        data.data_len = ret;
        bpf_trace_printk("[%d] SSL_get_server_random: %d bytes\\n", pid, ret);
        ssl_events.perf_submit(ctx, &data, sizeof(data));
    }
    
    return 0;
}

// SSL_do_handshake拦截 - 监控握手完成
int trace_ssl_handshake(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 检查返回值，1表示握手成功
    u64 ret = PT_REGS_RC(ctx);
    if (ret == 1) {
        bpf_trace_printk("[%d] SSL handshake complete\\n", pid);
        
        struct ssl_key_data data = {};
        data.pid = pid;
        data.timestamp = bpf_ktime_get_ns();
        data.type = 4;  // handshake_complete
        bpf_get_current_comm(&data.comm, sizeof(data.comm));
        ssl_events.perf_submit(ctx, &data, sizeof(data));
    }
    
    return 0;
}

// Java SSL监控 - JNI函数调用
int trace_java_ssl(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data data = {};
    data.pid = pid;
    data.timestamp = bpf_ktime_get_ns();
    data.type = 5;  // java_ssl_activity
    bpf_get_current_comm(&data.comm, sizeof(data.comm));
    
    bpf_trace_printk("[%d] Java SSL activity\\n", pid);
    ssl_events.perf_submit(ctx, &data, sizeof(data));
    
    return 0;
}

// 系统调用监控 - sendmsg/recvmsg (用于Go等)
int trace_net_syscall(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 简单检测SSL/TLS流量特征
    struct ssl_key_data data = {};
    data.pid = pid;
    data.timestamp = bpf_ktime_get_ns();
    data.type = 6;  // network_ssl_activity
    bpf_get_current_comm(&data.comm, sizeof(data.comm));
    
    ssl_events.perf_submit(ctx, &data, sizeof(data));
    
    return 0;
}
"""

class UniversalSSLInterceptor:
    def __init__(self):
        self.sessions = {}  # 存储会话数据
        self.keylog_file = "/tmp/universal_ssl_keylog.txt"
        
    def setup_bpf(self):
        """设置BPF程序和探针"""
        print("🔧 正在设置eBPF程序...")
        
        # 编译BPF程序
        self.bpf = BPF(text=BPF_PROGRAM)
        
        # 设置OpenSSL函数拦截
        ssl_functions = [
            ("SSL_get_client_random", "trace_ssl_get_client_random"),
            ("SSL_get_server_random", "trace_ssl_get_server_random"), 
            ("SSL_do_handshake", "trace_ssl_handshake"),
        ]
        
        attached_count = 0
        
        # 尝试附加到不同的SSL库
        ssl_libs = [
            "/lib64/libssl.so.1.1",
            "/usr/lib64/libssl.so.1.1",
            "/lib/x86_64-linux-gnu/libssl.so.1.1",
            "/usr/lib/x86_64-linux-gnu/libssl.so.1.1",
        ]
        
        # Node.js等静态链接OpenSSL的程序
        node_binaries = [
            "/usr/bin/node",
            "/usr/local/bin/node",
        ]
        
        # 首先尝试外部SSL库
        for lib_path in ssl_libs:
            try:
                for func_name, bpf_func in ssl_functions:
                    try:
                        self.bpf.attach_uretprobe(name=lib_path, sym=func_name, 
                                                fn_name=bpf_func)
                        print(f"✅ 已附加 {func_name} -> {lib_path}")
                        attached_count += 1
                    except Exception as e:
                        print(f"⚠️  无法附加 {func_name} 到 {lib_path}: {e}")
                if attached_count > 0:
                    break  # 如果成功附加到一个库，就不再尝试其他库
            except Exception as e:
                print(f"⚠️  无法访问SSL库 {lib_path}: {e}")
                continue
        
        # 如果外部库都失败了，尝试静态链接的程序（如Node.js）
        if attached_count == 0:
            print("🔍 尝试附加到静态链接OpenSSL的程序...")
            for binary_path in node_binaries:
                if os.path.exists(binary_path):
                    try:
                        for func_name, bpf_func in ssl_functions:
                            try:
                                self.bpf.attach_uretprobe(name=binary_path, sym=func_name, 
                                                        fn_name=bpf_func)
                                print(f"✅ 已附加 {func_name} -> {binary_path} (静态链接)")
                                attached_count += 1
                            except Exception as e:
                                print(f"⚠️  无法附加 {func_name} 到 {binary_path}: {e}")
                        if attached_count > 0:
                            break  # 成功附加就停止
                    except Exception as e:
                        print(f"⚠️  无法访问程序 {binary_path}: {e}")
                        continue
        
        # Java SSL监控 (JNI函数)
        try:
            # 这里可以添加Java JNI SSL函数的监控
            # 例如: Java_javax_net_ssl_SSLContext_getSession
            pass
        except Exception as e:
            print(f"⚠️  Java SSL监控设置失败: {e}")
        
        # 设置事件处理
        self.bpf["ssl_events"].open_perf_buffer(self.handle_ssl_event)
        
        if attached_count == 0:
            print("❌ 没有成功附加任何探针")
            return False
        
        print(f"🎯 成功附加 {attached_count} 个探针")
        return True
    
    def handle_ssl_event(self, cpu, data, size):
        """处理SSL事件"""
        import ctypes
        
        class SSLKeyData(ctypes.Structure):
            _fields_ = [
                ("pid", ctypes.c_uint32),
                ("timestamp", ctypes.c_uint64), 
                ("type", ctypes.c_uint8),
                ("data", ctypes.c_uint8 * 64),
                ("data_len", ctypes.c_uint32),
                ("comm", ctypes.c_char * 16),
            ]
        
        event = ctypes.cast(data, ctypes.POINTER(SSLKeyData)).contents
        
        # 解析事件
        pid = event.pid
        timestamp = event.timestamp
        event_type = event.type
        comm = event.comm.decode('utf-8', 'replace').rstrip('\x00')
        
        # 格式化时间戳
        time_sec = timestamp / 1000000000
        
        print(f"\n🔍 SSL事件检测:")
        print(f"  PID: {pid}")
        print(f"  进程: {comm}")
        print(f"  时间: {time_sec:.6f}")
        
        if event_type == 1:
            print(f"  类型: 客户端随机数 ({event.data_len} 字节)")
            self.save_session_data(pid, 'client_random', event.data, event.data_len)
        elif event_type == 2:
            print(f"  类型: 服务端随机数 ({event.data_len} 字节)")
            self.save_session_data(pid, 'server_random', event.data, event.data_len)
        elif event_type == 4:
            print(f"  类型: SSL握手完成")
            self.finalize_session(pid)
        elif event_type == 5:
            print(f"  类型: Java SSL活动")
        elif event_type == 6:
            print(f"  类型: 网络SSL活动")
        
        print("─" * 50)
    
    def save_session_data(self, pid, data_type, data, data_len):
        """保存会话数据"""
        if pid not in self.sessions:
            self.sessions[pid] = {}
        
        # 提取实际数据
        key_data = bytes(data[:data_len])
        hex_data = binascii.hexlify(key_data).decode('ascii')
        
        self.sessions[pid][data_type] = {
            'data': key_data,
            'hex': hex_data,
            'length': data_len
        }
        
        print(f"  数据: {hex_data}")
    
    def finalize_session(self, pid):
        """完成会话处理，生成keylog"""
        if pid not in self.sessions:
            return
        
        session = self.sessions[pid]
        
        # 检查是否有足够的数据生成keylog
        if 'client_random' in session:
            client_random = session['client_random']['hex']
            
            # 这里需要获取master_secret
            # 由于我们目前只能获取到随机数，可能需要其他方法获取master_secret
            print(f"📝 会话数据收集完成 (PID: {pid})")
            print(f"  客户端随机数: {client_random}")
            
            if 'server_random' in session:
                server_random = session['server_random']['hex']
                print(f"  服务端随机数: {server_random}")
            
            # 这里可以尝试从内存中搜索master_secret
            # 或者结合其他方法获取完整的密钥信息
    
    def analyze_ssl_implementations(self):
        """分析系统中的SSL实现"""
        print("\n🔍 分析系统SSL实现:")
        print("─" * 50)
        
        # 检查OpenSSL
        
        ssl_libs = [
            "/lib64/libssl.so.1.1",
            "/usr/lib64/libssl.so.1.1",
        ]
        
        for lib in ssl_libs:
            if os.path.exists(lib):
                print(f"✅ OpenSSL库: {lib}")
                try:
                    result = subprocess.run(['nm', '-D', lib], 
                                          stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                          universal_newlines=True)
                    ssl_funcs = [line for line in result.stdout.split('\n') 
                               if 'SSL_get' in line and 'random' in line]
                    if ssl_funcs:
                        print(f"  随机数函数: {len(ssl_funcs)} 个")
                except:
                    pass
        
        # 检查Java
        try:
            result = subprocess.run(['java', '-version'], 
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  universal_newlines=True)
            if result.returncode == 0:
                print(f"✅ Java环境: {result.stderr.split()[2]}")
                print("  SSL实现: JSSE (纯Java实现)")
        except:
            print("❌ Java未安装")
        
        # 检查Node.js
        node_paths = ['/usr/bin/node', '/usr/local/bin/node']
        node_found = False
        for node_path in node_paths:
            if os.path.exists(node_path):
                try:
                    # Python 3.6兼容性
                    result = subprocess.run([node_path, '--version'], 
                                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                          universal_newlines=True)
                    if result.returncode == 0:
                        node_version = result.stdout.strip()
                        # 获取OpenSSL版本
                        openssl_result = subprocess.run([node_path, '-p', 'process.versions.openssl'], 
                                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                                      universal_newlines=True)
                        if openssl_result.returncode == 0:
                            openssl_version = openssl_result.stdout.strip()
                            print(f"✅ Node.js环境: {node_version}")
                            print(f"  SSL实现: 内置OpenSSL {openssl_version} (静态链接)")
                        else:
                            print(f"✅ Node.js环境: {node_version}")
                            print("  SSL实现: 内置OpenSSL (版本未知)")
                        node_found = True
                        break
                except Exception as e:
                    continue
        
        if not node_found:
            print("❌ Node.js未安装")
        
        # 检查Python SSL
        try:
            import ssl
            print(f"✅ Python SSL: {ssl.OPENSSL_VERSION}")
        except:
            print("❌ Python SSL不可用")
        
        print("─" * 50)
    
    def run(self):
        """运行拦截器"""
        print("🔑 通用SSL密钥拦截器启动")
        print("═" * 60)
        print("支持的SSL实现:")
        print("  ✅ OpenSSL (C/C++, Python等)")  
        print("  ⚠️  Java JSSE (部分支持)")
        print("  ⚠️  Go crypto/tls (通过网络监控)")
        print("  ⚠️  其他SSL实现 (通过系统调用监控)")
        print("═" * 60)
        
        # 分析SSL实现
        self.analyze_ssl_implementations()
        
        # 设置BPF
        if not self.setup_bpf():
            print("❌ BPF设置失败")
            return
        
        print("\n⏳ 开始监控SSL活动...")
        print("💡 测试命令: curl -k https://httpbin.org/get")
        print("🛑 按Ctrl+C停止\n")
        
        # 主循环
        try:
            while not exit_event.is_set():
                self.bpf.perf_buffer_poll(timeout=100)
        except KeyboardInterrupt:
            pass
        
        print("\n🔚 拦截器已停止")

def main():
    parser = argparse.ArgumentParser(description='通用SSL密钥拦截器')
    parser.add_argument('--verbose', '-v', action='store_true',
                      help='详细输出模式')
    args = parser.parse_args()
    
    # 检查root权限
    if os.geteuid() != 0:
        print("❌ 此程序需要root权限运行")
        sys.exit(1)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动拦截器
    interceptor = UniversalSSLInterceptor()
    interceptor.run()

if __name__ == "__main__":
    main() 