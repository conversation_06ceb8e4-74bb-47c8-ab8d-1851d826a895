#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <errno.h>

static volatile int running = 1;

// 信号处理函数
static void signal_handler(int sig)
{
    running = 0;
    printf("\n收到信号 %d，正在退出...\n", sig);
}

// 获取函数名称
static const char* get_function_name(const char* event_name)
{
    if (strstr(event_name, "SSL_CTX_new")) return "SSL_CTX_new";
    if (strstr(event_name, "SSL_write")) return "SSL_write";
    if (strstr(event_name, "SSL_read")) return "SSL_read";
    if (strstr(event_name, "SSL_connect")) return "SSL_connect";
    return "Unknown";
}

// 获取函数描述
static const char* get_function_desc(const char* event_name)
{
    if (strstr(event_name, "SSL_CTX_new")) return "SSL上下文创建";
    if (strstr(event_name, "SSL_write")) return "发送加密数据";
    if (strstr(event_name, "SSL_read")) return "接收加密数据";
    if (strstr(event_name, "SSL_connect")) return "SSL握手连接";
    return "未知函数";
}

// 获取函数图标
static const char* get_function_icon(const char* event_name)
{
    if (strstr(event_name, "SSL_CTX_new")) return "🔐";
    if (strstr(event_name, "SSL_write")) return "📤";
    if (strstr(event_name, "SSL_read")) return "📥";
    if (strstr(event_name, "SSL_connect")) return "🤝";
    return "❓";
}

// 创建uprobe事件
static int create_uprobe_events()
{
    FILE *fp;
    const char *ssl_lib = "/lib64/libssl.so.1.1";
    
    // 清空现有事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (!fp) {
        perror("无法清空uprobe_events");
        return -1;
    }
    fclose(fp);
    
    // 创建SSL_CTX_new事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "a");
    if (!fp) {
        perror("无法打开uprobe_events");
        return -1;
    }
    fprintf(fp, "p:ssl_SSL_CTX_new %s:0x3b310\n", ssl_lib);
    fclose(fp);
    
    // 创建SSL_write事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "a");
    if (fp) {
        fprintf(fp, "p:ssl_SSL_write %s:0x39b90\n", ssl_lib);
        fclose(fp);
    }
    
    // 创建SSL_read事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "a");
    if (fp) {
        fprintf(fp, "p:ssl_SSL_read %s:0x398d0\n", ssl_lib);
        fclose(fp);
    }
    
    // 创建SSL_connect事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "a");
    if (fp) {
        fprintf(fp, "p:ssl_SSL_connect %s:0x3d7e0\n", ssl_lib);
        fclose(fp);
    }
    
    printf("✅ 创建uprobe事件完成\n");
    return 0;
}

// 启用所有uprobe事件
static int enable_uprobe_events()
{
    FILE *fp;
    const char* events[] = {"SSL_CTX_new", "SSL_write", "SSL_read", "SSL_connect"};
    int i;
    
    for (i = 0; i < 4; i++) {
        char path[256];
        snprintf(path, sizeof(path), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", events[i]);
        
        fp = fopen(path, "w");
        if (!fp) {
            printf("❌ 无法启用事件: %s\n", events[i]);
            continue;
        }
        fprintf(fp, "1\n");
        fclose(fp);
        printf("✅ 启用事件: ssl_%s\n", events[i]);
    }
    
    return 0;
}

// 清理uprobe事件
static void cleanup_uprobe_events()
{
    FILE *fp;
    const char* events[] = {"SSL_CTX_new", "SSL_write", "SSL_read", "SSL_connect"};
    int i;
    
    printf("🧹 清理uprobe事件...\n");
    
    // 禁用事件
    for (i = 0; i < 4; i++) {
        char path[256];
        snprintf(path, sizeof(path), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", events[i]);
        
        fp = fopen(path, "w");
        if (fp) {
            fprintf(fp, "0\n");
            fclose(fp);
        }
    }
    
    // 清空uprobe事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (fp) {
        fclose(fp);
    }
}

// 读取trace管道并解析SSL事件
static void read_trace_pipe()
{
    FILE *fp;
    char line[1024];
    
    fp = fopen("/sys/kernel/debug/tracing/trace_pipe", "r");
    if (!fp) {
        perror("无法打开trace_pipe");
        return;
    }
    
    printf("🎯 开始监听curl SSL事件...\n");
    printf("💡 使用 Ctrl+C 停止监听\n\n");
    
    while (running && fgets(line, sizeof(line), fp)) {
        // 解析trace输出
        if (strstr(line, "ssl_")) {
            char timestamp[32];
            char pid_str[16];
            char event_name[64];
            
            // 解析trace行格式: comm-pid [cpu] flags timestamp: event_name: (address)
            if (sscanf(line, "%*s-%15s %*s %*s %31s %63s", pid_str, timestamp, event_name) == 3) {
                printf("%s [%s] PID=%s curl调用%s - %s\n",
                       get_function_icon(event_name),
                       timestamp,
                       pid_str,
                       get_function_name(event_name),
                       get_function_desc(event_name));
            }
        }
    }
    
    fclose(fp);
}

int main(int argc, char **argv)
{
    printf("🚀 curl SSL密钥监控器 v3.0\n");
    printf("==========================\n");
    
    // 检查权限
    if (geteuid() != 0) {
        fprintf(stderr, "❌ 需要root权限运行\n");
        return 1;
    }
    
    // 注册信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查SSL库
    if (access("/lib64/libssl.so.1.1", F_OK) != 0) {
        fprintf(stderr, "❌ 找不到SSL库文件\n");
        return 1;
    }
    
    printf("✅ 使用SSL库: /lib64/libssl.so.1.1\n");
    
    // 停止追踪并清空缓冲区
    FILE *fp = fopen("/sys/kernel/debug/tracing/tracing_on", "w");
    if (fp) {
        fprintf(fp, "0\n");
        fclose(fp);
    }
    
    fp = fopen("/sys/kernel/debug/tracing/trace", "w");
    if (fp) {
        fprintf(fp, "\n");
        fclose(fp);
    }
    
    // 创建uprobe事件
    printf("📡 创建uprobe事件...\n");
    if (create_uprobe_events() < 0) {
        fprintf(stderr, "❌ 创建uprobe事件失败\n");
        return 1;
    }
    
    // 启用事件
    enable_uprobe_events();
    
    // 启用追踪
    fp = fopen("/sys/kernel/debug/tracing/tracing_on", "w");
    if (fp) {
        fprintf(fp, "1\n");
        fclose(fp);
    }
    
    printf("\n📋 uprobe设置完成，现在可以测试curl了\n");
    printf("🧪 在另一个终端运行: curl -k https://httpbin.org/get\n\n");
    
    // 读取trace事件
    read_trace_pipe();
    
    // 清理
    cleanup_uprobe_events();
    
    printf("🛑 监控已停止\n");
    return 0;
} 