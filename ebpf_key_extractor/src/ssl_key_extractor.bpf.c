#include <linux/bpf.h>
#include <linux/ptrace.h>

char LICENSE[] __attribute__((section("license"), used)) = "GPL";

// Map定义结构
struct bpf_map_def {
    unsigned int type;
    unsigned int key_size;
    unsigned int value_size;
    unsigned int max_entries;
    unsigned int map_flags;
};

// SSL密钥事件结构
struct ssl_key_event {
    __u64 timestamp;
    __u32 pid;
    __u32 tid;
    __u32 function_type;
    __u32 key_length;
    char comm[16];
    __u8 key_data[256];  // 存储密钥数据
    __u8 client_random[32]; // TLS客户端随机数
    __u8 server_random[32]; // TLS服务器随机数
};

// SSL会话结构（简化）
struct ssl_session_key {
    __u32 pid;
    __u64 ssl_ptr;  // SSL结构体指针
    __u8 master_key[48];   // TLS master key
    __u8 client_random[32];
    __u8 server_random[32];
    __u32 key_valid;
};

// 事件Map
struct bpf_map_def __attribute__((section("maps"), used)) ssl_events = {
    .type = BPF_MAP_TYPE_PERF_EVENT_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(__u32),
    .max_entries = 256,
};

// SSL会话Map
struct bpf_map_def __attribute__((section("maps"), used)) ssl_sessions = {
    .type = BPF_MAP_TYPE_HASH,
    .key_size = sizeof(__u64),  // SSL指针作为key
    .value_size = sizeof(struct ssl_session_key),
    .max_entries = 1024,
};

// BPF helper函数声明
static long (*bpf_ktime_get_ns)(void) = (void *) 5;
static long (*bpf_get_current_pid_tgid)(void) = (void *) 14;
static long (*bpf_get_current_comm)(void *buf, __u32 size_of_buf) = (void *) 16;
static long (*bpf_perf_event_output)(void *ctx, void *map, __u64 flags, void *data, __u64 size) = (void *) 25;
static long (*bpf_probe_read_user)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) 112;
static long (*bpf_map_lookup_elem)(void *map, const void *key) = (void *) 1;
static long (*bpf_map_update_elem)(void *map, const void *key, const void *value, __u64 flags) = (void *) 2;

#define BPF_F_CURRENT_CPU 0xffffffffULL

// x86_64寄存器访问宏
#define PT_REGS_PARM1(x) ((x)->di)
#define PT_REGS_PARM2(x) ((x)->si)
#define PT_REGS_PARM3(x) ((x)->dx)
#define PT_REGS_RC(x) ((x)->ax)

// 辅助函数：读取SSL结构体中的密钥信息
static int extract_ssl_keys(void *ssl_ptr, struct ssl_key_event *event)
{
    if (!ssl_ptr) return -1;
    
    // 这里需要根据OpenSSL版本的SSL结构体偏移来读取
    // SSL结构体大致结构（简化）：
    // struct ssl_st {
    //     SSL_CTX *ctx;
    //     SSL_SESSION *session;
    //     ...
    // }
    
    // 尝试读取SSL_SESSION指针（偏移约在0x20-0x30位置）
    void *session_ptr = NULL;
    
    // 从SSL结构体中读取session指针（偏移可能需要调整）
    if (bpf_probe_read_user(&session_ptr, sizeof(session_ptr), 
                           (char*)ssl_ptr + 0x28) != 0) {
        return -1;
    }
    
    if (!session_ptr) return -1;
    
    // 从SSL_SESSION中读取master_key（偏移需要根据OpenSSL版本调整）
    // SSL_SESSION结构体中master_key通常在偏移0x30-0x50位置
    if (bpf_probe_read_user(event->key_data, 48, 
                           (char*)session_ptr + 0x40) != 0) {
        return -1;
    }
    
    // 读取client_random（在SSL结构体中，偏移需要调整）
    if (bpf_probe_read_user(event->client_random, 32,
                           (char*)ssl_ptr + 0x190) != 0) {
        return -1;
    }
    
    event->key_length = 48; // master key长度
    return 0;
}

// SSL_CTX_new 函数拦截
__attribute__((section("uprobe/SSL_CTX_new"), used))
int trace_ssl_ctx_new(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 1; // SSL_CTX_new
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_new 函数拦截 - 新建SSL连接
__attribute__((section("uprobe/SSL_new"), used))
int trace_ssl_new(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 5; // SSL_new
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_connect返回时拦截 - 握手完成，此时可以提取密钥
__attribute__((section("uretprobe/SSL_connect"), used))
int trace_ssl_connect_ret(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 4; // SSL_connect返回
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // 获取返回值（SSL指针在第一个参数）
    void *ssl_ptr = (void *)PT_REGS_PARM1(ctx);
    int ret_value = (int)PT_REGS_RC(ctx);
    
    // 只有成功连接才提取密钥（返回值1表示成功）
    if (ret_value == 1 && ssl_ptr) {
        if (extract_ssl_keys(ssl_ptr, &event) == 0) {
            event.function_type = 6; // 成功提取密钥
        }
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_write 函数拦截
__attribute__((section("uprobe/SSL_write"), used))
int trace_ssl_write(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 2; // SSL_write
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // 尝试从SSL_write的第一个参数（SSL*）提取密钥
    void *ssl_ptr = (void *)PT_REGS_PARM1(ctx);
    if (ssl_ptr) {
        extract_ssl_keys(ssl_ptr, &event);
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_read 函数拦截
__attribute__((section("uprobe/SSL_read"), used))
int trace_ssl_read(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 3; // SSL_read
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // 尝试从SSL_read的第一个参数（SSL*）提取密钥
    void *ssl_ptr = (void *)PT_REGS_PARM1(ctx);
    if (ssl_ptr) {
        extract_ssl_keys(ssl_ptr, &event);
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
} 