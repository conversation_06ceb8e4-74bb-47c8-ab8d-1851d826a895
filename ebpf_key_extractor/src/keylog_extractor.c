#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <errno.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "../include/keylog_common.h"

#define MAX_LINKS 32
#define DEFAULT_KEYLOG_FILE "/tmp/ssl_keylog.txt"

struct keylog_extractor {
    struct bpf_object *obj;
    struct bpf_program *prog_ssl_ctx_new;
    struct bpf_program *prog_hkdf_extract;
    struct bpf_program *prog_hkdf_expand;
    struct bpf_program *prog_tls_prf;
    struct bpf_program *prog_hmac;
    struct bpf_program *prog_ssl_write;
    struct bpf_link *links[MAX_LINKS];
    int link_count;
    int perf_fd;
    FILE *keylog_file;
    volatile int running;
};

static struct keylog_extractor extractor = {};

// 信号处理函数
static void signal_handler(int sig)
{
    extractor.running = 0;
    printf("\n收到信号 %d，正在退出...\n", sig);
}

// 将二进制数据转换为十六进制字符串
static void bytes_to_hex(const unsigned char *bytes, size_t len, char *hex_str)
{
    for (size_t i = 0; i < len; i++) {
        sprintf(hex_str + i * 2, "%02x", bytes[i]);
    }
    hex_str[len * 2] = '\0';
}

// 格式化时间戳
static void format_timestamp(__u64 ns, char *buf, size_t buf_size)
{
    time_t sec = ns / 1000000000;
    long nsec = ns % 1000000000;
    struct tm *tm = localtime(&sec);
    
    snprintf(buf, buf_size, "%04d-%02d-%02d %02d:%02d:%02d.%09ld",
             tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday,
             tm->tm_hour, tm->tm_min, tm->tm_sec, nsec);
}

// 处理密钥事件的回调函数
static int handle_key_event(void *ctx, void *data, size_t data_sz)
{
    struct key_event *event = (struct key_event *)data;
    char timestamp_str[64];
    char hex_key[MAX_KEY_DATA_SIZE * 2 + 1];
    const char *func_name = "UNKNOWN";
    const char *key_type_name = "UNKNOWN";
    
    // 格式化时间戳
    format_timestamp(event->timestamp, timestamp_str, sizeof(timestamp_str));
    
    // 转换函数类型名称
    switch (event->function_type) {
        case FUNC_HKDF_EXTRACT: func_name = "HKDF_Extract"; break;
        case FUNC_HKDF_EXPAND:  func_name = "HKDF_Expand"; break;
        case FUNC_PRF:          func_name = "PRF"; break;
        case FUNC_HMAC:         func_name = "HMAC"; break;
        case FUNC_SSL_NEW:      func_name = "SSL_CTX_new"; break;
        case FUNC_SSL_WRITE:    func_name = "SSL_write"; break;
    }
    
    // 转换密钥类型名称
    switch (event->key_type) {
        case KEY_TYPE_PRK:       key_type_name = "PRK"; break;
        case KEY_TYPE_MASTER:    key_type_name = "MASTER_SECRET"; break;
        case KEY_TYPE_TRAFFIC:   key_type_name = "TRAFFIC_KEY"; break;
        case KEY_TYPE_HANDSHAKE: key_type_name = "HANDSHAKE_KEY"; break;
        case KEY_TYPE_FINISHED:  key_type_name = "FINISHED_KEY"; break;
    }
    
    // 输出到控制台
    printf("[%s] PID=%d (%s) %s", 
           timestamp_str, event->pid, event->process_name, func_name);
           
    if (event->key_type > 0) {
        printf(" - %s", key_type_name);
    }
    
    if (event->data_len > 0) {
        bytes_to_hex(event->key_data, event->data_len, hex_key);
        printf(" - KEY_DATA: %s", hex_key);
        
        // 写入keylog文件 (标准格式)
        if (extractor.keylog_file && event->key_type == KEY_TYPE_MASTER) {
            // 简化的keylog格式
            fprintf(extractor.keylog_file, 
                    "# [%s] PID=%d %s\n"
                    "CLIENT_RANDOM %s %s\n",
                    timestamp_str, event->pid, event->process_name,
                    "0000000000000000000000000000000000000000000000000000000000000000", // 占位符
                    hex_key);
            fflush(extractor.keylog_file);
        }
    }
    
    printf("\n");
    return 0;
}

// 检查进程是否使用SSL
static int process_uses_ssl(int pid)
{
    char maps_path[256];
    FILE *maps_file;
    char line[1024];
    int uses_ssl = 0;
    
    snprintf(maps_path, sizeof(maps_path), "/proc/%d/maps", pid);
    maps_file = fopen(maps_path, "r");
    if (!maps_file) {
        return 0;
    }
    
    while (fgets(line, sizeof(line), maps_file)) {
        // 检查是否加载了SSL相关库
        if (strstr(line, "libssl.so") || strstr(line, "libcrypto.so") ||
            strstr(line, "libgnutls.so") || strstr(line, "libnss") ||
            strstr(line, "java") || strstr(line, "node")) {
            uses_ssl = 1;
            break;
        }
    }
    
    fclose(maps_file);
    return uses_ssl;
}

// 附加uprobe到特定进程的SSL函数
static int attach_ssl_probes(int pid)
{
    char binary_path[256];
    int attached = 0;
    
    // 获取进程可执行文件路径
    snprintf(binary_path, sizeof(binary_path), "/proc/%d/exe", pid);
    
    // 尝试附加SSL_CTX_new
    struct bpf_link *link = bpf_program__attach_uprobe(
        extractor.prog_ssl_ctx_new, false, pid, binary_path, 0);
    
    if (!libbpf_get_error(link)) {
        extractor.links[extractor.link_count++] = link;
        attached++;
        printf("✓ 已附加 SSL_CTX_new 到进程 %d\n", pid);
    } else {
        // 尝试库文件路径
        link = bpf_program__attach_uprobe(
            extractor.prog_ssl_ctx_new, false, -1, "/usr/lib/x86_64-linux-gnu/libssl.so.1.1", 0);
        if (!libbpf_get_error(link)) {
            extractor.links[extractor.link_count++] = link;
            attached++;
            printf("✓ 已附加 SSL_CTX_new 到 libssl.so\n");
        }
    }
    
    // 尝试附加其他SSL函数
    const char *ssl_libs[] = {
        "/usr/lib/x86_64-linux-gnu/libssl.so.1.1",
        "/usr/lib/x86_64-linux-gnu/libcrypto.so.1.1",
        "/lib/x86_64-linux-gnu/libssl.so.1.1",
        "/lib/x86_64-linux-gnu/libcrypto.so.1.1",
        NULL
    };
    
    for (int i = 0; ssl_libs[i] && extractor.link_count < MAX_LINKS - 5; i++) {
        // 检查库文件是否存在
        if (access(ssl_libs[i], R_OK) != 0) {
            continue;
        }
        
        // 尝试附加HKDF函数
        link = bpf_program__attach_uprobe(
            extractor.prog_hkdf_extract, false, -1, ssl_libs[i], 0);
        if (!libbpf_get_error(link)) {
            extractor.links[extractor.link_count++] = link;
            attached++;
        }
        
        // 尝试附加PRF函数
        link = bpf_program__attach_uprobe(
            extractor.prog_tls_prf, false, -1, ssl_libs[i], 0);
        if (!libbpf_get_error(link)) {
            extractor.links[extractor.link_count++] = link;
            attached++;
        }
        
        // 尝试附加HMAC函数
        link = bpf_program__attach_uprobe(
            extractor.prog_hmac, false, -1, ssl_libs[i], 0);
        if (!libbpf_get_error(link)) {
            extractor.links[extractor.link_count++] = link;
            attached++;
        }
    }
    
    return attached;
}

// 发现并附加到SSL进程
static int discover_and_attach_probes()
{
    FILE *proc_dir;
    char line[256];
    int total_attached = 0;
    
    // 简化版本：直接尝试附加到常见SSL库
    const char *common_ssl_libs[] = {
        "/usr/lib/x86_64-linux-gnu/libssl.so.1.1",
        "/usr/lib/x86_64-linux-gnu/libcrypto.so.1.1",
        "/lib/x86_64-linux-gnu/libssl.so.1.1",
        "/lib/x86_64-linux-gnu/libcrypto.so.1.1",
        "/usr/lib64/libssl.so.1.1",
        "/usr/lib64/libcrypto.so.1.1",
        NULL
    };
    
    printf("🔍 正在搜索SSL库并附加uprobe...\n");
    
    for (int i = 0; common_ssl_libs[i] && extractor.link_count < MAX_LINKS - 5; i++) {
        if (access(common_ssl_libs[i], R_OK) != 0) {
            continue;
        }
        
        printf("📚 发现SSL库: %s\n", common_ssl_libs[i]);
        
        // 附加SSL_CTX_new
        struct bpf_link *link = bpf_program__attach_uprobe(
            extractor.prog_ssl_ctx_new, false, -1, common_ssl_libs[i], 0);
        if (!libbpf_get_error(link)) {
            extractor.links[extractor.link_count++] = link;
            total_attached++;
            printf("  ✓ SSL_CTX_new\n");
        }
        
        // 附加SSL_write
        link = bpf_program__attach_uprobe(
            extractor.prog_ssl_write, false, -1, common_ssl_libs[i], 0);
        if (!libbpf_get_error(link)) {
            extractor.links[extractor.link_count++] = link;
            total_attached++;
            printf("  ✓ SSL_write\n");
        }
    }
    
    printf("📊 总共附加了 %d 个uprobe\n", total_attached);
    return total_attached;
}

// 处理perf事件循环
static int process_events()
{
    struct perf_buffer_opts pb_opts = {};
    struct perf_buffer *pb;
    int err;
    
    // 获取perf事件map的文件描述符
    int map_fd = bpf_object__find_map_fd_by_name(extractor.obj, "key_events");
    if (map_fd < 0) {
        fprintf(stderr, "找不到key_events map\n");
        return -1;
    }
    
    // 创建perf buffer
    pb_opts.sample_cb = handle_key_event;
    pb = perf_buffer__new(map_fd, 64, &pb_opts);
    if (libbpf_get_error(pb)) {
        fprintf(stderr, "创建perf buffer失败\n");
        return -1;
    }
    
    printf("🎯 开始监听SSL密钥事件...\n");
    printf("📝 密钥将保存到: %s\n", DEFAULT_KEYLOG_FILE);
    printf("💡 使用 Ctrl+C 停止监听\n\n");
    
    // 事件处理循环
    while (extractor.running) {
        err = perf_buffer__poll(pb, 1000); // 1秒超时
        if (err < 0 && err != -EINTR) {
            fprintf(stderr, "轮询perf buffer出错: %d\n", err);
            break;
        }
    }
    
    perf_buffer__free(pb);
    return 0;
}

// 清理资源
static void cleanup()
{
    // 关闭所有bpf links
    for (int i = 0; i < extractor.link_count; i++) {
        if (extractor.links[i]) {
            bpf_link__destroy(extractor.links[i]);
        }
    }
    
    // 关闭bpf object
    if (extractor.obj) {
        bpf_object__close(extractor.obj);
    }
    
    // 关闭keylog文件
    if (extractor.keylog_file) {
        fclose(extractor.keylog_file);
    }
    
    printf("🧹 资源清理完成\n");
}

int main(int argc, char **argv)
{
    int err;
    
    printf("🚀 eBPF通用SSL密钥提取器 v1.0\n");
    printf("===============================\n");
    
    // 注册信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    extractor.running = 1;
    
    // 提升内存限制
    struct rlimit rlim_new = {
        .rlim_cur = RLIM_INFINITY,
        .rlim_max = RLIM_INFINITY,
    };
    
    if (setrlimit(RLIMIT_MEMLOCK, &rlim_new)) {
        fprintf(stderr, "⚠️ 设置内存限制失败，可能影响eBPF程序加载\n");
    }
    
    // 设置libbpf严格模式
    libbpf_set_strict_mode(LIBBPF_STRICT_ALL);
    
    // 打开keylog文件
    extractor.keylog_file = fopen(DEFAULT_KEYLOG_FILE, "a");
    if (!extractor.keylog_file) {
        fprintf(stderr, "⚠️ 无法打开keylog文件: %s\n", DEFAULT_KEYLOG_FILE);
    }
    
    // 加载eBPF程序
    printf("📦 正在加载eBPF程序...\n");
    extractor.obj = bpf_object__open_file("keylog_extractor.bpf.o", NULL);
    if (libbpf_get_error(extractor.obj)) {
        fprintf(stderr, "❌ 无法打开eBPF目标文件\n");
        return 1;
    }
    
    err = bpf_object__load(extractor.obj);
    if (err) {
        fprintf(stderr, "❌ 加载eBPF程序失败: %d\n", err);
        bpf_object__close(extractor.obj);
        return 1;
    }
    
    printf("✅ eBPF程序加载成功\n");
    
    // 获取eBPF程序引用
    extractor.prog_ssl_ctx_new = bpf_object__find_program_by_name(extractor.obj, "uprobe_ssl_ctx_new");
    extractor.prog_hkdf_extract = bpf_object__find_program_by_name(extractor.obj, "uprobe_hkdf_extract");
    extractor.prog_hkdf_expand = bpf_object__find_program_by_name(extractor.obj, "uprobe_hkdf_expand");
    extractor.prog_tls_prf = bpf_object__find_program_by_name(extractor.obj, "uprobe_tls1_prf");
    extractor.prog_hmac = bpf_object__find_program_by_name(extractor.obj, "uprobe_hmac");
    extractor.prog_ssl_write = bpf_object__find_program_by_name(extractor.obj, "uprobe_ssl_write");
    
    if (!extractor.prog_ssl_ctx_new || !extractor.prog_ssl_write) {
        fprintf(stderr, "❌ 找不到必需的eBPF程序\n");
        cleanup();
        return 1;
    }
    
    // 发现并附加SSL探针
    if (discover_and_attach_probes() == 0) {
        fprintf(stderr, "❌ 没有成功附加任何uprobe\n");
        cleanup();
        return 1;
    }
    
    // 处理事件
    err = process_events();
    
    printf("\n🛑 监听已停止\n");
    cleanup();
    
    return err;
} 