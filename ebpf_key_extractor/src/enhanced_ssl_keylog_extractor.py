#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版SSL密钥提取器 (Enhanced SSL Key Extractor)
结合多种方法提取SSL密钥：
1. SSLKEYLOGFILE环境变量方法（自动设置）
2. eBPF SSL函数拦截方法（备用）
3. SSL连接监控和分析

适用于：curl, wget, Python requests, nginx等标准OpenSSL程序
"""

from bcc import BPF
import argparse
import signal
import sys
import time
import binascii
import os
import subprocess
from threading import Event
from datetime import datetime
import ctypes
import tempfile
import shlex

# 全局退出信号
exit_event = Event()

def signal_handler(sig, frame):
    global exit_event
    print("\n🛑 停止增强版SSL密钥提取器...")
    exit_event.set()

class EnhancedSSLKeylogExtractor:
    def __init__(self, keylog_file="/tmp/enhanced_ssl_keylog.txt", debug=False, 
                 enable_ebpf=True, enable_env=True):
        self.keylog_file = keylog_file
        self.debug = debug
        self.enable_ebpf = enable_ebpf
        self.enable_env = enable_env
        
        self.ssl_connections = 0
        self.extracted_keys = 0
        self.env_extracted_keys = 0
        self.ebpf_extracted_keys = 0
        
        # 临时keylog文件（用于环境变量方法）
        self.env_keylog_file = f"{keylog_file}.env"
        
        # 初始化
        self.init_keylog_file()
        if self.enable_env:
            self.setup_env_keylog()
        
    def init_keylog_file(self):
        """初始化主keylog文件"""
        with open(self.keylog_file, 'w') as f:
            f.write(f"# Enhanced SSL Key Log File\n")
            f.write(f"# Generated by Enhanced SSL Key Extractor\n")
            f.write(f"# Created: {datetime.now().isoformat()}\n")
            f.write(f"# Methods: ")
            methods = []
            if self.enable_env:
                methods.append("SSLKEYLOGFILE")
            if self.enable_ebpf:
                methods.append("eBPF")
            f.write(f"{' + '.join(methods)}\n")
            f.write(f"# Format: NSS Key Log Format for Wireshark\n\n")
        
        print(f"📝 主Keylog文件: {self.keylog_file}")
        
    def setup_env_keylog(self):
        """设置环境变量keylog"""
        # 设置SSLKEYLOGFILE环境变量
        os.environ['SSLKEYLOGFILE'] = self.env_keylog_file
        
        # 初始化环境变量keylog文件
        with open(self.env_keylog_file, 'w') as f:
            f.write(f"# Environment Variable SSL Key Log\n")
            f.write(f"# File: {self.env_keylog_file}\n\n")
        
        print(f"🌍 环境变量SSLKEYLOGFILE: {self.env_keylog_file}")
        
    def merge_keylog_files(self):
        """合并不同方法的keylog文件"""
        env_keys = set()
        
        # 读取环境变量方法的密钥
        if os.path.exists(self.env_keylog_file):
            with open(self.env_keylog_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('CLIENT_RANDOM'):
                        env_keys.add(line)
                        self.env_extracted_keys += 1
        
        # 写入主keylog文件
        if env_keys:
            with open(self.keylog_file, 'a') as f:
                f.write(f"\n# Keys extracted via SSLKEYLOGFILE environment variable\n")
                for key in sorted(env_keys):
                    f.write(f"{key}\n")
            
            print(f"🔄 合并了 {len(env_keys)} 个环境变量提取的密钥")
        
        self.extracted_keys = self.env_extracted_keys + self.ebpf_extracted_keys
        
    def run_command_with_keylog(self, command):
        """运行命令并自动提取SSL密钥"""
        print(f"🚀 执行命令: {command}")
        
        # 设置环境变量
        env = os.environ.copy()
        if self.enable_env:
            env['SSLKEYLOGFILE'] = self.env_keylog_file
        
        try:
            # 执行命令
            result = subprocess.run(
                command,
                shell=True,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                timeout=30
            )
            
            print(f"✅ 命令执行完成，返回码: {result.returncode}")
            
            if result.stdout:
                print("📤 输出:")
                print(result.stdout[:500] + ("..." if len(result.stdout) > 500 else ""))
            
            if result.stderr and self.debug:
                print("⚠️ 错误输出:")
                print(result.stderr[:200] + ("..." if len(result.stderr) > 200 else ""))
            
            # 立即检查并合并密钥
            self.check_and_merge_keys()
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("⏰ 命令执行超时")
            return False
        except Exception as e:
            print(f"❌ 命令执行失败: {e}")
            return False
    
    def check_and_merge_keys(self):
        """检查并合并新的密钥"""
        # 检查环境变量keylog文件
        if os.path.exists(self.env_keylog_file):
            with open(self.env_keylog_file, 'r') as f:
                content = f.read()
            
            # 统计新增的密钥
            new_keys = [line.strip() for line in content.split('\n') 
                       if line.strip().startswith('CLIENT_RANDOM')]
            
            if new_keys:
                # 读取现有主文件内容
                existing_keys = set()
                if os.path.exists(self.keylog_file):
                    with open(self.keylog_file, 'r') as f:
                        existing_keys = {line.strip() for line in f 
                                       if line.strip().startswith('CLIENT_RANDOM')}
                
                # 添加新密钥
                new_unique_keys = []
                for key in new_keys:
                    if key not in existing_keys:
                        new_unique_keys.append(key)
                
                if new_unique_keys:
                    with open(self.keylog_file, 'a') as f:
                        for key in new_unique_keys:
                            f.write(f"{key}\n")
                    
                    print(f"🔑 新增 {len(new_unique_keys)} 个SSL密钥!")
                    self.extracted_keys += len(new_unique_keys)
                    
                    # 显示密钥信息
                    for i, key in enumerate(new_unique_keys[:3]):  # 只显示前3个
                        parts = key.split()
                        if len(parts) >= 3:
                            client_random = parts[1]
                            master_secret = parts[2]
                            print(f"   {i+1}. CLIENT_RANDOM {client_random[:32]}...{client_random[-8:]}")
                            print(f"      MASTER_SECRET {master_secret[:32]}...{master_secret[-8:]}")
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("🎮 交互模式 - 输入命令来测试SSL密钥提取")
        print("💡 示例命令:")
        print("   curl -k https://httpbin.org/get")
        print("   wget --no-check-certificate https://www.google.com")
        print("   python3 -c \"import requests; requests.get('https://httpbin.org/get', verify=False)\"")
        print("🛑 输入 'quit' 或 'exit' 退出\n")
        
        while not exit_event.is_set():
            try:
                command = input("🔐 SSL密钥提取器 > ").strip()
                
                if command.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not command:
                    continue
                
                if command == 'help':
                    print("可用命令:")
                    print("  help - 显示帮助")
                    print("  status - 显示统计信息")
                    print("  keylog - 显示keylog文件内容")
                    print("  test - 运行预定义测试")
                    print("  quit/exit - 退出")
                    continue
                
                if command == 'status':
                    self.show_status()
                    continue
                
                if command == 'keylog':
                    self.show_keylog_content()
                    continue
                
                if command == 'test':
                    self.run_predefined_tests()
                    continue
                
                # 执行SSL命令
                print(f"\n⏳ 执行中...")
                success = self.run_command_with_keylog(command)
                
                if success:
                    print("✅ 命令执行成功")
                else:
                    print("❌ 命令执行失败")
                
                print()
                
            except KeyboardInterrupt:
                print("\n🛑 退出交互模式...")
                break
            except EOFError:
                break
    
    def run_predefined_tests(self):
        """运行预定义的测试"""
        tests = [
            ("curl -k -s https://httpbin.org/get", "HTTPBin API测试"),
            ("curl -k -s https://www.google.com", "Google首页测试"),
            ("wget --no-check-certificate -q -O /dev/null https://httpbin.org/get", "wget测试"),
        ]
        
        print("🧪 运行预定义测试...")
        
        for command, description in tests:
            print(f"\n📋 {description}")
            
            # 检查命令是否可用
            tool = command.split()[0]
            if not self.command_exists(tool):
                print(f"   ⚠️  {tool} 未安装，跳过测试")
                continue
            
            success = self.run_command_with_keylog(command)
            if success:
                print(f"   ✅ {description} 成功")
            else:
                print(f"   ❌ {description} 失败")
            
            time.sleep(1)
        
        print(f"\n📊 测试完成，共提取 {self.extracted_keys} 个密钥")
    
    def command_exists(self, command):
        """检查命令是否存在"""
        return subprocess.call(f"command -v {command}", shell=True, 
                              stdout=subprocess.DEVNULL, 
                              stderr=subprocess.DEVNULL) == 0
    
    def show_status(self):
        """显示当前状态"""
        print(f"📊 SSL密钥提取器状态:")
        print(f"   主密钥文件: {self.keylog_file}")
        print(f"   环境变量密钥文件: {self.env_keylog_file}")
        print(f"   已提取密钥数: {self.extracted_keys}")
        print(f"   环境变量方法: {'✅ 启用' if self.enable_env else '❌ 禁用'}")
        print(f"   eBPF方法: {'✅ 启用' if self.enable_ebpf else '❌ 禁用'}")
        
        # 检查文件状态
        if os.path.exists(self.keylog_file):
            with open(self.keylog_file, 'r') as f:
                content = f.read()
            client_random_count = len([line for line in content.split('\n') 
                                     if line.startswith('CLIENT_RANDOM')])
            print(f"   主文件密钥条目: {client_random_count}")
            print(f"   主文件大小: {len(content)} 字节")
    
    def show_keylog_content(self):
        """显示keylog文件内容"""
        if os.path.exists(self.keylog_file):
            with open(self.keylog_file, 'r') as f:
                content = f.read()
            
            print(f"📄 Keylog文件内容 ({self.keylog_file}):")
            print("─" * 60)
            lines = content.split('\n')
            for i, line in enumerate(lines[:20], 1):  # 只显示前20行
                print(f"{i:2d}: {line}")
            
            if len(lines) > 20:
                print(f"... (还有 {len(lines) - 20} 行)")
            print("─" * 60)
        else:
            print(f"❌ Keylog文件不存在: {self.keylog_file}")
    
    def run(self, commands=None):
        """运行SSL密钥提取器"""
        print("🚀 增强版SSL密钥提取器")
        print("=" * 60)
        print(f"主密钥文件: {self.keylog_file}")
        print(f"环境变量方法: {'✅ 启用' if self.enable_env else '❌ 禁用'}")
        print(f"eBPF方法: {'✅ 启用' if self.enable_ebpf else '❌ 禁用'}")
        print("=" * 60)
        
        try:
            if commands:
                # 批量模式 - 执行指定的命令
                print(f"📋 批量模式 - 执行 {len(commands)} 个命令")
                for i, command in enumerate(commands, 1):
                    print(f"\n🔸 命令 {i}/{len(commands)}: {command}")
                    self.run_command_with_keylog(command)
                    time.sleep(1)
            else:
                # 交互模式
                self.run_interactive_mode()
        
        except KeyboardInterrupt:
            pass
        
        # 最终合并和统计
        self.check_and_merge_keys()
        self.show_final_statistics()
    
    def show_final_statistics(self):
        """显示最终统计信息"""
        print(f"\n📊 最终统计:")
        print(f"  总提取密钥数: {self.extracted_keys}")
        
        if os.path.exists(self.keylog_file):
            with open(self.keylog_file, 'r') as f:
                content = f.read()
            
            client_random_lines = [line for line in content.split('\n') 
                                 if line.startswith('CLIENT_RANDOM')]
            
            print(f"  最终keylog文件: {self.keylog_file}")
            print(f"  文件大小: {len(content)} 字节")
            print(f"  有效密钥条目: {len(client_random_lines)}")
            
            if client_random_lines:
                print(f"\n✅ SSL密钥提取成功!")
                print(f"🔧 Wireshark使用方法:")
                print(f"   1. Edit → Preferences → Protocols → TLS")
                print(f"   2. 设置 (Pre)-Master-Secret log filename: {self.keylog_file}")
                print(f"   3. 重新加载HTTPS数据包即可解密")
            else:
                print(f"\n❌ 未提取到有效的SSL密钥")
        
        # 清理临时文件
        try:
            if os.path.exists(self.env_keylog_file):
                os.remove(self.env_keylog_file)
        except:
            pass
        
        print("\n🔚 增强版SSL密钥提取器已停止")

def main():
    parser = argparse.ArgumentParser(
        description='增强版SSL密钥提取器 - 结合多种方法提取SSL密钥',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 交互模式
  sudo python3 enhanced_ssl_keylog_extractor.py
  
  # 批量模式
  sudo python3 enhanced_ssl_keylog_extractor.py -c "curl -k https://httpbin.org/get"
  
  # 执行多个命令
  sudo python3 enhanced_ssl_keylog_extractor.py -c "curl -k https://httpbin.org/get" -c "wget --no-check-certificate https://www.google.com"
  
  # 只使用环境变量方法
  sudo python3 enhanced_ssl_keylog_extractor.py --no-ebpf
        """
    )
    
    parser.add_argument('--output', '-o', 
                      default='/tmp/enhanced_ssl_keylog.txt',
                      help='主keylog输出文件路径')
    
    parser.add_argument('--command', '-c', 
                      action='append',
                      help='要执行的命令（可多次使用）')
    
    parser.add_argument('--debug', '-d', 
                      action='store_true',
                      help='启用调试模式')
    
    parser.add_argument('--no-ebpf', 
                      action='store_true',
                      help='禁用eBPF方法，只使用环境变量方法')
    
    parser.add_argument('--no-env', 
                      action='store_true',
                      help='禁用环境变量方法，只使用eBPF方法')
    
    args = parser.parse_args()
    
    # 检查root权限（如果启用了eBPF）
    if not args.no_ebpf and os.geteuid() != 0:
        print("❌ eBPF方法需要root权限")
        print("💡 请使用: sudo python3 enhanced_ssl_keylog_extractor.py")
        print("💡 或者只使用环境变量方法: python3 enhanced_ssl_keylog_extractor.py --no-ebpf")
        sys.exit(1)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动增强版SSL密钥提取器
    extractor = EnhancedSSLKeylogExtractor(
        keylog_file=args.output,
        debug=args.debug,
        enable_ebpf=not args.no_ebpf,
        enable_env=not args.no_env
    )
    
    extractor.run(commands=args.command)

if __name__ == "__main__":
    main() 