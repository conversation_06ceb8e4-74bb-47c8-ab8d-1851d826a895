#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <linux/perf_event.h>
#include <linux/bpf.h>
#include <sys/syscall.h>

// SSL事件结构（与eBPF程序匹配）
struct ssl_event {
    __u64 timestamp;
    __u32 pid;
    __u32 function_type;
    char comm[16];
};

static volatile int running = 1;

// 信号处理函数
static void signal_handler(int sig)
{
    running = 0;
    printf("\n收到信号 %d，正在退出...\n", sig);
}

// 格式化时间戳
static void format_timestamp(__u64 ns, char *buf, size_t buf_size)
{
    time_t sec = ns / 1000000000;
    long nsec = ns % 1000000000;
    struct tm *tm = localtime(&sec);
    
    snprintf(buf, buf_size, "%04d-%02d-%02d %02d:%02d:%02d.%09ld",
             tm->tm_year + 1900, tm->tm_mon + 1, tm->tm_mday,
             tm->tm_hour, tm->tm_min, tm->tm_sec, nsec);
}

// 获取函数名称
static const char* get_function_name(int type)
{
    switch (type) {
        case 1: return "SSL_CTX_new";
        case 2: return "SSL_write";
        case 3: return "SSL_read";
        case 4: return "SSL_connect";
        default: return "Unknown";
    }
}

// 获取函数描述
static const char* get_function_desc(int type)
{
    switch (type) {
        case 1: return "SSL上下文创建";
        case 2: return "发送加密数据";
        case 3: return "接收加密数据";
        case 4: return "SSL握手连接";
        default: return "未知函数";
    }
}

// 获取函数图标
static const char* get_function_icon(int type)
{
    switch (type) {
        case 1: return "🔐";
        case 2: return "📤";
        case 3: return "📥";
        case 4: return "🤝";
        default: return "❓";
    }
}

// BPF系统调用包装
static int bpf_syscall(int cmd, union bpf_attr *attr, unsigned int size)
{
    return syscall(__NR_bpf, cmd, attr, size);
}

// 创建uprobe
static int create_uprobe(const char *lib_path, const char *symbol, const char *offset)
{
    char filename[512];
    FILE *fp;
    int ret;
    
    // 创建uprobe事件
    snprintf(filename, sizeof(filename), "p:ssl_%s %s:%s", symbol, lib_path, offset);
    
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (!fp) {
        perror("无法打开uprobe_events");
        return -1;
    }
    
    ret = fprintf(fp, "%s\n", filename);
    fclose(fp);
    
    if (ret < 0) {
        perror("写入uprobe事件失败");
        return -1;
    }
    
    printf("✅ 创建uprobe: %s\n", filename);
    return 0;
}

// 启用uprobe事件
static int enable_uprobe_event(const char *symbol)
{
    char filename[256];
    FILE *fp;
    
    snprintf(filename, sizeof(filename), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", symbol);
    fp = fopen(filename, "w");
    if (!fp) {
        perror("无法打开事件enable文件");
        return -1;
    }
    
    if (fprintf(fp, "1\n") < 0) {
        fclose(fp);
        perror("启用事件失败");
        return -1;
    }
    
    fclose(fp);
    printf("✅ 启用uprobe事件: ssl_%s\n", symbol);
    return 0;
}

// 清理uprobe事件
static void cleanup_uprobe(const char *symbol)
{
    char command[256];
    FILE *fp;
    
    // 禁用事件
    snprintf(command, sizeof(command), "/sys/kernel/debug/tracing/events/uprobes/ssl_%s/enable", symbol);
    fp = fopen(command, "w");
    if (fp) {
        fprintf(fp, "0\n");
        fclose(fp);
    }
    
    // 删除事件
    snprintf(command, sizeof(command), "-:ssl_%s", symbol);
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (fp) {
        fprintf(fp, "%s\n", command);
        fclose(fp);
    }
}

// 读取trace管道并解析SSL事件
static void read_trace_pipe()
{
    FILE *fp;
    char line[1024];
    
    fp = fopen("/sys/kernel/debug/tracing/trace_pipe", "r");
    if (!fp) {
        perror("无法打开trace_pipe");
        return;
    }
    
    printf("🎯 开始监听curl SSL事件...\n");
    printf("💡 使用 Ctrl+C 停止监听\n\n");
    
    while (running && fgets(line, sizeof(line), fp)) {
        // 解析trace输出
        if (strstr(line, "ssl_")) {
            char timestamp[32];
            char pid_str[16];
            char event_name[32];
            
            // 解析trace行格式: comm-pid [cpu] flags timestamp: event_name: (address)
            if (sscanf(line, "%*s-%15s %*s %*s %31s %31s", pid_str, timestamp, event_name) == 3) {
                // 提取事件类型
                int func_type = 0;
                if (strstr(event_name, "SSL_CTX_new")) func_type = 1;
                else if (strstr(event_name, "SSL_write")) func_type = 2;
                else if (strstr(event_name, "SSL_read")) func_type = 3;
                else if (strstr(event_name, "SSL_connect")) func_type = 4;
                
                if (func_type > 0) {
                    printf("%s [%s] PID=%s curl调用%s - %s\n",
                           get_function_icon(func_type),
                           timestamp,
                           pid_str,
                           get_function_name(func_type),
                           get_function_desc(func_type));
                }
            }
        }
    }
    
    fclose(fp);
}

int main(int argc, char **argv)
{
    printf("🚀 curl SSL密钥监控器 v2.0\n");
    printf("==========================\n");
    
    // 检查权限
    if (geteuid() != 0) {
        fprintf(stderr, "❌ 需要root权限运行\n");
        return 1;
    }
    
    // 注册信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 检查eBPF程序
    if (access("obj/curl_ssl_simple.bpf.o", F_OK) != 0) {
        fprintf(stderr, "❌ eBPF程序文件不存在: obj/curl_ssl_simple.bpf.o\n");
        return 1;
    }
    
    printf("✅ 找到eBPF程序文件\n");
    
    // 获取SSL库路径和函数偏移
    const char *ssl_lib = "/lib64/libssl.so.1.1";
    if (access(ssl_lib, F_OK) != 0) {
        ssl_lib = "/usr/lib64/libssl.so.1.1";
        if (access(ssl_lib, F_OK) != 0) {
            fprintf(stderr, "❌ 找不到SSL库文件\n");
            return 1;
        }
    }
    
    printf("✅ 使用SSL库: %s\n", ssl_lib);
    
    // 停止追踪并清空缓冲区
    FILE *fp = fopen("/sys/kernel/debug/tracing/tracing_on", "w");
    if (fp) {
        fprintf(fp, "0\n");
        fclose(fp);
    }
    
    fp = fopen("/sys/kernel/debug/tracing/trace", "w");
    if (fp) {
        fprintf(fp, "\n");
        fclose(fp);
    }
    
    // 清空现有uprobe事件
    fp = fopen("/sys/kernel/debug/tracing/uprobe_events", "w");
    if (fp) {
        fprintf(fp, "\n");
        fclose(fp);
    }
    
    // 创建uprobe事件
    printf("📡 创建uprobe事件...\n");
    if (create_uprobe(ssl_lib, "SSL_CTX_new", "0x3b310") < 0 ||
        create_uprobe(ssl_lib, "SSL_write", "0x39b90") < 0 ||
        create_uprobe(ssl_lib, "SSL_read", "0x398d0") < 0 ||
        create_uprobe(ssl_lib, "SSL_connect", "0x3d7e0") < 0) {
        fprintf(stderr, "❌ 创建uprobe事件失败\n");
        return 1;
    }
    
    // 启用事件
    if (enable_uprobe_event("SSL_CTX_new") < 0 ||
        enable_uprobe_event("SSL_write") < 0 ||
        enable_uprobe_event("SSL_read") < 0 ||
        enable_uprobe_event("SSL_connect") < 0) {
        fprintf(stderr, "❌ 启用uprobe事件失败\n");
        goto cleanup;
    }
    
    // 启用追踪
    fp = fopen("/sys/kernel/debug/tracing/tracing_on", "w");
    if (fp) {
        fprintf(fp, "1\n");
        fclose(fp);
    }
    
    printf("\n📋 uprobe设置完成，现在可以测试curl了\n");
    printf("🧪 在另一个终端运行: curl -k https://httpbin.org/get\n\n");
    
    // 读取trace事件
    read_trace_pipe();
    
cleanup:
    // 清理
    printf("\n🧹 清理uprobe事件...\n");
    cleanup_uprobe("SSL_CTX_new");
    cleanup_uprobe("SSL_write");
    cleanup_uprobe("SSL_read");
    cleanup_uprobe("SSL_connect");
    
    printf("🛑 监控已停止\n");
    return 0;
} 