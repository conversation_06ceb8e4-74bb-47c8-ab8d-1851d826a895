#include <linux/bpf.h>
#include <linux/ptrace.h>

char LICENSE[] __attribute__((section("license"), used)) = "GPL";

// Map定义结构
struct bpf_map_def {
    unsigned int type;
    unsigned int key_size;
    unsigned int value_size;
    unsigned int max_entries;
    unsigned int map_flags;
};

// 简化的SSL事件结构
struct ssl_event {
    __u64 timestamp;
    __u32 pid;
    __u32 function_type;
    char comm[16];
};

// 事件Map
struct bpf_map_def __attribute__((section("maps"), used)) ssl_events = {
    .type = BPF_MAP_TYPE_PERF_EVENT_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(__u32),
    .max_entries = 256,
};

// BPF helper函数声明
static long (*bpf_ktime_get_ns)(void) = (void *) 5;
static long (*bpf_get_current_pid_tgid)(void) = (void *) 14;
static long (*bpf_get_current_comm)(void *buf, __u32 size_of_buf) = (void *) 16;
static long (*bpf_perf_event_output)(void *ctx, void *map, __u64 flags, void *data, __u64 size) = (void *) 25;

#define BPF_F_CURRENT_CPU 0xffffffffULL

// SSL_CTX_new 函数拦截
__attribute__((section("uprobe/SSL_CTX_new"), used))
int trace_ssl_ctx_new(struct pt_regs *ctx)
{
    struct ssl_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 1; // SSL_CTX_new
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_write 函数拦截
__attribute__((section("uprobe/SSL_write"), used))
int trace_ssl_write(struct pt_regs *ctx)
{
    struct ssl_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 2; // SSL_write
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_read 函数拦截
__attribute__((section("uprobe/SSL_read"), used))
int trace_ssl_read(struct pt_regs *ctx)
{
    struct ssl_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 3; // SSL_read
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_connect 函数拦截
__attribute__((section("uprobe/SSL_connect"), used))
int trace_ssl_connect(struct pt_regs *ctx)
{
    struct ssl_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 4; // SSL_connect
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
} 