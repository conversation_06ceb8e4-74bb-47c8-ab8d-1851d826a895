#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_DATA_SIZE 256
#define MAX_SESSIONS 1024

// SSL随机数和密钥数据结构
struct ssl_key_data {
    u32 pid;
    u64 timestamp;
    u8 client_random[32];
    u8 server_random[32]; 
    u8 master_key[48];
    u32 client_random_len;
    u32 server_random_len;
    u32 master_key_len;
    u8 has_client_random;
    u8 has_server_random;
    u8 has_master_key;
};

// BPF Maps
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_SESSIONS);
    __type(key, u32);  // PID
    __type(value, struct ssl_key_data);
} ssl_sessions SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(u32));
    __uint(value_size, sizeof(u32));
} ssl_events SEC(".maps");

// 安全地读取用户空间内存
static __always_inline int safe_read_user(void *dst, const void *src, u32 size) {
    long ret = bpf_probe_read_user(dst, size, src);
    return ret == 0 ? size : 0;
}

// 拦截 SSL_get_client_random - 获取客户端随机数
SEC("uprobe/SSL_get_client_random")
int BPF_KPROBE(uprobe_ssl_get_client_random, void *ssl, void *out)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 获取或创建会话数据
    struct ssl_key_data *session = bpf_map_lookup_elem(&ssl_sessions, &pid);
    if (!session) {
        struct ssl_key_data new_session = {0};
        new_session.pid = pid;
        new_session.timestamp = bpf_ktime_get_ns();
        bpf_map_update_elem(&ssl_sessions, &pid, &new_session, BPF_ANY);
        session = bpf_map_lookup_elem(&ssl_sessions, &pid);
        if (!session) return 0;
    }
    
    bpf_printk("拦截SSL_get_client_random, PID: %d", pid);
    return 0;
}

// 拦截 SSL_get_client_random 的返回值
SEC("uretprobe/SSL_get_client_random") 
int BPF_KRETPROBE(uretprobe_ssl_get_client_random, size_t ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data *session = bpf_map_lookup_elem(&ssl_sessions, &pid);
    if (!session) return 0;
    
    if (ret > 0 && ret <= 32) {
        session->client_random_len = ret;
        session->has_client_random = 1;
        bpf_printk("获取到client_random, 长度: %d", ret);
    }
    
    return 0;
}

// 拦截 SSL_get_server_random - 获取服务器随机数
SEC("uprobe/SSL_get_server_random")
int BPF_KPROBE(uprobe_ssl_get_server_random, void *ssl, void *out)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data *session = bpf_map_lookup_elem(&ssl_sessions, &pid);
    if (!session) {
        struct ssl_key_data new_session = {0};
        new_session.pid = pid;
        new_session.timestamp = bpf_ktime_get_ns();
        bpf_map_update_elem(&ssl_sessions, &pid, &new_session, BPF_ANY);
        session = bpf_map_lookup_elem(&ssl_sessions, &pid);
        if (!session) return 0;
    }
    
    bpf_printk("🔍 拦截SSL_get_server_random, PID: %d", pid);
    return 0;
}

// 拦截 SSL_get_server_random 的返回值
SEC("uretprobe/SSL_get_server_random")
int BPF_KRETPROBE(uretprobe_ssl_get_server_random, size_t ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data *session = bpf_map_lookup_elem(&ssl_sessions, &pid);
    if (!session) return 0;
    
    if (ret > 0 && ret <= 32) {
        session->server_random_len = ret;  
        session->has_server_random = 1;
        bpf_printk("✅ 获取到server_random, 长度: %d", ret);
    }
    
    return 0;
}

// 拦截 SSL_SESSION_get_master_key - 获取主密钥
SEC("uprobe/SSL_SESSION_get_master_key") 
int BPF_KPROBE(uprobe_ssl_session_get_master_key, void *session, void *out, u32 outlen)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data *ssl_session = bpf_map_lookup_elem(&ssl_sessions, &pid);
    if (!ssl_session) {
        struct ssl_key_data new_session = {0};
        new_session.pid = pid;
        new_session.timestamp = bpf_ktime_get_ns();
        bpf_map_update_elem(&ssl_sessions, &pid, &new_session, BPF_ANY);
        ssl_session = bpf_map_lookup_elem(&ssl_sessions, &pid);
        if (!ssl_session) return 0;
    }
    
    bpf_printk("拦截SSL_SESSION_get_master_key, PID: %d", pid);
    return 0;
}

// 拦截 SSL_SESSION_get_master_key 的返回值
SEC("uretprobe/SSL_SESSION_get_master_key")
int BPF_KRETPROBE(uretprobe_ssl_session_get_master_key, size_t ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct ssl_key_data *session = bpf_map_lookup_elem(&ssl_sessions, &pid);
    if (!session) return 0;
    
    if (ret > 0) {
        session->master_key_len = ret;
        session->has_master_key = 1;
        bpf_printk("获取到master_key, 长度: %d", ret);
        
        // 如果我们有了必要的数据，发送事件
        if (session->has_client_random && session->has_master_key) {
            bpf_printk("SSL会话数据收集完成! PID: %d", pid);
            bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, session, sizeof(*session));
        }
    }
    
    return 0;
}

// 清理过期会话数据
SEC("uprobe/SSL_free")
int BPF_KPROBE(uprobe_ssl_free, void *ssl)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 删除会话数据
    bpf_map_delete_elem(&ssl_sessions, &pid);
    bpf_printk("🧹 清理SSL会话数据, PID: %d", pid);
    
    return 0;
}

char LICENSE[] SEC("license") = "GPL"; 