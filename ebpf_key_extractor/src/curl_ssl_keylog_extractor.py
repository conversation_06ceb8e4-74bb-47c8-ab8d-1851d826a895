#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Curl SSL密钥提取器 (Curl SSL Key Extractor)
专门用于提取curl和其他标准OpenSSL程序的SSL密钥

支持的程序：
- curl
- wget
- Python requests
- nginx (客户端连接)
- 其他使用标准OpenSSL的程序

输出格式：NSS Key Log Format，兼容Wireshark
"""

from bcc import BPF
import argparse
import signal
import sys
import time
import binascii
import os
from threading import Event
from datetime import datetime
import ctypes

# 全局退出信号
exit_event = Event()

def signal_handler(sig, frame):
    global exit_event
    print("\n🛑 停止密钥提取器...")
    exit_event.set()

# eBPF程序 - 专门针对OpenSSL的SSL密钥提取
BPF_PROGRAM = """
#include <uapi/linux/ptrace.h>
#include <linux/sched.h>

struct ssl_data {
    u32 pid;
    u64 timestamp;
    u8 data_type;  // 1=client_random, 2=server_random, 3=master_key
    u8 data[64];
    u32 data_len;
    char comm[TASK_COMM_LEN];
    u64 ssl_ptr;
};

struct ssl_session {
    u64 ssl_ptr;
    u8 client_random[32];
    u8 server_random[32];
    u8 master_key[48];
    u8 has_client_random;
    u8 has_server_random;
    u8 has_master_key;
};

BPF_PERF_OUTPUT(ssl_events);
BPF_HASH(ssl_sessions, u64, struct ssl_session);

// 拦截SSL_get_client_random函数
int trace_ssl_get_client_random_ret(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 获取进程名
    char comm[TASK_COMM_LEN];
    bpf_get_current_comm(&comm, sizeof(comm));
    
    // 获取函数参数和返回值
    u64 ssl_ptr = PT_REGS_PARM1(ctx);
    u64 out_ptr = PT_REGS_PARM2(ctx);
    u64 ret_len = PT_REGS_RC(ctx);
    
    // 验证返回值合理性（client_random通常是32字节）
    if (ret_len > 0 && ret_len <= 32 && out_ptr != 0) {
        struct ssl_data event = {};
        event.pid = pid;
        event.timestamp = bpf_ktime_get_ns();
        event.data_type = 1;  // client_random
        event.ssl_ptr = ssl_ptr;
        event.data_len = ret_len;
        bpf_get_current_comm(&event.comm, sizeof(event.comm));
        
        // 读取client_random数据
        if (bpf_probe_read_user(&event.data, ret_len, (void*)out_ptr) == 0) {
            ssl_events.perf_submit(ctx, &event, sizeof(event));
            
            // 更新会话状态
            u64 key = ssl_ptr;
            struct ssl_session *session = ssl_sessions.lookup(&key);
            if (!session) {
                struct ssl_session new_session = {};
                new_session.ssl_ptr = ssl_ptr;
                bpf_probe_read(&new_session.client_random, ret_len, (void*)out_ptr);
                new_session.has_client_random = 1;
                ssl_sessions.update(&key, &new_session);
            } else {
                bpf_probe_read(&session->client_random, ret_len, (void*)out_ptr);
                session->has_client_random = 1;
                ssl_sessions.update(&key, session);
            }
        }
    }
    
    return 0;
}

// 拦截SSL_get_server_random函数
int trace_ssl_get_server_random_ret(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    char comm[TASK_COMM_LEN];
    bpf_get_current_comm(&comm, sizeof(comm));
    
    u64 ssl_ptr = PT_REGS_PARM1(ctx);
    u64 out_ptr = PT_REGS_PARM2(ctx);
    u64 ret_len = PT_REGS_RC(ctx);
    
    if (ret_len > 0 && ret_len <= 32 && out_ptr != 0) {
        struct ssl_data event = {};
        event.pid = pid;
        event.timestamp = bpf_ktime_get_ns();
        event.data_type = 2;  // server_random
        event.ssl_ptr = ssl_ptr;
        event.data_len = ret_len;
        bpf_get_current_comm(&event.comm, sizeof(event.comm));
        
        if (bpf_probe_read_user(&event.data, ret_len, (void*)out_ptr) == 0) {
            ssl_events.perf_submit(ctx, &event, sizeof(event));
            
            // 更新会话状态
            u64 key = ssl_ptr;
            struct ssl_session *session = ssl_sessions.lookup(&key);
            if (!session) {
                struct ssl_session new_session = {};
                new_session.ssl_ptr = ssl_ptr;
                bpf_probe_read(&new_session.server_random, ret_len, (void*)out_ptr);
                new_session.has_server_random = 1;
                ssl_sessions.update(&key, &new_session);
            } else {
                bpf_probe_read(&session->server_random, ret_len, (void*)out_ptr);
                session->has_server_random = 1;
                ssl_sessions.update(&key, session);
            }
        }
    }
    
    return 0;
}

// 拦截SSL_SESSION_get_master_key函数
int trace_ssl_session_get_master_key_ret(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    char comm[TASK_COMM_LEN];
    bpf_get_current_comm(&comm, sizeof(comm));
    
    u64 session_ptr = PT_REGS_PARM1(ctx);
    u64 out_ptr = PT_REGS_PARM2(ctx);
    u64 ret_len = PT_REGS_RC(ctx);
    
    // master_key通常是48字节（TLS 1.2）或32字节（TLS 1.3）
    if (ret_len > 0 && ret_len <= 48 && out_ptr != 0) {
        struct ssl_data event = {};
        event.pid = pid;
        event.timestamp = bpf_ktime_get_ns();
        event.data_type = 3;  // master_key
        event.ssl_ptr = session_ptr;  // 注意这里是session指针
        event.data_len = ret_len;
        bpf_get_current_comm(&event.comm, sizeof(event.comm));
        
        if (bpf_probe_read_user(&event.data, ret_len, (void*)out_ptr) == 0) {
            ssl_events.perf_submit(ctx, &event, sizeof(event));
        }
    }
    
    return 0;
}

// 拦截SSL_do_handshake完成
int trace_ssl_do_handshake_ret(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    char comm[TASK_COMM_LEN];
    bpf_get_current_comm(&comm, sizeof(comm));
    
    u64 ssl_ptr = PT_REGS_PARM1(ctx);
    u64 ret_val = PT_REGS_RC(ctx);
    
    // SSL_do_handshake成功返回1
    if (ret_val == 1) {
        struct ssl_data event = {};
        event.pid = pid;
        event.timestamp = bpf_ktime_get_ns();
        event.data_type = 10;  // handshake_complete
        event.ssl_ptr = ssl_ptr;
        event.data_len = 0;
        bpf_get_current_comm(&event.comm, sizeof(event.comm));
        
        ssl_events.perf_submit(ctx, &event, sizeof(event));
    }
    
    return 0;
}

// 拦截SSL_new - 监控SSL对象创建
int trace_ssl_new_ret(struct pt_regs *ctx) {
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    char comm[TASK_COMM_LEN];
    bpf_get_current_comm(&comm, sizeof(comm));
    
    u64 ssl_ptr = PT_REGS_RC(ctx);
    
    if (ssl_ptr != 0) {
        struct ssl_data event = {};
        event.pid = pid;
        event.timestamp = bpf_ktime_get_ns();
        event.data_type = 20;  // ssl_new
        event.ssl_ptr = ssl_ptr;
        event.data_len = 0;
        bpf_get_current_comm(&event.comm, sizeof(event.comm));
        
        ssl_events.perf_submit(ctx, &event, sizeof(event));
        
        // 初始化SSL会话记录
        u64 key = ssl_ptr;
        struct ssl_session new_session = {};
        new_session.ssl_ptr = ssl_ptr;
        ssl_sessions.update(&key, &new_session);
    }
    
    return 0;
}
"""

class CurlSSLKeylogExtractor:
    def __init__(self, keylog_file="/tmp/curl_ssl_keylog.txt", debug=False):
        self.keylog_file = keylog_file
        self.debug = debug
        self.ssl_sessions = {}  # ssl_ptr -> session_data
        self.extracted_keys = 0
        
        # 创建keylog文件
        self.init_keylog_file()
        
    def init_keylog_file(self):
        """初始化keylog文件"""
        with open(self.keylog_file, 'w') as f:
            f.write(f"# SSL Key Log File\n")
            f.write(f"# Generated by Curl SSL Key Extractor\n")
            f.write(f"# Created: {datetime.now().isoformat()}\n")
            f.write(f"# Format: NSS Key Log Format for Wireshark\n\n")
        
        print(f"📝 Keylog文件: {self.keylog_file}")
        
    def setup_bpf(self):
        """设置BPF程序和探针"""
        print("🔧 设置SSL密钥拦截程序...")
        
        try:
            # 编译BPF程序
            self.bpf = BPF(text=BPF_PROGRAM)
            
            # SSL函数探针配置
            ssl_functions = [
                ("SSL_get_client_random", "trace_ssl_get_client_random_ret", "uretprobe"),
                ("SSL_get_server_random", "trace_ssl_get_server_random_ret", "uretprobe"),
                ("SSL_SESSION_get_master_key", "trace_ssl_session_get_master_key_ret", "uretprobe"),
                ("SSL_do_handshake", "trace_ssl_do_handshake_ret", "uretprobe"),
                ("SSL_new", "trace_ssl_new_ret", "uretprobe"),
            ]
            
            attached_count = 0
            
            # 尝试附加到系统OpenSSL库
            openssl_libs = [
                "/usr/lib64/libssl.so.1.1",
                "/usr/lib/x86_64-linux-gnu/libssl.so.1.1",
                "/usr/lib64/libssl.so.3",
                "/usr/lib/x86_64-linux-gnu/libssl.so.3",
                "/usr/lib64/libssl.so",
                "/usr/lib/x86_64-linux-gnu/libssl.so",
            ]
            
            # 查找可用的OpenSSL库
            available_lib = None
            for lib_path in openssl_libs:
                if os.path.exists(lib_path):
                    available_lib = lib_path
                    break
            
            if not available_lib:
                print("❌ 未找到OpenSSL库文件")
                return False
                
            print(f"📍 使用OpenSSL库: {available_lib}")
            
            # 附加SSL函数探针
            for func_name, bpf_func, probe_type in ssl_functions:
                try:
                    if probe_type == "uprobe":
                        self.bpf.attach_uprobe(name=available_lib, sym=func_name, fn_name=bpf_func)
                    else:  # uretprobe
                        self.bpf.attach_uretprobe(name=available_lib, sym=func_name, fn_name=bpf_func)
                    print(f"✅ 成功附加 {func_name} ({probe_type})")
                    attached_count += 1
                except Exception as e:
                    if self.debug:
                        print(f"⚠️  无法附加 {func_name}: {e}")
            
            if attached_count == 0:
                print("❌ 没有成功附加任何SSL函数探针")
                return False
            
            print(f"🎯 成功附加 {attached_count}/{len(ssl_functions)} 个探针")
            
            # 设置事件处理
            self.bpf["ssl_events"].open_perf_buffer(self.handle_ssl_event)
            
            return True
            
        except Exception as e:
            print(f"❌ BPF设置失败: {e}")
            return False
    
    def handle_ssl_event(self, cpu, data, size):
        """处理SSL事件"""
        class SSLData(ctypes.Structure):
            _fields_ = [
                ("pid", ctypes.c_uint32),
                ("timestamp", ctypes.c_uint64),
                ("data_type", ctypes.c_uint8),
                ("data", ctypes.c_uint8 * 64),
                ("data_len", ctypes.c_uint32),
                ("comm", ctypes.c_char * 16),
                ("ssl_ptr", ctypes.c_uint64),
            ]
        
        event = ctypes.cast(data, ctypes.POINTER(SSLData)).contents
        
        pid = event.pid
        data_type = event.data_type
        data_len = event.data_len
        ssl_ptr = event.ssl_ptr
        comm = event.comm.decode('utf-8', 'replace').rstrip('\x00')
        
        if self.debug:
            print(f"\n🔍 [PID {pid}] {comm} - SSL事件:")
            print(f"   SSL对象: {hex(ssl_ptr)}")
        
        # 获取或创建SSL会话记录
        if ssl_ptr not in self.ssl_sessions:
            self.ssl_sessions[ssl_ptr] = {
                'pid': pid,
                'comm': comm,
                'client_random': None,
                'server_random': None,
                'master_key': None,
                'timestamp': time.time()
            }
        
        session = self.ssl_sessions[ssl_ptr]
        
        if data_type == 1:  # client_random
            if data_len > 0:
                key_data = bytes(event.data[:data_len])
                hex_data = binascii.hexlify(key_data).decode('ascii').upper()
                session['client_random'] = hex_data
                if self.debug:
                    print(f"   类型: Client Random ({data_len} 字节)")
                    print(f"   数据: {hex_data}")
                
        elif data_type == 2:  # server_random
            if data_len > 0:
                key_data = bytes(event.data[:data_len])
                hex_data = binascii.hexlify(key_data).decode('ascii').upper()
                session['server_random'] = hex_data
                if self.debug:
                    print(f"   类型: Server Random ({data_len} 字节)")
                    print(f"   数据: {hex_data}")
                
        elif data_type == 3:  # master_key
            if data_len > 0:
                key_data = bytes(event.data[:data_len])
                hex_data = binascii.hexlify(key_data).decode('ascii').upper()
                session['master_key'] = hex_data
                if self.debug:
                    print(f"   类型: Master Key ({data_len} 字节)")
                    print(f"   数据: {hex_data}")
                
                # 尝试生成keylog条目
                self.try_generate_keylog(ssl_ptr, session)
                
        elif data_type == 10:  # handshake_complete
            if self.debug:
                print(f"   类型: SSL握手完成")
            # 握手完成后，尝试提取密钥信息
            self.try_extract_keys_from_session(ssl_ptr, session)
            
        elif data_type == 20:  # ssl_new
            if self.debug:
                print(f"   类型: SSL对象创建")
        
        if self.debug:
            print("─" * 50)
    
    def try_extract_keys_from_session(self, ssl_ptr, session):
        """从SSL会话中尝试提取密钥"""
        # 这里可以添加更多的密钥提取逻辑
        # 例如直接从SSL结构体中读取密钥信息
        pass
    
    def try_generate_keylog(self, ssl_ptr, session):
        """尝试生成keylog条目"""
        if session.get('client_random') and session.get('master_key'):
            client_random = session['client_random']
            master_key = session['master_key']
            
            # NSS Key Log格式: CLIENT_RANDOM <client_random> <master_secret>
            keylog_entry = f"CLIENT_RANDOM {client_random} {master_key}\n"
            
            # 写入keylog文件
            with open(self.keylog_file, 'a') as f:
                f.write(keylog_entry)
            
            self.extracted_keys += 1
            
            print(f"🎉 [{session['comm']}] 成功提取SSL密钥!")
            print(f"   CLIENT_RANDOM {client_random[:32]}...{client_random[-8:]}")
            print(f"   MASTER_SECRET {master_key[:32]}...{master_key[-8:]}")
            print(f"   ✅ 已写入: {self.keylog_file}")
            
            # 标记已生成
            session['keylog_generated'] = True
            return True
        
        return False
    
    def run(self):
        """运行SSL密钥提取器"""
        print("🚀 Curl SSL密钥提取器")
        print("=" * 60)
        print(f"目标程序: curl, wget, Python requests等OpenSSL程序")
        print(f"输出文件: {self.keylog_file}")
        print(f"调试模式: {'开启' if self.debug else '关闭'}")
        print("=" * 60)
        
        # 设置BPF
        if not self.setup_bpf():
            print("❌ SSL拦截器设置失败")
            return
        
        print("\n⏳ 开始SSL密钥监控...")
        print("💡 测试命令:")
        print("   curl -k https://httpbin.org/get")
        print("   curl -k https://www.google.com")
        print("   wget --no-check-certificate https://httpbin.org/get")
        print("🛑 按Ctrl+C停止监控\n")
        
        # 主监控循环
        try:
            while not exit_event.is_set():
                self.bpf.perf_buffer_poll(timeout=1000)
        except KeyboardInterrupt:
            pass
        
        # 显示统计信息
        self.show_statistics()
        
    def show_statistics(self):
        """显示统计信息"""
        print(f"\n📊 SSL密钥提取统计:")
        print(f"  监控的SSL会话数: {len(self.ssl_sessions)}")
        print(f"  成功提取的密钥数: {self.extracted_keys}")
        
        if os.path.exists(self.keylog_file):
            with open(self.keylog_file, 'r') as f:
                content = f.read()
            lines = [line for line in content.split('\n') if line.startswith('CLIENT_RANDOM')]
            print(f"  Keylog文件大小: {len(content)} 字节")
            print(f"  有效密钥条目: {len(lines)}")
            
            if lines:
                print(f"\n✅ 密钥提取成功! Wireshark使用方法:")
                print(f"   1. Edit → Preferences → Protocols → TLS")
                print(f"   2. 设置 (Pre)-Master-Secret log filename: {self.keylog_file}")
                print(f"   3. 重新加载HTTPS数据包")
        
        # 显示会话详情
        if self.ssl_sessions and self.debug:
            print(f"\n📋 SSL会话详情:")
            for ssl_ptr, session in list(self.ssl_sessions.items())[:5]:  # 只显示前5个
                print(f"  SSL {hex(ssl_ptr)} [{session['comm']}]:")
                if session.get('client_random'):
                    print(f"    ✅ Client Random: {session['client_random'][:32]}...")
                if session.get('server_random'):
                    print(f"    ✅ Server Random: {session['server_random'][:32]}...")
                if session.get('master_key'):
                    print(f"    ✅ Master Key: {session['master_key'][:32]}...")
                if session.get('keylog_generated'):
                    print(f"    🎉 Keylog已生成")
        
        print("\n🔚 SSL密钥提取器已停止")

def main():
    parser = argparse.ArgumentParser(
        description='Curl SSL密钥提取器 - 专门用于curl等OpenSSL程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  sudo python3 curl_ssl_keylog_extractor.py
  sudo python3 curl_ssl_keylog_extractor.py -o /tmp/my_keylog.txt -d
  sudo python3 curl_ssl_keylog_extractor.py --debug

然后运行:
  curl -k https://httpbin.org/get
  wget --no-check-certificate https://www.google.com
        """
    )
    
    parser.add_argument('--output', '-o', 
                      default='/tmp/curl_ssl_keylog.txt',
                      help='Keylog输出文件路径 (默认: /tmp/curl_ssl_keylog.txt)')
    
    parser.add_argument('--debug', '-d', 
                      action='store_true',
                      help='启用调试模式，显示详细的SSL事件信息')
    
    args = parser.parse_args()
    
    # 检查root权限
    if os.geteuid() != 0:
        print("❌ 此程序需要root权限运行")
        print("请使用: sudo python3 curl_ssl_keylog_extractor.py")
        sys.exit(1)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动SSL密钥提取器
    extractor = CurlSSLKeylogExtractor(keylog_file=args.output, debug=args.debug)
    extractor.run()

if __name__ == "__main__":
    main() 