#include <linux/bpf.h>
#include <linux/ptrace.h>
#include "../include/bpf_helpers.h"
#include "../include/bpf_tracing.h"
#include "../include/keylog_common.h"

char LICENSE[] SEC("license") = "GPL";

// eBPF Maps定义 - 使用传统语法
struct bpf_map_def SEC("maps") key_events = {
    .type = BPF_MAP_TYPE_PERF_EVENT_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(__u32),
    .max_entries = 256,
};

struct bpf_map_def SEC("maps") ssl_sessions = {
    .type = BPF_MAP_TYPE_HASH,
    .key_size = sizeof(__u64),
    .value_size = sizeof(struct ssl_session),
    .max_entries = MAX_SESSIONS,
};

struct bpf_map_def SEC("maps") function_addresses = {
    .type = BPF_MAP_TYPE_HASH,
    .key_size = sizeof(__u64),
    .value_size = sizeof(struct ssl_function),
    .max_entries = MAX_FUNCTIONS,
};

struct bpf_map_def SEC("maps") stats_map = {
    .type = BPF_MAP_TYPE_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(struct keylog_stats),
    .max_entries = 1,
};

// 辅助函数：获取进程名
static inline void get_process_name(char *name, int max_len)
{
    // 简化版本：直接获取当前进程名
    bpf_get_current_comm(name, max_len);
}

// 辅助函数：更新统计信息
static inline void update_stats(__u64 event_type)
{
    __u32 key = 0;
    struct keylog_stats *stats = bpf_map_lookup_elem(&stats_map, &key);
    if (stats) {
        __sync_fetch_and_add(&stats->total_events, 1);
        if (event_type == 1) {
            __sync_fetch_and_add(&stats->key_extractions, 1);
        }
        stats->last_update = bpf_ktime_get_ns();
        bpf_map_update_elem(&stats_map, &key, stats, BPF_ANY);
    }
}

// 通用事件发送函数
static inline int send_key_event(struct pt_regs *ctx, __u8 func_type, 
                                 __u8 key_type, void *data, __u32 data_len)
{
    struct key_event event = {};
    
    // 基础信息
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.tid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
    event.function_type = func_type;
    event.key_type = key_type;
    
    // 获取进程名
    get_process_name(event.process_name, MAX_PROCESS_NAME);
    
    // 读取密钥数据（安全检查）
    if (data && data_len > 0 && data_len <= MAX_KEY_DATA_SIZE) {
        if (bpf_probe_read_user(event.key_data, data_len, data) == 0) {
            event.data_len = data_len;
        }
    }
    
    // 发送事件
    int ret = bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                                   &event, sizeof(event));
    
    // 更新统计
    update_stats(1);
    
    return ret;
}

// SSL_CTX_new 函数拦截 - 检测SSL会话开始
SEC("uprobe/SSL_CTX_new")
int uprobe_ssl_ctx_new(struct pt_regs *ctx)
{
    struct key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.tid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
    event.function_type = FUNC_SSL_NEW;
    event.key_type = 0; // 无密钥类型
    
    get_process_name(event.process_name, MAX_PROCESS_NAME);
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    bpf_printk("SSL_CTX_new called by PID: %d (%s)", event.pid, event.process_name);
    
    update_stats(0);
    return 0;
}

// HKDF_Extract 函数拦截 (TLS 1.3)
SEC("uprobe/HKDF_Extract")
int uprobe_hkdf_extract(struct pt_regs *ctx)
{
    // 获取函数参数
    void *prk = (void *)PT_REGS_PARM1(ctx);       // 输出PRK
    void *salt = (void *)PT_REGS_PARM2(ctx);      // 盐值
    size_t salt_len = PT_REGS_PARM3(ctx);         // 盐值长度
    void *ikm = (void *)PT_REGS_PARM4(ctx);       // 输入密钥材料
    size_t ikm_len = PT_REGS_PARM5(ctx);          // IKM长度
    
    bpf_printk("HKDF_Extract: PID=%d, salt_len=%zu, ikm_len=%zu", 
               bpf_get_current_pid_tgid() >> 32, salt_len, ikm_len);
    
    // 发送IKM事件
    if (ikm && ikm_len > 0) {
        send_key_event(ctx, FUNC_HKDF_EXTRACT, KEY_TYPE_PRK, ikm, ikm_len);
    }
    
    return 0;
}

// HKDF_Expand 函数拦截 (TLS 1.3)
SEC("uprobe/HKDF_Expand")
int uprobe_hkdf_expand(struct pt_regs *ctx)
{
    void *okm = (void *)PT_REGS_PARM1(ctx);       // 输出密钥材料
    size_t okm_len = PT_REGS_PARM2(ctx);          // OKM长度
    void *prk = (void *)PT_REGS_PARM3(ctx);       // 伪随机密钥
    size_t prk_len = PT_REGS_PARM4(ctx);          // PRK长度
    void *info = (void *)PT_REGS_PARM5(ctx);      // 信息字段
    size_t info_len = PT_REGS_PARM6(ctx);         // 信息长度
    
    bpf_printk("HKDF_Expand: PID=%d, prk_len=%zu, okm_len=%zu", 
               bpf_get_current_pid_tgid() >> 32, prk_len, okm_len);
    
    // 发送PRK事件
    if (prk && prk_len > 0) {
        send_key_event(ctx, FUNC_HKDF_EXPAND, KEY_TYPE_TRAFFIC, prk, prk_len);
    }
    
    return 0;
}

// PRF 函数拦截 (TLS 1.2)
SEC("uprobe/tls1_PRF")
int uprobe_tls1_prf(struct pt_regs *ctx)
{
    void *out = (void *)PT_REGS_PARM1(ctx);       // 输出
    size_t out_len = PT_REGS_PARM2(ctx);          // 输出长度
    void *secret = (void *)PT_REGS_PARM3(ctx);    // 密钥
    size_t secret_len = PT_REGS_PARM4(ctx);       // 密钥长度
    char *label = (char *)PT_REGS_PARM5(ctx);     // 标签
    void *seed = (void *)PT_REGS_PARM6(ctx);      // 种子
    size_t seed_len = PT_REGS_PARM7(ctx);         // 种子长度
    
    bpf_printk("tls1_PRF: PID=%d, secret_len=%zu, out_len=%zu", 
               bpf_get_current_pid_tgid() >> 32, secret_len, out_len);
    
    // 检查是否是master secret生成
    char master_label[16] = {};
    __u8 key_type = KEY_TYPE_MASTER;
    
    if (label) {
        bpf_probe_read_user(master_label, sizeof(master_label), label);
        // 简单的字符串比较 (eBPF限制)
        if (master_label[0] == 'm' && master_label[1] == 'a' && 
            master_label[2] == 's' && master_label[3] == 't') {
            key_type = KEY_TYPE_MASTER;
        }
    }
    
    // 发送密钥事件
    if (secret && secret_len > 0) {
        send_key_event(ctx, FUNC_PRF, key_type, secret, secret_len);
    }
    
    return 0;
}

// HMAC 函数拦截 - 底层密码学原语
SEC("uprobe/HMAC")
int uprobe_hmac(struct pt_regs *ctx)
{
    void *md = (void *)PT_REGS_PARM1(ctx);        // 消息摘要
    void *key = (void *)PT_REGS_PARM2(ctx);       // HMAC密钥
    int key_len = PT_REGS_PARM3(ctx);             // 密钥长度
    void *data = (void *)PT_REGS_PARM4(ctx);      // 数据
    size_t data_len = PT_REGS_PARM5(ctx);         // 数据长度
    
    bpf_printk("HMAC: PID=%d, key_len=%d, data_len=%zu", 
               bpf_get_current_pid_tgid() >> 32, key_len, data_len);
    
    // 发送HMAC密钥事件
    if (key && key_len > 0) {
        send_key_event(ctx, FUNC_HMAC, KEY_TYPE_PRK, key, key_len);
    }
    
    return 0;
}

// SSL_write 函数拦截 - 检测数据加密
SEC("uprobe/SSL_write")
int uprobe_ssl_write(struct pt_regs *ctx)
{
    struct key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.tid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
    event.function_type = FUNC_SSL_WRITE;
    event.key_type = 0;
    
    get_process_name(event.process_name, MAX_PROCESS_NAME);
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    update_stats(0);
    return 0;
}

// 通用uprobe - 用于动态附加到发现的函数
SEC("uprobe/generic_ssl_function")
int uprobe_generic_ssl_function(struct pt_regs *ctx)
{
    struct key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.tid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
    event.function_type = 0; // 通用类型
    event.key_type = 0;
    
    get_process_name(event.process_name, MAX_PROCESS_NAME);
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    bpf_printk("Generic SSL function called by PID: %d (%s)", 
               event.pid, event.process_name);
    
    update_stats(0);
    return 0;
} 