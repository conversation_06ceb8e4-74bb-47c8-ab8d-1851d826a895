#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_KEY_SIZE 128
#define MAX_SESSIONS 1024
#define MAX_RANDOM_SIZE 64

// 密钥派生数据结构
struct key_derivation_data {
    u32 pid;
    u64 timestamp;
    u8 function_type;  // 1=HKDF, 2=PRF, 3=P_hash, 4=KDF
    u8 pre_master_secret[48];
    u8 client_random[32];
    u8 server_random[32];
    u8 master_secret[48];
    u8 session_keys[128];  // 存储会话密钥
    u32 pre_master_len;
    u32 master_len;
    u32 session_keys_len;
    u8 has_pre_master;
    u8 has_master;
    u8 has_randoms;
    u8 tls_version;  // 12=TLS1.2, 13=TLS1.3
};

// BPF Maps
struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, MAX_SESSIONS);
    __type(key, u32);  // PID
    __type(value, struct key_derivation_data);
} key_sessions SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(u32));
    __uint(value_size, sizeof(u32));
} key_events SEC(".maps");

// 函数名匹配辅助函数
static __always_inline int is_key_derivation_func(const char *func_name) {
    // 检查是否为密钥派生相关函数
    // 这里使用简化的匹配逻辑
    return 1; // 先返回1，实际应该做字符串匹配
}

// 安全读取用户空间数据
static __always_inline int safe_read_user_data(void *dst, const void *src, u32 size) {
    if (size > MAX_KEY_SIZE) return 0;
    long ret = bpf_probe_read_user(dst, size, src);
    return ret == 0 ? size : 0;
}

// 拦截HKDF-Extract函数 (TLS 1.3主要密钥派生)
SEC("uprobe/HKDF_extract")
int BPF_KPROBE(uprobe_hkdf_extract, 
               unsigned char *prk,     // 输出：伪随机密钥  
               size_t *prk_len,        // 输出长度
               const unsigned char *salt, size_t salt_len,  // 盐值
               const unsigned char *ikm, size_t ikm_len)    // 输入密钥材料
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 获取或创建会话数据
    struct key_derivation_data session = {0};
    struct key_derivation_data *existing = bpf_map_lookup_elem(&key_sessions, &pid);
    if (existing) {
        session = *existing;
    }
    
    session.pid = pid;
    session.timestamp = bpf_ktime_get_ns();
    session.function_type = 1; // HKDF
    session.tls_version = 13;  // TLS 1.3
    
    // 提取输入密钥材料（通常是pre-master secret）
    if (ikm && ikm_len > 0 && ikm_len <= 48) {
        if (safe_read_user_data(session.pre_master_secret, ikm, ikm_len)) {
            session.pre_master_len = ikm_len;
            session.has_pre_master = 1;
        }
    }
    
    bpf_map_update_elem(&key_sessions, &pid, &session, BPF_ANY);
    bpf_printk("🔑 拦截HKDF_extract, PID: %d, ikm_len: %d", pid, ikm_len);
    
    return 0;
}

// 拦截HKDF-Extract返回值
SEC("uretprobe/HKDF_extract")
int BPF_KRETPROBE(uretprobe_hkdf_extract, int ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct key_derivation_data *session = bpf_map_lookup_elem(&key_sessions, &pid);
    if (!session) return 0;
    
    if (ret == 1) {  // OpenSSL成功返回1
        session->has_master = 1;
        bpf_printk("✅ HKDF_extract成功, PID: %d", pid);
    }
    
    return 0;
}

// 拦截HKDF-Expand函数 (TLS 1.3会话密钥派生)
SEC("uprobe/HKDF_expand")
int BPF_KPROBE(uprobe_hkdf_expand,
               unsigned char *okm,        // 输出密钥材料
               size_t okm_len,           // 输出长度
               const unsigned char *prk, size_t prk_len,  // 伪随机密钥
               const unsigned char *info, size_t info_len) // 上下文信息
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct key_derivation_data *session = bpf_map_lookup_elem(&key_sessions, &pid);
    if (!session) return 0;
    
    session->function_type = 1; // HKDF
    bpf_printk("🔑 拦截HKDF_expand, PID: %d, okm_len: %d", pid, okm_len);
    
    return 0;
}

// 拦截PRF函数 (TLS 1.2主要密钥派生)
SEC("uprobe/tls1_PRF")
int BPF_KPROBE(uprobe_tls1_prf,
               void *digest,              // 摘要算法
               const unsigned char *secret, int secret_len,  // 密钥
               const char *label,         // 标签
               const unsigned char *seed1, int seed1_len,    // 种子1
               const unsigned char *seed2, int seed2_len,    // 种子2  
               unsigned char *out, int olen)                 // 输出
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct key_derivation_data session = {0};
    struct key_derivation_data *existing = bpf_map_lookup_elem(&key_sessions, &pid);
    if (existing) {
        session = *existing;
    }
    
    session.pid = pid;
    session.timestamp = bpf_ktime_get_ns();
    session.function_type = 2; // PRF
    session.tls_version = 12;  // TLS 1.2
    
    // 提取密钥（pre-master或master secret）
    if (secret && secret_len > 0 && secret_len <= 48) {
        if (safe_read_user_data(session.pre_master_secret, secret, secret_len)) {
            session.pre_master_len = secret_len;
            session.has_pre_master = 1;
        }
    }
    
    // 提取随机数（通常seed1是client_random，seed2是server_random）
    if (seed1 && seed1_len == 32) {
        if (safe_read_user_data(session.client_random, seed1, 32)) {
            session.has_randoms = 1;
        }
    }
    if (seed2 && seed2_len == 32) {
        safe_read_user_data(session.server_random, seed2, 32);
    }
    
    bpf_map_update_elem(&key_sessions, &pid, &session, BPF_ANY);
    bpf_printk("🔑 拦截tls1_PRF, PID: %d, secret_len: %d", pid, secret_len);
    
    return 0;
}

// 拦截PRF返回值，获取派生的master secret
SEC("uretprobe/tls1_PRF")
int BPF_KRETPROBE(uretprobe_tls1_prf, int ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct key_derivation_data *session = bpf_map_lookup_elem(&key_sessions, &pid);
    if (!session) return 0;
    
    if (ret == 1) {  // 成功
        session->has_master = 1;
        bpf_printk("✅ tls1_PRF成功, PID: %d", pid);
        
        // 如果我们收集到了关键数据，发送事件
        if (session->has_pre_master && session->has_randoms) {
            bpf_printk("📤 密钥派生数据收集完成! PID: %d", pid);
            bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU,
                                session, sizeof(*session));
        }
    }
    
    return 0;
}

// 拦截P_hash函数（PRF的底层实现）
SEC("uprobe/tls1_P_hash")
int BPF_KPROBE(uprobe_tls1_p_hash,
               void *md,                  // 摘要算法
               const unsigned char *sec, int sec_len,    // 密钥
               const unsigned char *seed, int seed_len,  // 种子
               unsigned char *out, int olen)             // 输出
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    struct key_derivation_data *session = bpf_map_lookup_elem(&key_sessions, &pid);
    if (!session) return 0;
    
    session->function_type = 3; // P_hash
    bpf_printk("🔧 拦截tls1_P_hash, PID: %d, sec_len: %d", pid, sec_len);
    
    return 0;
}

// 通用密钥派生函数检测
SEC("uprobe")
int uprobe_generic_kdf(struct pt_regs *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // 通过寄存器获取函数参数
    void *arg1 = (void *)PT_REGS_PARM1(ctx);
    void *arg2 = (void *)PT_REGS_PARM2(ctx);
    size_t len = PT_REGS_PARM3(ctx);
    
    bpf_printk("🔍 检测到可能的密钥派生函数, PID: %d", pid);
    
    return 0;
}

// 监控SSL握手完成
SEC("uprobe/SSL_do_handshake")
int BPF_KPROBE(uprobe_ssl_do_handshake, void *ssl)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    bpf_printk("🤝 SSL握手开始, PID: %d", pid);
    return 0;
}

SEC("uretprobe/SSL_do_handshake")
int BPF_KRETPROBE(uretprobe_ssl_do_handshake, int ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    if (ret == 1) { // 握手成功
        bpf_printk("✅ SSL握手完成, PID: %d", pid);
        
        // 检查是否有完整的密钥派生数据
        struct key_derivation_data *session = bpf_map_lookup_elem(&key_sessions, &pid);
        if (session && session->has_pre_master) {
            bpf_printk("📋 发送最终密钥数据, PID: %d", pid);
            bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU,
                                session, sizeof(*session));
        }
    }
    
    return 0;
}

char LICENSE[] SEC("license") = "GPL"; 