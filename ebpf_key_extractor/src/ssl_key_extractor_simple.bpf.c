#include <linux/bpf.h>
#include <linux/ptrace.h>

char LICENSE[] __attribute__((section("license"), used)) = "GPL";

// Map定义结构
struct bpf_map_def {
    unsigned int type;
    unsigned int key_size;
    unsigned int value_size;
    unsigned int max_entries;
    unsigned int map_flags;
};

// SSL密钥事件结构
struct ssl_key_event {
    __u64 timestamp;
    __u32 pid;
    __u32 tid;
    __u32 function_type;
    __u32 key_length;
    char comm[16];
    __u64 ssl_ptr;  // SSL结构体指针
    __u8 key_data[64];  // 存储前64字节密钥数据
    __u8 success;       // 是否成功提取密钥
};

// 事件Map
struct bpf_map_def __attribute__((section("maps"), used)) ssl_events = {
    .type = BPF_MAP_TYPE_PERF_EVENT_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(__u32),
    .max_entries = 256,
};

// BPF helper函数声明
static long (*bpf_ktime_get_ns)(void) = (void *) 5;
static long (*bpf_get_current_pid_tgid)(void) = (void *) 14;
static long (*bpf_get_current_comm)(void *buf, __u32 size_of_buf) = (void *) 16;
static long (*bpf_perf_event_output)(void *ctx, void *map, __u64 flags, void *data, __u64 size) = (void *) 25;
static long (*bpf_probe_read_user)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) 112;

#define BPF_F_CURRENT_CPU 0xffffffffULL

// 简化的辅助函数：尝试读取SSL结构体中的数据
static int try_extract_ssl_data(void *ssl_ptr, struct ssl_key_event *event)
{
    if (!ssl_ptr) return -1;
    
    event->ssl_ptr = (__u64)ssl_ptr;
    
    // 尝试读取SSL结构体的前64字节作为示例
    // 这不是真正的密钥，只是为了验证我们可以读取SSL结构体
    if (bpf_probe_read_user(event->key_data, 64, ssl_ptr) != 0) {
        return -1;
    }
    
    event->key_length = 64;
    event->success = 1;
    return 0;
}

// SSL_CTX_new 函数拦截
__attribute__((section("uprobe/SSL_CTX_new"), used))
int trace_ssl_ctx_new(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 1; // SSL_CTX_new
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_write 函数拦截 - 尝试提取SSL结构体数据
__attribute__((section("uprobe/SSL_write"), used))
int trace_ssl_write(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 2; // SSL_write
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // 从寄存器读取SSL指针 - 使用直接寄存器访问
    // 在x86_64上，第一个参数通常在rdi寄存器中
    void *ssl_ptr = (void *)ctx->di;
    
    if (ssl_ptr) {
        try_extract_ssl_data(ssl_ptr, &event);
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_read 函数拦截
__attribute__((section("uprobe/SSL_read"), used))
int trace_ssl_read(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    __u64 pid_tgid = bpf_get_current_pid_tgid();
    event.pid = pid_tgid >> 32;
    event.tid = pid_tgid & 0xffffffff;
    event.function_type = 3; // SSL_read
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // 从寄存器读取SSL指针
    void *ssl_ptr = (void *)ctx->di;
    
    if (ssl_ptr) {
        try_extract_ssl_data(ssl_ptr, &event);
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
} 