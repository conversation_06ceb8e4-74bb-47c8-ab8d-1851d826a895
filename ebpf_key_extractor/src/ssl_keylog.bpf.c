#include <linux/bpf.h>
#include <linux/ptrace.h>

char LICENSE[] __attribute__((section("license"), used)) = "GPL";

// Map定义结构
struct bpf_map_def {
    unsigned int type;
    unsigned int key_size;
    unsigned int value_size;
    unsigned int max_entries;
    unsigned int map_flags;
};

// SSL密钥事件结构
struct ssl_key_event {
    __u64 timestamp;
    __u32 pid;
    __u32 function_type;
    __u32 data_len;
    char comm[16];
    __u8 key_data[64];  // 存储密钥数据
};

// 事件Map
struct bpf_map_def __attribute__((section("maps"), used)) ssl_events = {
    .type = BPF_MAP_TYPE_PERF_EVENT_ARRAY,
    .key_size = sizeof(__u32),
    .value_size = sizeof(__u32),
    .max_entries = 256,
};

// BPF helper函数声明
static long (*bpf_ktime_get_ns)(void) = (void *) 5;
static long (*bpf_get_current_pid_tgid)(void) = (void *) 14;
static long (*bpf_get_current_comm)(void *buf, __u32 size_of_buf) = (void *) 16;
static long (*bpf_perf_event_output)(void *ctx, void *map, __u64 flags, void *data, __u64 size) = (void *) 25;
static long (*bpf_probe_read_user)(void *dst, __u32 size, const void *unsafe_ptr) = (void *) 112;

#define BPF_F_CURRENT_CPU 0xffffffffULL

// x86_64寄存器访问宏
#define PT_REGS_PARM1(x) ((x)->di)
#define PT_REGS_PARM2(x) ((x)->si)
#define PT_REGS_PARM3(x) ((x)->dx)
#define PT_REGS_RC(x) ((x)->ax)

// SSL_CTX_new 函数拦截
__attribute__((section("uprobe/SSL_CTX_new"), used))
int trace_ssl_ctx_new(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 1; // SSL_CTX_new
    event.data_len = 0;
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_write 函数拦截 - 尝试读取写入的数据
__attribute__((section("uprobe/SSL_write"), used))
int trace_ssl_write(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 2; // SSL_write
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // 尝试读取SSL_write的参数
    // SSL_write(SSL *ssl, const void *buf, int num)
    void *buf = (void *)PT_REGS_PARM2(ctx);  // 第二个参数是数据缓冲区
    int num = (int)PT_REGS_PARM3(ctx);       // 第三个参数是数据长度
    
    if (buf && num > 0 && num <= sizeof(event.key_data)) {
        if (bpf_probe_read_user(event.key_data, num, buf) == 0) {
            event.data_len = num;
        }
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_read 函数拦截 - 尝试读取接收的数据
__attribute__((section("uprobe/SSL_read"), used))
int trace_ssl_read(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 3; // SSL_read
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    // SSL_read的参数类似SSL_write
    void *buf = (void *)PT_REGS_PARM2(ctx);
    int num = (int)PT_REGS_PARM3(ctx);
    
    if (buf && num > 0 && num <= sizeof(event.key_data)) {
        if (bpf_probe_read_user(event.key_data, num, buf) == 0) {
            event.data_len = num;
        }
    }
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
}

// SSL_connect 函数拦截 - SSL握手
__attribute__((section("uprobe/SSL_connect"), used))
int trace_ssl_connect(struct pt_regs *ctx)
{
    struct ssl_key_event event = {};
    
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = 4; // SSL_connect
    event.data_len = 0;
    
    bpf_get_current_comm(event.comm, sizeof(event.comm));
    
    bpf_perf_event_output(ctx, &ssl_events, BPF_F_CURRENT_CPU, &event, sizeof(event));
    
    return 0;
} 