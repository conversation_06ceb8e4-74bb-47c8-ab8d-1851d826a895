#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <sys/time.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <argp.h>

// 与eBPF程序中相同的数据结构
struct key_derivation_data {
    uint32_t pid;
    uint64_t timestamp;
    uint8_t function_type;  // 1=HKDF, 2=PRF, 3=P_hash, 4=KDF
    uint8_t pre_master_secret[48];
    uint8_t client_random[32];
    uint8_t server_random[32];
    uint8_t master_secret[48];
    uint8_t session_keys[128];
    uint32_t pre_master_len;
    uint32_t master_len; 
    uint32_t session_keys_len;
    uint8_t has_pre_master;
    uint8_t has_master;
    uint8_t has_randoms;
    uint8_t tls_version;  // 12=TLS1.2, 13=TLS1.3
};

volatile sig_atomic_t exiting = 0;

static void sig_int(int signo) {
    exiting = 1;
}

// 将字节数组转换为十六进制字符串
void bytes_to_hex(const uint8_t *bytes, int len, char *hex_str) {
    for (int i = 0; i < len; i++) {
        sprintf(hex_str + i * 2, "%02x", bytes[i]);
    }
    hex_str[len * 2] = '\0';
}

// 获取函数类型名称
const char* get_function_type_name(uint8_t type) {
    switch (type) {
        case 1: return "HKDF";
        case 2: return "PRF";
        case 3: return "P_hash";
        case 4: return "KDF";
        default: return "Unknown";
    }
}

// 输出密钥派生分析
void analyze_key_derivation(const struct key_derivation_data *data) {
    char client_random_hex[65] = {0};
    char server_random_hex[65] = {0};
    char pre_master_hex[97] = {0};
    char master_hex[97] = {0};
    
    struct timeval tv;
    gettimeofday(&tv, NULL);
    
    printf("\n═══════════════════════════════════════════════════════════════\n");
    printf("🔍 密钥派生拦截分析报告\n");      
    printf("═══════════════════════════════════════════════════════════════\n");
    printf("时间戳: %ld.%06ld\n", tv.tv_sec, tv.tv_usec);
    printf("进程PID: %d\n", data->pid);
    printf("派生函数: %s\n", get_function_type_name(data->function_type));
    printf("TLS版本: %s\n", data->tls_version == 12 ? "TLS 1.2" : 
                          data->tls_version == 13 ? "TLS 1.3" : "Unknown");
    printf("───────────────────────────────────────────────────────────────\n");
    
    // 数据完整性检查
    printf("📊 数据完整性状态:\n");
    printf("  Pre-Master Secret: %s (%d 字节)\n", 
           data->has_pre_master ? "✅ 已获取" : "❌ 未获取", data->pre_master_len);
    printf("  Client Random:     %s\n", 
           data->has_randoms ? "✅ 已获取" : "❌ 未获取");
    printf("  Server Random:     %s\n", 
           data->has_randoms ? "✅ 已获取" : "❌ 未获取");
    printf("  Master Secret:     %s\n", 
           data->has_master ? "✅ 已获取" : "❌ 未获取");
    
    // 输出密钥数据（十六进制）
    if (data->has_pre_master && data->pre_master_len > 0) {
        bytes_to_hex(data->pre_master_secret, data->pre_master_len, pre_master_hex);
        printf("\n🔑 密钥材料 (十六进制):\n");
        printf("  Pre-Master Secret: %s\n", pre_master_hex);
    }
    
    if (data->has_randoms) {
        bytes_to_hex(data->client_random, 32, client_random_hex);
        bytes_to_hex(data->server_random, 32, server_random_hex);
        printf("  Client Random:     %s\n", client_random_hex);
        printf("  Server Random:     %s\n", server_random_hex);
    }
    
    // 尝试生成keylog格式
    printf("\n📝 Keylog生成状态:\n");
    if (data->has_randoms && data->has_pre_master && 
        data->pre_master_len == 48) {
        // 生成CLIENT_RANDOM格式（使用pre-master作为master key）
        printf("✅ 满足keylog生成条件\n");
        printf("\n🎯 标准Keylog格式输出:\n");
        printf("CLIENT_RANDOM %s %s\n", client_random_hex, pre_master_hex);
        
        // 保存到文件
        FILE *keylog_file = fopen("/tmp/ssl_keylog.txt", "a");
        if (keylog_file) {
            fprintf(keylog_file, "CLIENT_RANDOM %s %s\n", 
                   client_random_hex, pre_master_hex);
            fclose(keylog_file);
            printf("📁 已保存到文件: /tmp/ssl_keylog.txt\n");
        }
    } else {
        printf("❌ 数据不完整，无法生成keylog\n");
        printf("   需要: client_random(32字节) + pre_master_secret(48字节)\n");
        printf("   当前: client_random(%s) + pre_master_secret(%d字节)\n",
               data->has_randoms ? "有" : "无", data->pre_master_len);
    }
    
    printf("═══════════════════════════════════════════════════════════════\n");
}

// 事件处理回调
static int handle_event(void *ctx, void *data, size_t data_sz) {
    const struct key_derivation_data *e = data;
    analyze_key_derivation(e);
    return 0;
}

// libbpf错误回调
static int libbpf_print_fn(enum libbpf_print_level level, 
                          const char *format, va_list args) {
    if (level == LIBBPF_DEBUG)
        return 0;
    return vfprintf(stderr, format, args);
}

// 检查OpenSSL库中是否存在目标函数
int check_function_exists(const char *lib_path, const char *func_name) {
    char cmd[512];
    snprintf(cmd, sizeof(cmd), 
             "nm -D %s 2>/dev/null | grep -w %s >/dev/null", 
             lib_path, func_name);
    return system(cmd) == 0;
}

// 获取函数在库中的偏移地址
unsigned long get_function_offset(const char *lib_path, const char *func_name) {
    char cmd[512];
    char offset_str[32];
    unsigned long offset = 0;
    
    snprintf(cmd, sizeof(cmd), 
             "readelf -s %s 2>/dev/null | grep -w %s | awk '{print $2}' | head -1",
             lib_path, func_name);
    
    FILE *fp = popen(cmd, "r");
    if (fp) {
        if (fgets(offset_str, sizeof(offset_str), fp)) {
            offset = strtoul(offset_str, NULL, 16);
        }
        pclose(fp);
    }
    
    return offset;
}

int main(int argc, char **argv) {
    struct bpf_object *obj;
    struct bpf_program *prog;
    struct bpf_link *links[20] = {0};  // 支持更多链接
    struct perf_buffer *pb = NULL;
    int err = 0;
    int link_count = 0;

    // 设置libbpf回调
    libbpf_set_print(libbpf_print_fn);

    // 注册信号处理器
    signal(SIGINT, sig_int);
    signal(SIGTERM, sig_int);

    printf("🔑 启动密钥派生拦截器 (Key Derivation Interceptor)\n");
    printf("════════════════════════════════════════════════════════════════\n");
    printf("目标: 拦截底层密钥派生函数，提取TLS密钥材料\n");
    printf("支持: HKDF (TLS 1.3), PRF (TLS 1.2), P_hash等函数\n");
    printf("测试: curl -k https://httpbin.org/get\n");
    printf("按Ctrl+C停止\n");
    printf("════════════════════════════════════════════════════════════════\n");

    // 加载eBPF程序
    obj = bpf_object__open_file("/root/ebpf/ebpf_key_extractor/src/key_derivation_interceptor.bpf.o", NULL);
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "❌ 打开eBPF程序失败: %s\n", strerror(errno));
        return 1;
    }

    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "❌ 加载eBPF程序失败: %s\n", strerror(-err));
        goto cleanup;
    }

    printf("✅ eBPF程序加载成功\n");

    // 目标函数列表
    struct {
        const char *name;
        int has_retprobe;
    } target_functions[] = {
        {"HKDF_extract", 1},
        {"HKDF_expand", 0},
        {"tls1_PRF", 1},
        {"tls1_P_hash", 0},
        {"SSL_do_handshake", 1},
    };

    // SSL库路径候选
    const char *ssl_libs[] = {
        "/lib64/libssl.so.1.1",
        "/usr/lib64/libssl.so.1.1", 
        "/lib/x86_64-linux-gnu/libssl.so.1.1",
        "/usr/lib/x86_64-linux-gnu/libssl.so.1.1",
        "/lib64/libssl.so.3",
        "/usr/lib64/libssl.so.3",
    };

    // 找到有效的SSL库
    const char *ssl_lib = NULL;
    for (int i = 0; i < sizeof(ssl_libs)/sizeof(ssl_libs[0]); i++) {
        if (access(ssl_libs[i], R_OK) == 0) {
            ssl_lib = ssl_libs[i];
            printf("📚 使用SSL库: %s\n", ssl_lib);
            break;
        }
    }

    if (!ssl_lib) {
        fprintf(stderr, "❌ 未找到SSL库\n");
        goto cleanup;
    }

    // 附加uprobe到目标函数
    for (int i = 0; i < sizeof(target_functions)/sizeof(target_functions[0]); i++) {
        const char *func_name = target_functions[i].name;
        
        // 检查函数是否存在
        if (!check_function_exists(ssl_lib, func_name)) {
            printf("⚠️  函数 %s 在 %s 中不存在，跳过\n", func_name, ssl_lib);
            continue;
        }
        
        unsigned long offset = get_function_offset(ssl_lib, func_name);
        if (offset == 0) {
            printf("⚠️  无法获取函数 %s 的偏移地址\n", func_name);
            continue;
        }
        
        // uprobe
        char section_name[128];
        snprintf(section_name, sizeof(section_name), "uprobe/%s", func_name);
        prog = bpf_object__find_program_by_name(obj, section_name);
        if (prog) {
            links[link_count] = bpf_program__attach_uprobe(prog, false, -1, ssl_lib, offset);
            if (!libbpf_get_error(links[link_count])) {
                printf("✅ 附加uprobe: %s (偏移: 0x%lx)\n", func_name, offset);
                link_count++;
            } else {
                printf("❌ 附加uprobe失败: %s\n", func_name);
            }
        }
        
        // uretprobe (如果需要)
        if (target_functions[i].has_retprobe) {
            snprintf(section_name, sizeof(section_name), "uretprobe/%s", func_name);
            prog = bpf_object__find_program_by_name(obj, section_name);
            if (prog) {
                links[link_count] = bpf_program__attach_uprobe(prog, true, -1, ssl_lib, offset);
                if (!libbpf_get_error(links[link_count])) {
                    printf("✅ 附加uretprobe: %s (偏移: 0x%lx)\n", func_name, offset);
                    link_count++;
                } else {
                    printf("❌ 附加uretprobe失败: %s\n", func_name);
                }
            }
        }
    }

    if (link_count == 0) {
        fprintf(stderr, "❌ 没有成功附加任何探针\n");
        goto cleanup;
    }

    printf("🎯 成功附加 %d 个探针，开始监控密钥派生过程...\n", link_count);
    printf("────────────────────────────────────────────────────────────────\n");

    // 设置perf event buffer
    pb = perf_buffer__new(bpf_object__find_map_by_name(obj, "key_events"), 
                         8, handle_event, NULL, NULL, NULL);
    if (libbpf_get_error(pb)) {
        fprintf(stderr, "❌ 创建perf buffer失败\n");
        goto cleanup;
    }

    // 事件循环
    printf("⏳ 等待密钥派生事件...\n\n");
    while (!exiting) {
        err = perf_buffer__poll(pb, 100); // 100ms超时
        if (err < 0 && err != -EINTR) {
            fprintf(stderr, "❌ 轮询perf buffer出错: %d\n", err);
            break;
        }
    }

cleanup:
    perf_buffer__free(pb);
    for (int i = 0; i < link_count; i++) {
        bpf_link__destroy(links[i]);
    }
    bpf_object__close(obj);
    
    printf("\n🔚 密钥派生拦截器已停止\n");
    return err != 0;
} 