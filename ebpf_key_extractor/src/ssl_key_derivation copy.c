#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <signal.h>
#include <string.h>
#include <errno.h>
#include <time.h>
#include <sys/time.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

// 与eBPF程序中相同的数据结构
struct ssl_key_data {
    uint32_t pid;
    uint64_t timestamp;
    uint8_t client_random[32];
    uint8_t server_random[32]; 
    uint8_t master_key[48];
    uint32_t client_random_len;
    uint32_t server_random_len;
    uint32_t master_key_len;
    uint8_t has_client_random;
    uint8_t has_server_random;
    uint8_t has_master_key;
};

volatile sig_atomic_t exiting = 0;

static void sig_int(int signo) {
    exiting = 1;
}

// 将字节数组转换为十六进制字符串
void bytes_to_hex(const uint8_t *bytes, int len, char *hex_str) {
    for (int i = 0; i < len; i++) {
        sprintf(hex_str + i * 2, "%02x", bytes[i]);
    }
    hex_str[len * 2] = '\0';
}

// 输出标准keylog格式
void output_keylog(const struct ssl_key_data *data) {
    char client_random_hex[65] = {0};  // 32 bytes = 64 hex chars + null
    char master_key_hex[97] = {0};     // 48 bytes = 96 hex chars + null
    
    // 转换为十六进制字符串
    bytes_to_hex(data->client_random, data->client_random_len, client_random_hex);
    bytes_to_hex(data->master_key, data->master_key_len, master_key_hex);
    
    // 获取当前时间戳
    struct timeval tv;
    gettimeofday(&tv, NULL);
    
    printf("\n[%ld.%06ld] SSL密钥提取成功 - PID: %d\n", 
           tv.tv_sec, tv.tv_usec, data->pid);
    printf("数据完整性: client_random: %s, master_key: %s\n",
           data->has_client_random ? "YES" : "NO",
           data->has_master_key ? "YES" : "NO");
    printf("数据长度: client_random: %d, master_key: %d\n",
           data->client_random_len, data->master_key_len);
    
    // 输出标准keylog格式（与SSLKEYLOGFILE兼容）
    if (data->has_client_random && data->has_master_key && 
        data->client_random_len == 32 && data->master_key_len == 48) {
        printf("CLIENT_RANDOM %s %s\n", client_random_hex, master_key_hex);
        printf("标准keylog格式输出完成\n");
    } else {
        printf("数据不完整，无法生成标准keylog格式\n");
        printf("需要: client_random(32字节), master_key(48字节)\n");
        printf("实际: client_random(%d字节), master_key(%d字节)\n", 
               data->client_random_len, data->master_key_len);
    }
    printf("─────────────────────────────────────────────────────────────\n");
}

// perf event回调函数
static int handle_event(void *ctx, void *data, size_t data_sz) {
    const struct ssl_key_data *e = data;
    output_keylog(e);
    return 0;
}

// libbpf错误回调
static int libbpf_print_fn(enum libbpf_print_level level, const char *format, va_list args) {
    if (level == LIBBPF_DEBUG)
        return 0;
    return vfprintf(stderr, format, args);
}

int main(int argc, char **argv) {
    struct bpf_object *obj;
    struct bpf_program *prog;
    struct bpf_link *links[10] = {0};  // 最多10个链接
    struct perf_buffer *pb = NULL;
    int err = 0;
    int link_count = 0;

    // 设置libbpf回调
    libbpf_set_print(libbpf_print_fn);

    // 注册信号处理器
    signal(SIGINT, sig_int);
    signal(SIGTERM, sig_int);

    printf("启动SSL密钥派生拦截器\n");
    printf("使用方法: 在另一个终端运行curl命令，这里将显示提取的SSL密钥\n");
    printf("测试命令: curl -k https://httpbin.org/get\n");
    printf("按Ctrl+C停止\n");
    printf("─────────────────────────────────────────────────────────────\n");

    // 加载eBPF程序
    obj = bpf_object__open_file("src/ssl_key_derivation.bpf.o", NULL);
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "打开eBPF程序失败: %s\n", strerror(errno));
        return 1;
    }

    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "加载eBPF程序失败: %s\n", strerror(-err));
        goto cleanup;
    }

    printf("eBPF程序加载成功\n");

    // 附加uprobe到SSL函数
    const char *functions[] = {
        "SSL_get_client_random",
        "SSL_SESSION_get_master_key"
    };
    
    const char *ssl_lib = "/lib64/libssl.so.1.1";
    
    for (int i = 0; i < 2; i++) {
        char section_name[128];
        
        // uprobe
        snprintf(section_name, sizeof(section_name), "uprobe/%s", functions[i]);
        prog = bpf_object__find_program_by_name(obj, section_name);
        if (prog) {
            // 获取函数偏移
            char cmd[256];
            snprintf(cmd, sizeof(cmd), 
                "readelf -s %s | grep -w %s | awk '{print $2}' | head -1", 
                ssl_lib, functions[i]);
            
            FILE *fp = popen(cmd, "r");
            if (fp) {
                char offset_str[32];
                if (fgets(offset_str, sizeof(offset_str), fp)) {
                    unsigned long offset = strtoul(offset_str, NULL, 16);
                    if (offset > 0) {
                        links[link_count] = bpf_program__attach_uprobe(prog, false, -1, ssl_lib, offset);
                        if (!libbpf_get_error(links[link_count])) {
                            printf("附加uprobe成功: %s (偏移: 0x%lx)\n", functions[i], offset);
                            link_count++;
                        }
                    }
                }
                pclose(fp);
            }
        }
        
        // uretprobe
        snprintf(section_name, sizeof(section_name), "uretprobe/%s", functions[i]);
        prog = bpf_object__find_program_by_name(obj, section_name);
        if (prog) {
            // 获取函数偏移
            char cmd[256];
            snprintf(cmd, sizeof(cmd), 
                "readelf -s %s | grep -w %s | awk '{print $2}' | head -1", 
                ssl_lib, functions[i]);
            
            FILE *fp = popen(cmd, "r");
            if (fp) {
                char offset_str[32];
                if (fgets(offset_str, sizeof(offset_str), fp)) {
                    unsigned long offset = strtoul(offset_str, NULL, 16);
                    if (offset > 0) {
                        links[link_count] = bpf_program__attach_uprobe(prog, true, -1, ssl_lib, offset);
                        if (!libbpf_get_error(links[link_count])) {
                            printf("附加uretprobe成功: %s (偏移: 0x%lx)\n", functions[i], offset);
                            link_count++;
                        }
                    }
                }
                pclose(fp);
            }
        }
    }

    if (link_count == 0) {
        fprintf(stderr, "没有成功附加任何探针\n");
        goto cleanup;
    }

    printf("成功附加 %d 个探针\n", link_count);

    // 设置perf event buffer
    pb = perf_buffer__new(bpf_object__find_map_by_name(obj, "ssl_events"), 8, handle_event, NULL, NULL, NULL);
    if (libbpf_get_error(pb)) {
        fprintf(stderr, "创建perf buffer失败\n");
        err = -1;
        goto cleanup;
    }

    printf("开始监控SSL密钥派生...\n\n");

    // 主循环
    while (!exiting) {
        err = perf_buffer__poll(pb, 100);
        if (err < 0 && err != -EINTR) {
            fprintf(stderr, "perf buffer轮询错误: %d\n", err);
            break;
        }
    }

cleanup:
    printf("\n正在清理资源...\n");
    
    perf_buffer__free(pb);
    
    for (int i = 0; i < link_count; i++) {
        if (links[i]) {
            bpf_link__destroy(links[i]);
        }
    }
    
    bpf_object__close(obj);
    printf("清理完成\n");
    
    return err != 0;
} 