#!/bin/bash

# 密钥派生拦截器可行性测试脚本

set -e

echo "🔍 密钥派生拦截器可行性探索"
echo "════════════════════════════════════════════════════════════════"
echo "目标: 测试底层密钥派生函数拦截的可行性"
echo "思路: 专注于HKDF、PRF、P_hash等通用密钥派生函数"
echo "════════════════════════════════════════════════════════════════"

# 工作目录
WORK_DIR="/root/ebpf/ebpf_key_extractor"
cd "$WORK_DIR"

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "❌ 此脚本需要root权限运行"
   exit 1
fi

# 步骤1: 分析系统SSL库中的密钥派生函数
echo "📊 步骤1: 分析系统SSL库"
echo "────────────────────────────────────────────────────────────────"

# 查找SSL库
SSL_LIBS=(
    "/lib64/libssl.so.1.1"
    "/usr/lib64/libssl.so.1.1"
    "/lib/x86_64-linux-gnu/libssl.so.1.1"
    "/usr/lib/x86_64-linux-gnu/libssl.so.1.1"
    "/lib64/libssl.so.3"
    "/usr/lib64/libssl.so.3"
)

FOUND_SSL_LIB=""
for lib in "${SSL_LIBS[@]}"; do
    if [[ -f "$lib" ]]; then
        FOUND_SSL_LIB="$lib"
        echo "✅ 找到SSL库: $lib"
        break
    fi
done

if [[ -z "$FOUND_SSL_LIB" ]]; then
    echo "❌ 未找到SSL库"
    exit 1
fi

# 分析密钥派生函数
echo "🔍 分析密钥派生函数:"
echo ""

# HKDF函数（TLS 1.3）
echo "🔑 HKDF函数 (TLS 1.3密钥派生):"
HKDF_FUNCS=$(nm -D "$FOUND_SSL_LIB" 2>/dev/null | grep -i hkdf | awk '{print $3}' || true)
if [[ -n "$HKDF_FUNCS" ]]; then
    echo "$HKDF_FUNCS" | while read func; do
        echo "  ✅ $func"
    done
else
    echo "  ❌ 未找到HKDF函数"
fi

# PRF函数（TLS 1.2）
echo "🔑 PRF函数 (TLS 1.2密钥派生):"
PRF_FUNCS=$(nm -D "$FOUND_SSL_LIB" 2>/dev/null | grep -E "(PRF|prf)" | awk '{print $3}' || true)
if [[ -n "$PRF_FUNCS" ]]; then
    echo "$PRF_FUNCS" | while read func; do
        echo "  ✅ $func"
    done
else
    echo "  ❌ 未找到PRF函数"
fi

# P_hash函数（PRF底层实现）
echo "🔑 P_hash函数 (PRF底层实现):"
P_HASH_FUNCS=$(nm -D "$FOUND_SSL_LIB" 2>/dev/null | grep -i "p_hash" | awk '{print $3}' || true)
if [[ -n "$P_HASH_FUNCS" ]]; then
    echo "$P_HASH_FUNCS" | while read func; do
        echo "  ✅ $func"
    done
else
    echo "  ❌ 未找到P_hash函数"
fi

# 其他密钥派生相关函数
echo "🔑 其他密钥派生函数:"
OTHER_KDF_FUNCS=$(nm -D "$FOUND_SSL_LIB" 2>/dev/null | grep -E "(kdf|KDF|derive|DERIVE)" | awk '{print $3}' || true)
if [[ -n "$OTHER_KDF_FUNCS" ]]; then
    echo "$OTHER_KDF_FUNCS" | while read func; do
        echo "  ✅ $func"
    done
else
    echo "  ❌ 未找到其他密钥派生函数"
fi

echo ""

# 步骤2: 编译密钥派生拦截器
echo "🔨 步骤2: 编译密钥派生拦截器"
echo "────────────────────────────────────────────────────────────────"

# 检查编译依赖
echo "📦 检查编译依赖..."
if ! which gcc >/dev/null 2>&1; then
    echo "❌ 缺少gcc，正在安装..."
    yum install -y gcc
fi

if ! which clang >/dev/null 2>&1; then
    echo "❌ 缺少clang，正在安装..."
    yum install -y clang
fi

if ! pkg-config --exists libbpf 2>/dev/null; then
    echo "❌ 缺少libbpf开发包，正在安装..."
    yum install -y libbpf-devel elfutils-libelf-devel zlib-devel
fi

# 编译程序
echo "🔨 编译密钥派生拦截器..."
make -f Makefile.key_derivation clean
make -f Makefile.key_derivation all

if [[ $? -eq 0 ]]; then
    echo "✅ 编译成功"
else
    echo "❌ 编译失败"
    exit 1
fi

# 步骤3: 运行可行性测试
echo ""
echo "🧪 步骤3: 运行可行性测试"
echo "────────────────────────────────────────────────────────────────"

# 创建测试日志目录
TEST_LOG_DIR="/tmp/key_derivation_test"
mkdir -p "$TEST_LOG_DIR"

echo "📝 测试日志将保存到: $TEST_LOG_DIR"

# 启动密钥派生拦截器（后台运行）
echo "🚀 启动密钥派生拦截器..."
./key_derivation_interceptor > "$TEST_LOG_DIR/interceptor.log" 2>&1 &
INTERCEPTOR_PID=$!

echo "📍 拦截器PID: $INTERCEPTOR_PID"

# 等待拦截器启动
sleep 3

# 测试用例1: curl HTTPS请求
echo "🌐 测试用例1: curl HTTPS请求"
echo "执行命令: curl -k https://httpbin.org/get"

timeout 30 curl -k https://httpbin.org/get >/dev/null 2>&1 || true

# 等待事件处理
sleep 2

# 测试用例2: OpenSSL s_client连接
echo "🔐 测试用例2: OpenSSL s_client连接"
echo "执行命令: openssl s_client -connect httpbin.org:443"

echo "GET /get HTTP/1.1\r\nHost: httpbin.org\r\n\r\n" | \
timeout 30 openssl s_client -connect httpbin.org:443 -quiet >/dev/null 2>&1 || true

# 等待事件处理
sleep 2

# 停止拦截器
echo "🛑 停止密钥派生拦截器..."
kill $INTERCEPTOR_PID 2>/dev/null || true
wait $INTERCEPTOR_PID 2>/dev/null || true

# 步骤4: 分析测试结果
echo ""
echo "📊 步骤4: 分析测试结果"
echo "────────────────────────────────────────────────────────────────"

# 检查拦截器日志
if [[ -f "$TEST_LOG_DIR/interceptor.log" ]]; then
    echo "📄 拦截器日志分析:"
    echo ""
    
    # 统计探针附加情况
    ATTACHED_PROBES=$(grep -c "✅ 附加.*probe" "$TEST_LOG_DIR/interceptor.log" || echo "0")
    echo "🎯 成功附加的探针数量: $ATTACHED_PROBES"
    
    # 检查是否有函数调用被拦截
    INTERCEPTED_CALLS=$(grep -c "拦截" "$TEST_LOG_DIR/interceptor.log" || echo "0")
    echo "🔍 拦截到的函数调用: $INTERCEPTED_CALLS"
    
    # 检查是否生成了密钥数据
    KEY_EVENTS=$(grep -c "密钥派生" "$TEST_LOG_DIR/interceptor.log" || echo "0")
    echo "🔑 密钥派生事件: $KEY_EVENTS"
    
    # 检查是否生成了keylog
    if [[ -f "/tmp/ssl_keylog.txt" ]]; then
        KEYLOG_ENTRIES=$(wc -l < /tmp/ssl_keylog.txt || echo "0")
        echo "📝 生成的keylog条目: $KEYLOG_ENTRIES"
    else
        echo "📝 生成的keylog条目: 0"
    fi
    
    echo ""
    echo "📋 详细日志内容:"
    echo "────────────────────────────────────"
    cat "$TEST_LOG_DIR/interceptor.log"
    echo "────────────────────────────────────"
else
    echo "❌ 未找到拦截器日志文件"
fi

# 步骤5: 可行性评估
echo ""
echo "🎯 步骤5: 可行性评估"
echo "════════════════════════════════════════════════════════════════"

# 评估标准
SCORE=0
MAX_SCORE=5

echo "📊 评估指标:"

# 1. SSL库函数可用性
if [[ -n "$HKDF_FUNCS" || -n "$PRF_FUNCS" || -n "$P_HASH_FUNCS" ]]; then
    echo "  ✅ SSL库密钥派生函数可用 (+1分)"
    ((SCORE++))
else
    echo "  ❌ SSL库密钥派生函数不可用 (+0分)"
fi

# 2. 程序编译成功
if [[ -f "./key_derivation_interceptor" ]]; then
    echo "  ✅ 程序编译成功 (+1分)"
    ((SCORE++))
else
    echo "  ❌ 程序编译失败 (+0分)"
fi

# 3. 探针附加成功
if [[ $ATTACHED_PROBES -gt 0 ]]; then
    echo "  ✅ 探针附加成功 (+1分)"
    ((SCORE++))
else
    echo "  ❌ 探针附加失败 (+0分)"
fi

# 4. 函数调用拦截
if [[ $INTERCEPTED_CALLS -gt 0 ]]; then
    echo "  ✅ 成功拦截函数调用 (+1分)"
    ((SCORE++))
else
    echo "  ❌ 未拦截到函数调用 (+0分)"
fi

# 5. 密钥数据提取
if [[ $KEY_EVENTS -gt 0 ]]; then
    echo "  ✅ 成功提取密钥数据 (+1分)"
    ((SCORE++))
else
    echo "  ❌ 未提取到密钥数据 (+0分)"
fi

echo ""
echo "🏆 最终评分: $SCORE/$MAX_SCORE"

# 给出结论
if [[ $SCORE -ge 4 ]]; then
    echo "🎉 结论: 密钥派生拦截方案 【高度可行】"
    echo "   ✅ 能够成功拦截密钥派生函数"
    echo "   ✅ 能够提取关键密钥数据"
    echo "   ✅ 技术路线正确，可投入生产使用"
elif [[ $SCORE -ge 2 ]]; then
    echo "⚠️  结论: 密钥派生拦截方案 【部分可行】"
    echo "   ✅ 基础技术可行，但需要进一步优化"
    echo "   ❌ 存在一些技术障碍需要解决"
    echo "   📋 建议：针对特定问题进行针对性改进"
else
    echo "❌ 结论: 密钥派生拦截方案 【可行性低】"
    echo "   ❌ 存在重大技术障碍"
    echo "   📋 建议：重新评估技术路线或寻找替代方案"
fi

echo ""
echo "📖 建议和后续工作:"
echo "────────────────────────────────────────────────────────────────"

if [[ $ATTACHED_PROBES -eq 0 ]]; then
    echo "🔧 1. 检查目标函数名称是否正确"
    echo "     - 使用 'make -f Makefile.key_derivation analyze-ssl' 分析"
    echo "     - 考虑不同OpenSSL版本的函数名差异"
fi

if [[ $INTERCEPTED_CALLS -eq 0 ]]; then
    echo "🔧 2. 优化函数拦截策略"
    echo "     - 尝试拦截更底层的密钥派生函数"
    echo "     - 添加SSL握手阶段的函数拦截"
fi

if [[ $KEY_EVENTS -eq 0 ]]; then
    echo "🔧 3. 改进密钥数据提取逻辑"
    echo "     - 分析函数参数结构"
    echo "     - 优化内存读取方法"
fi

echo "🔧 4. 扩展支持范围"
echo "     - 支持更多SSL库版本"
echo "     - 支持BoringSSL、LibreSSL等其他SSL实现"
echo "     - 支持Java JSSE、Go crypto等高级语言SSL库"

echo ""
echo "🔚 可行性测试完成"
echo "📁 测试结果已保存到: $TEST_LOG_DIR"
echo "════════════════════════════════════════════════════════════════" 