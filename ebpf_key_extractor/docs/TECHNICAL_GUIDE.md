# eBPF通用SSL密钥提取器 - 技术指引

## 🎯 项目目标

构建基于eBPF的通用SSL密钥提取解决方案，支持所有主流SSL/TLS实现，包括：
- OpenSSL/BoringSSL (C/C++)
- Java JSSE (Java)
- Go crypto/tls (Go)
- Node.js crypto (JavaScript)
- .NET Core SSL (C#)

## 🔬 技术原理

### 核心策略：密钥派生函数拦截
不依赖具体SSL库实现，而是拦截所有SSL库都必须使用的底层密钥派生函数：

```
任意应用 -> SSL库 -> 密钥派生函数 -> eBPF拦截 -> 密钥提取
```

### 通用密钥派生函数
```c
// TLS 1.3 密钥派生
HKDF-Extract(salt, IKM) -> PRK
HKDF-Expand(PRK, info, L) -> OKM

// TLS 1.2 密钥派生  
PRF(secret, label, seed) -> output
P_hash(secret, seed) -> output

// 共同的底层函数
HMAC(key, data) -> hash
SHA256/SHA384/SHA512(data) -> hash
```

## 📋 实现架构

### 1. eBPF程序架构
```
┌─────────────────────────────────────────────────────────────┐
│                     eBPF Kernel Space                      │
├─────────────────────────────────────────────────────────────┤
│  uprobe_hkdf_extract()  │  uprobe_prf()  │  uprobe_hmac()  │
│  uprobe_hkdf_expand()   │  uprobe_p_hash() │               │
├─────────────────────────────────────────────────────────────┤
│                    BPF Maps                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐    │
│  │ key_events  │ │ ssl_sessions│ │ function_addresses  │    │
│  └─────────────┘ └─────────────┘ └─────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                   User Space Program                       │
├─────────────────────────────────────────────────────────────┤
│ 1. 自动发现SSL库和函数地址                                   │
│ 2. 动态安装uprobe                                          │
│ 3. 处理密钥事件                                            │
│ 4. 生成标准keylog格式                                       │
│ 5. 输出到文件或网络                                         │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据结构设计
```c
// 通用密钥事件
struct key_event {
    __u64 timestamp;
    __u32 pid;
    __u32 tid;
    __u8 function_type;     // HKDF=1, PRF=2, HMAC=3
    __u8 tls_version;       // 12=TLS1.2, 13=TLS1.3
    __u8 key_type;          // MASTER=1, TRAFFIC=2, FINISHED=3
    __u8 data_len;
    __u8 key_data[256];     // 密钥数据
    __u8 context_data[128]; // 上下文数据(randoms等)
};

// SSL会话状态
struct ssl_session {
    __u64 session_id;
    __u8 client_random[32];
    __u8 server_random[32];
    __u8 master_secret[48];
    __u16 cipher_suite;
    __u8 tls_version;
    __u8 session_state;     // HANDSHAKE=1, ESTABLISHED=2
};
```

### 3. 函数自动发现机制
```python
class SSLFunctionDiscovery:
    def __init__(self):
        self.ssl_libraries = [
            "/usr/lib/x86_64-linux-gnu/libssl.so*",
            "/usr/lib/x86_64-linux-gnu/libcrypto.so*", 
            "/usr/lib/jvm/*/lib/*/libsunec.so",
            "/usr/bin/node",
            "*.so"  # 动态库扫描
        ]
        
    def discover_functions(self, pid):
        """发现进程中的密钥派生函数"""
        functions = {}
        
        # 1. 读取进程内存映射
        maps = self.read_proc_maps(pid)
        
        # 2. 扫描每个库的符号表
        for lib in maps:
            symbols = self.extract_symbols(lib.path)
            for symbol in symbols:
                if self.is_key_derivation_function(symbol):
                    functions[symbol.name] = lib.base + symbol.offset
                    
        return functions
        
    def is_key_derivation_function(self, symbol):
        """识别密钥派生函数"""
        patterns = [
            r"HKDF_(Extract|Expand)",
            r"PRF|P_hash",
            r"HMAC_(Init|Update|Final)",
            r"tls13?_.*_key",
            r".*master.*secret",
            r".*derive.*key"
        ]
        return any(re.search(pattern, symbol.name, re.I) for pattern in patterns)
```

## 🛠️ 核心eBPF程序

### 1. HKDF函数拦截
```c
// src/hkdf_probe.bpf.c
SEC("uprobe")
int uprobe_hkdf_extract(struct pt_regs *ctx)
{
    struct key_event event = {};
    
    // 获取函数参数
    void *salt = (void *)PT_REGS_PARM1(ctx);
    size_t salt_len = PT_REGS_PARM2(ctx);
    void *ikm = (void *)PT_REGS_PARM3(ctx);  // Input Key Material
    size_t ikm_len = PT_REGS_PARM4(ctx);
    
    // 填充事件信息
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.tid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
    event.function_type = FUNC_HKDF_EXTRACT;
    event.tls_version = 13; // TLS 1.3
    event.key_type = KEY_TYPE_PRK;
    
    // 读取密钥材料 (安全检查)
    if (ikm_len > 0 && ikm_len <= sizeof(event.key_data)) {
        bpf_probe_read_user(event.key_data, ikm_len, ikm);
        event.data_len = ikm_len;
    }
    
    // 发送到用户态
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    
    return 0;
}

SEC("uretprobe")
int uretprobe_hkdf_extract(struct pt_regs *ctx)
{
    // 获取返回值(PRK - Pseudo-Random Key)
    void *prk = (void *)PT_REGS_RC(ctx);
    
    struct key_event event = {};
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = FUNC_HKDF_EXTRACT;
    event.key_type = KEY_TYPE_PRK;
    
    // 读取派生的密钥
    if (prk) {
        bpf_probe_read_user(event.key_data, 32, prk); // SHA256输出长度
        event.data_len = 32;
    }
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    return 0;
}
```

### 2. PRF函数拦截 (TLS 1.2)
```c
SEC("uprobe")
int uprobe_prf(struct pt_regs *ctx)
{
    // PRF(secret, label, seed) -> output
    void *secret = (void *)PT_REGS_PARM1(ctx);
    size_t secret_len = PT_REGS_PARM2(ctx);
    char *label = (char *)PT_REGS_PARM3(ctx);
    void *seed = (void *)PT_REGS_PARM4(ctx);
    size_t seed_len = PT_REGS_PARM5(ctx);
    
    struct key_event event = {};
    event.timestamp = bpf_ktime_get_ns();
    event.pid = bpf_get_current_pid_tgid() >> 32;
    event.function_type = FUNC_PRF;
    event.tls_version = 12; // TLS 1.2
    
    // 检查是否是master secret生成
    char master_label[16] = {};
    if (label) {
        bpf_probe_read_user(master_label, sizeof(master_label), label);
        if (bpf_strncmp(master_label, "master secret", 13) == 0) {
            event.key_type = KEY_TYPE_MASTER;
        }
    }
    
    // 提取secret
    if (secret_len > 0 && secret_len <= sizeof(event.key_data)) {
        bpf_probe_read_user(event.key_data, secret_len, secret);
        event.data_len = secret_len;
    }
    
    bpf_perf_event_output(ctx, &key_events, BPF_F_CURRENT_CPU, 
                         &event, sizeof(event));
    return 0;
}
```

## 🚀 用户态程序

### 1. 主程序框架
```c
// src/keylog_extractor.c
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

struct keylog_extractor {
    struct bpf_object *obj;
    struct bpf_program *prog_hkdf_extract;
    struct bpf_program *prog_prf;
    struct bpf_link **links;
    int link_count;
    FILE *keylog_file;
};

int main(int argc, char **argv)
{
    struct keylog_extractor extractor = {};
    
    // 1. 初始化libbpf
    libbpf_set_strict_mode(LIBBPF_STRICT_ALL);
    
    // 2. 加载eBPF程序
    extractor.obj = bpf_object__open_file("keylog_extractor.bpf.o", NULL);
    if (libbpf_get_error(extractor.obj)) {
        fprintf(stderr, "Failed to open BPF object\n");
        return 1;
    }
    
    if (bpf_object__load(extractor.obj)) {
        fprintf(stderr, "Failed to load BPF object\n");
        return 1;
    }
    
    // 3. 自动发现SSL函数并安装uprobe
    discover_and_attach_probes(&extractor);
    
    // 4. 启动事件处理循环
    process_key_events(&extractor);
    
    // 5. 清理资源
    cleanup(&extractor);
    
    return 0;
}
```

### 2. SSL函数自动发现
```c
int discover_and_attach_probes(struct keylog_extractor *extractor)
{
    DIR *proc_dir = opendir("/proc");
    struct dirent *entry;
    
    // 扫描所有进程
    while ((entry = readdir(proc_dir)) != NULL) {
        if (!isdigit(entry->d_name[0]))
            continue;
            
        int pid = atoi(entry->d_name);
        
        // 检查进程是否使用SSL
        if (!process_uses_ssl(pid))
            continue;
            
        printf("Found SSL process: %d\n", pid);
        
        // 发现SSL函数
        struct function_list *functions = discover_ssl_functions(pid);
        
        // 为每个函数安装uprobe
        for (int i = 0; i < functions->count; i++) {
            struct ssl_function *func = &functions->functions[i];
            
            if (attach_uprobe(extractor, func->name, func->address, pid) < 0) {
                fprintf(stderr, "Failed to attach uprobe to %s\n", func->name);
            } else {
                printf("Attached uprobe to %s at 0x%lx\n", func->name, func->address);
            }
        }
        
        free(functions);
    }
    
    closedir(proc_dir);
    return 0;
}
```

## 📊 验证测试计划

### 1. 基础功能验证
```bash
# 测试1: OpenSSL应用
curl -k https://httpbin.org/get
# 期望: 捕获HKDF/PRF函数调用，提取master secret

# 测试2: Java应用  
java -jar spring-boot-app.jar
# 期望: 捕获JSSE密钥派生函数

# 测试3: Node.js应用
node https-server.js
# 期望: 捕获Node.js crypto模块函数
```

### 2. 性能基准测试
```bash
# CPU使用率
top -p `pgrep keylog_extractor`

# 内存使用
cat /proc/`pgrep keylog_extractor`/status | grep Vm

# 网络延迟影响
ab -n 1000 -c 10 https://test-server/
```

### 3. 兼容性测试
- [ ] CentOS 7/8
- [ ] Ubuntu 18.04/20.04/22.04  
- [ ] 内核版本: 4.15+ (eBPF支持)
- [ ] OpenSSL 1.1.1/3.0
- [ ] Java 8/11/17
- [ ] Node.js 14/16/18

## 🎯 成功标准

1. **功能性**: 能够从至少3种不同SSL实现中提取密钥
2. **性能**: CPU开销 < 5%, 内存开销 < 100MB
3. **兼容性**: 支持主流Linux发行版和内核版本
4. **稳定性**: 24小时连续运行无崩溃
5. **易用性**: 一键部署，自动检测和配置

## 🔧 开发里程碑

### Phase 1: 概念验证 (本次实现)
- [x] 项目框架搭建
- [ ] 基础eBPF程序实现
- [ ] OpenSSL函数拦截验证
- [ ] 密钥提取和keylog生成

### Phase 2: 多库支持
- [ ] Java JSSE支持
- [ ] Go crypto/tls支持  
- [ ] Node.js支持
- [ ] 自动函数发现机制

### Phase 3: 企业级功能
- [ ] 性能优化
- [ ] 错误处理和恢复
- [ ] 配置管理
- [ ] 监控和日志

这个技术指引为我们提供了清晰的实现路径和验证标准！ 