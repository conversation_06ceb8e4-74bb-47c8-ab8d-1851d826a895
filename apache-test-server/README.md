# Apache HTTPS 测试服务器

## 📋 项目简介

基于 **Apache HTTP Server + mod_ssl** 的HTTPS测试服务器，专门用于验证SSL密钥提取器对Apache的支持情况。

## 🚀 快速启动

### 1. 启动服务器
```bash
./start_apache_https.sh
```

### 2. 访问测试
```bash
# 主页
curl -k https://localhost:8443/

# API测试
curl -k https://localhost:8443/api/test
curl -k https://localhost:8443/api/status
```

### 3. 停止服务器
```bash
./stop_apache_https.sh
```

## 🔧 技术规格

### 服务器配置
- **Web服务器**: Apache HTTP Server 2.4
- **SSL模块**: mod_ssl (基于OpenSSL)
- **端口**: 8443 (避免与其他服务冲突)
- **证书**: 自签名证书
- **PHP支持**: 通过php-cgi处理API请求

### SSL配置
- **协议**: TLS 1.2, TLS 1.3
- **密码套件**: ECDHE-ECDSA-AES128-GCM-SHA256等现代密码套件
- **证书位置**: `/root/ebpf/apache-test-server/ssl/`

## 🌐 API接口

### 可用接口
- `GET /api/test` - 基础测试接口
- `GET /api/status` - 系统状态接口  
- `GET /api/users` - 用户管理接口
- `GET /api/data` - 大数据接口 (>2KB)
- `GET /api/config` - 配置管理接口

### 示例响应
```json
{
    "status": "success",
    "message": "Apache HTTPS 测试服务器 - API 测试成功",
    "server_info": {
        "software": "Apache HTTP Server",
        "ssl_module": "mod_ssl",
        "port": 8443,
        "protocol": "HTTPS"
    }
}
```

## 📂 目录结构

```
apache-test-server/
├── conf/
│   └── httpd.conf              # Apache配置文件
├── html/
│   ├── index.html              # 主页
│   └── api.php                 # API处理脚本
├── ssl/
│   ├── apache-server.crt       # SSL证书
│   ├── apache-server.key       # SSL私钥
│   └── apache-server.csr       # 证书签名请求
├── logs/                       # 日志目录
├── generate_ssl_cert.sh        # SSL证书生成脚本
├── start_apache_https.sh       # 启动脚本
├── stop_apache_https.sh        # 停止脚本
└── README.md                   # 本文档
```

## 🧪 SSL密钥提取测试

### 验证目的
验证SSL密钥提取器是否能从Apache HTTP Server (mod_ssl)中提取SSL密钥。

### 测试步骤
1. 启动Apache HTTPS服务器
2. 确认SSL密钥提取器全局注入已启用
3. 从外部机器访问Apache服务器
4. 检查`/tmp/global_ssl_keylog.txt`是否有新增SSL密钥记录

### 期望结果
- ✅ Apache使用mod_ssl (基于OpenSSL)
- ✅ 应该能成功提取SSL密钥

## 📝 日志文件

- **访问日志**: `/root/ebpf/apache-test-server/logs/access.log`
- **错误日志**: `/root/ebpf/apache-test-server/logs/error.log`

## ⚠️ 注意事项

1. **端口冲突**: 使用8443端口避免与其他HTTPS服务冲突
2. **自签名证书**: 浏览器会显示安全警告，选择继续访问即可
3. **权限要求**: 需要root权限启动服务器
4. **防火墙**: 确保防火墙允许8443端口访问

## 🔍 故障排除

### 启动失败
```bash
# 检查配置文件语法
httpd -f /root/ebpf/apache-test-server/conf/httpd.conf -t

# 查看错误日志
tail -f /root/ebpf/apache-test-server/logs/error.log
```

### 端口被占用
```bash
# 查看占用进程
netstat -tlnp | grep 8443

# 强制停止
./stop_apache_https.sh
``` 