<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apache HTTPS 测试服务</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #d73527; border-bottom: 3px solid #d73527; padding-bottom: 10px; }
        h2 { color: #333; margin-top: 30px; }
        .success { color: #27ae60; font-size: 18px; font-weight: bold; }
        .info-list { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .info-list li { margin: 8px 0; }
        .api-link { display: inline-block; margin: 5px 10px 5px 0; padding: 8px 15px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .api-link:hover { background: #2980b9; }
        .timestamp { color: #7f8c8d; font-size: 14px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Apache HTTPS 测试服务</h1>
        <p class="success">✅ HTTPS 连接成功！</p>
        <p>您已经成功通过 HTTPS 协议访问本 Apache 服务器。</p>
        
        <h2>服务器信息：</h2>
        <ul class="info-list">
            <li><strong>Web服务器：</strong>Apache HTTP Server</li>
            <li><strong>协议：</strong>HTTPS (TLS/SSL)</li>
            <li><strong>端口：</strong>2443</li>
            <li><strong>SSL实现：</strong>mod_ssl (基于OpenSSL)</li>
            <li><strong>证书：</strong>自签名证书</li>
            <li><strong>服务器地址：</strong>localhost:2443</li>
            <li><strong>访问时间：</strong><span id="currentTime"></span></li>
        </ul>
        
        <h2>API测试：</h2>
        <p>以下是可用的测试API接口：</p>
        <div>
            <a href="/api/test" class="api-link">基础测试API</a>
            <a href="/api/status" class="api-link">系统状态API</a>
            <a href="/api/users" class="api-link">用户管理API</a>
            <a href="/api/data" class="api-link">大数据API</a>
            <a href="/api/config" class="api-link">配置管理API</a>
        </div>
        
        <div class="timestamp">
            页面生成时间: <span id="pageTime"></span>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
            document.getElementById('pageTime').textContent = now.toLocaleString('zh-CN');
        }
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html> 