#!/bin/bash

# 设置HTTP头
echo "Content-Type: application/json"
echo "Access-Control-Allow-Origin: *"
echo ""

# 获取请求参数
ENDPOINT="${QUERY_STRING#endpoint=}"
ENDPOINT="${ENDPOINT%%&*}"

# 当前时间戳
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

case "$ENDPOINT" in
    "test")
        cat << EOF
{
    "status": "success",
    "message": "Apache HTTPS 测试服务器 - API 测试成功",
    "method": "${REQUEST_METHOD:-GET}",
    "server_info": {
        "software": "Apache HTTP Server",
        "ssl_module": "mod_ssl",
        "port": 2443,
        "protocol": "HTTPS"
    },
    "timestamp": "$TIMESTAMP"
}
EOF
        ;;
    "status")
        MEMORY_TOTAL=$(free -b | awk 'NR==2{print $2}')
        MEMORY_USED=$(free -b | awk 'NR==2{print $3}')
        LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
        
        cat << EOF
{
    "status": "success",
    "server_status": "running",
    "system_info": {
        "server_software": "Apache HTTP Server",
        "ssl_enabled": "yes",
        "request_scheme": "https"
    },
    "memory_usage": {
        "total": $MEMORY_TOTAL,
        "used": $MEMORY_USED
    },
    "load_average": "$LOAD_AVG",
    "timestamp": "$TIMESTAMP"
}
EOF
        ;;
    "users")
        cat << EOF
{
    "status": "success",
    "users": [
        {"id": 1, "name": "Apache User", "role": "admin"},
        {"id": 2, "name": "Test User", "role": "user"}
    ],
    "total": 2,
    "timestamp": "$TIMESTAMP"
}
EOF
        ;;
    "data")
        # 生成大于2KB的数据
        LARGE_DATA=$(printf "Apache HTTPS测试数据 - %.0s" {1..100})
        DATA_SIZE=${#LARGE_DATA}
        
        cat << EOF
{
    "status": "success",
    "data_size": $DATA_SIZE,
    "large_data": "$LARGE_DATA",
    "server": "Apache HTTP Server with mod_ssl",
    "timestamp": "$TIMESTAMP"
}
EOF
        ;;
    "config")
        cat << EOF
{
    "status": "success",
    "config": {
        "server_name": "${SERVER_NAME:-localhost}",
        "server_port": "${SERVER_PORT:-2443}",
        "document_root": "${DOCUMENT_ROOT:-/root/ebpf/apache-test-server/html}",
        "ssl_enabled": true,
        "modules": ["mod_ssl", "mod_rewrite", "mod_cgi"]
    },
    "timestamp": "$TIMESTAMP"
}
EOF
        ;;
    *)
        echo "Status: 404 Not Found"
        cat << EOF
{
    "status": "error",
    "message": "API endpoint not found: $ENDPOINT",
    "available_endpoints": ["test", "status", "users", "data", "config"],
    "timestamp": "$TIMESTAMP"
}
EOF
        ;;
esac 