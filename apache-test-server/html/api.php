<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$endpoint = $_GET['endpoint'] ?? 'unknown';
$method = $_SERVER['REQUEST_METHOD'];

$response = array();

switch($endpoint) {
    case 'test':
        $response = array(
            'status' => 'success',
            'message' => 'Apache HTTPS 测试服务器 - API 测试成功',
            'method' => $method,
            'server_info' => array(
                'software' => 'Apache HTTP Server',
                'ssl_module' => 'mod_ssl',
                'port' => 8443,
                'protocol' => 'HTTPS'
            ),
            'timestamp' => date('Y-m-d H:i:s')
        );
        break;
        
    case 'status':
        $response = array(
            'status' => 'success',
            'server_status' => 'running',
            'system_info' => array(
                'php_version' => phpversion(),
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Apache',
                'ssl_enabled' => isset($_SERVER['HTTPS']) ? 'yes' : 'no',
                'request_scheme' => $_SERVER['REQUEST_SCHEME'] ?? 'https'
            ),
            'memory_usage' => array(
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true)
            ),
            'timestamp' => date('Y-m-d H:i:s')
        );
        break;
        
    case 'users':
        $response = array(
            'status' => 'success',
            'users' => array(
                array('id' => 1, 'name' => 'Apache User', 'role' => 'admin'),
                array('id' => 2, 'name' => 'Test User', 'role' => 'user')
            ),
            'total' => 2,
            'timestamp' => date('Y-m-d H:i:s')
        );
        break;
        
    case 'data':
        // 生成大于2KB的数据
        $large_data = str_repeat('Apache HTTPS测试数据 - ', 100);
        $response = array(
            'status' => 'success',
            'data_size' => strlen($large_data),
            'large_data' => $large_data,
            'server' => 'Apache HTTP Server with mod_ssl',
            'timestamp' => date('Y-m-d H:i:s')
        );
        break;
        
    case 'config':
        $response = array(
            'status' => 'success',
            'config' => array(
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'localhost',
                'server_port' => $_SERVER['SERVER_PORT'] ?? 8443,
                'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '/root/ebpf/apache-test-server/html',
                'ssl_enabled' => isset($_SERVER['HTTPS']),
                'modules' => array('mod_ssl', 'mod_rewrite', 'mod_mime')
            ),
            'timestamp' => date('Y-m-d H:i:s')
        );
        break;
        
    default:
        http_response_code(404);
        $response = array(
            'status' => 'error',
            'message' => 'API endpoint not found: ' . $endpoint,
            'available_endpoints' => array('test', 'status', 'users', 'data', 'config'),
            'timestamp' => date('Y-m-d H:i:s')
        );
        break;
}

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?> 