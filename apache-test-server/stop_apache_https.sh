#!/bin/bash

echo "======================================"
echo "  Apache HTTPS 测试服务停止脚本"
echo "======================================"

APACHE_DIR="/root/ebpf/apache-test-server"
PID_FILE="$APACHE_DIR/logs/httpd.pid"

echo "[INFO] 停止Apache HTTPS服务器..."

# 尝试通过PID文件停止
if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null; then
        echo "[INFO] 通过PID停止Apache进程 ($PID)..."
        kill $PID
        sleep 3
        
        # 检查是否成功停止
        if ps -p $PID > /dev/null; then
            echo "[WARNING] 强制杀死Apache进程..."
            kill -9 $PID
        fi
    fi
    rm -f "$PID_FILE"
fi

# 强制杀死所有相关进程
echo "[INFO] 清理残留的Apache进程..."
pkill -f "httpd.*2443" 2>/dev/null || true
pkill -f "apache.*2443" 2>/dev/null || true

# 检查端口是否释放
sleep 2
if netstat -tlnp | grep -q ":2443 "; then
    echo "[WARNING] 端口2443仍被占用"
    netstat -tlnp | grep ":2443"
else
    echo "[INFO] ✅ Apache HTTPS服务器已成功停止"
    echo "[INFO] 端口2443已释放"
fi

echo "======================================" 