#!/bin/bash

echo "======================================"
echo "  Apache HTTPS 测试服务启动脚本"
echo "======================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "[ERROR] 请使用root用户运行此脚本"
    exit 1
fi

APACHE_DIR="/root/ebpf/apache-test-server"
CONFIG_FILE="$APACHE_DIR/conf/httpd.conf"
PID_FILE="$APACHE_DIR/logs/httpd.pid"

# 创建必要的目录
mkdir -p "$APACHE_DIR/logs"

# 检查配置文件
echo "[INFO] 检查Apache配置文件..."
if [ ! -f "$CONFIG_FILE" ]; then
    echo "[ERROR] 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

# 验证配置文件语法
echo "[INFO] 验证Apache配置文件语法..."
httpd -f "$CONFIG_FILE" -t
if [ $? -ne 0 ]; then
    echo "[ERROR] Apache配置文件语法错误"
    exit 1
fi

# 检查端口是否被占用
echo "[INFO] 检查端口2443是否可用..."
if netstat -tlnp | grep -q ":2443 "; then
    echo "[WARNING] 端口2443已被占用，尝试停止现有Apache进程..."
    if [ -f "$PID_FILE" ]; then
        kill $(cat "$PID_FILE") 2>/dev/null
        sleep 2
    fi
    pkill -f "httpd.*2443" 2>/dev/null
    sleep 2
fi

# 设置目录权限
echo "[INFO] 设置目录权限..."
chown -R apache:apache "$APACHE_DIR/html" "$APACHE_DIR/logs" 2>/dev/null || true
chmod 755 "$APACHE_DIR/html"
chmod 755 "$APACHE_DIR/html/"*.php 2>/dev/null || true

# 启动Apache
echo "[INFO] 启动Apache HTTPS服务器..."
httpd -f "$CONFIG_FILE" -D FOREGROUND &
APACHE_PID=$!

# 等待服务启动
sleep 3

# 检查服务是否启动成功
if ps -p $APACHE_PID > /dev/null; then
    echo $APACHE_PID > "$PID_FILE"
    echo "[INFO] ✅ Apache HTTPS服务器启动成功！"
    echo ""
    echo "🌐 服务信息:"
    echo "   • 服务器: Apache HTTP Server"
    echo "   • 端口: 2443 (HTTPS)"
    echo "   • SSL: mod_ssl (基于OpenSSL)"
    echo "   • 进程ID: $APACHE_PID"
    echo ""
    echo "🔗 访问地址:"
    echo "   • 主页: https://localhost:2443/"
    echo "   • API测试: https://localhost:2443/api/test"
    echo "   • 系统状态: https://localhost:2443/api/status"
    echo ""
    echo "📝 日志文件:"
    echo "   • 访问日志: $APACHE_DIR/logs/access.log"
    echo "   • 错误日志: $APACHE_DIR/logs/error.log"
    echo ""
    echo "⚠️  注意: 使用自签名证书，浏览器会显示安全警告"
else
    echo "[ERROR] ❌ Apache启动失败"
    echo "请检查错误日志: $APACHE_DIR/logs/error.log"
    exit 1
fi 