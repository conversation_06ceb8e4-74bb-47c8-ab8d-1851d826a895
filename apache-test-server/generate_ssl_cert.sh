#!/bin/bash

echo "======================================"
echo "  Apache HTTPS SSL证书生成脚本"
echo "======================================"

CERT_DIR="/root/ebpf/apache-test-server/ssl"
mkdir -p $CERT_DIR

# 生成私钥
echo "[INFO] 生成私钥..."
openssl genrsa -out $CERT_DIR/apache-server.key 2048

# 生成证书签名请求
echo "[INFO] 生成证书签名请求..."
openssl req -new -key $CERT_DIR/apache-server.key -out $CERT_DIR/apache-server.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=Test/OU=IT/CN=localhost"

# 生成自签名证书
echo "[INFO] 生成自签名证书..."
openssl x509 -req -days 365 -in $CERT_DIR/apache-server.csr -signkey $CERT_DIR/apache-server.key -out $CERT_DIR/apache-server.crt

# 设置文件权限
chmod 600 $CERT_DIR/apache-server.key
chmod 644 $CERT_DIR/apache-server.crt

echo "[INFO] ✅ SSL证书生成完成！"
echo "   • 私钥: $CERT_DIR/apache-server.key"
echo "   • 证书: $CERT_DIR/apache-server.crt"

# 显示证书信息
echo ""
echo "[INFO] 证书信息:"
openssl x509 -in $CERT_DIR/apache-server.crt -text -noout | grep -E "(Subject:|Not After)" 