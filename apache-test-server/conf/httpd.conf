# Apache HTTPS 测试服务器配置
# 端口: 2443 (避免与其他服务冲突)

ServerRoot "/root/ebpf/apache-test-server"
PidFile "/root/ebpf/apache-test-server/logs/httpd.pid"

# 基础设置
Listen 2443 ssl
ServerName localhost:2443
DocumentRoot "/root/ebpf/apache-test-server/html"

# 模块加载
LoadModule mpm_prefork_module /usr/lib64/httpd/modules/mod_mpm_prefork.so
LoadModule authz_core_module /usr/lib64/httpd/modules/mod_authz_core.so
LoadModule dir_module /usr/lib64/httpd/modules/mod_dir.so
LoadModule mime_module /usr/lib64/httpd/modules/mod_mime.so
LoadModule ssl_module /usr/lib64/httpd/modules/mod_ssl.so
LoadModule log_config_module /usr/lib64/httpd/modules/mod_log_config.so
LoadModule unixd_module /usr/lib64/httpd/modules/mod_unixd.so
LoadModule rewrite_module /usr/lib64/httpd/modules/mod_rewrite.so
LoadModule cgi_module /usr/lib64/httpd/modules/mod_cgi.so

# 用户和组
User apache
Group apache

# 日志配置
ErrorLog "/root/ebpf/apache-test-server/logs/error.log"
LogLevel warn
LogFormat "%h %l %u %t \"%r\" %>s %b \"%{Referer}i\" \"%{User-Agent}i\"" combined
CustomLog "/root/ebpf/apache-test-server/logs/access.log" combined

# 目录权限
<Directory />
    AllowOverride none
    Require all denied
</Directory>

<Directory "/root/ebpf/apache-test-server/html">
    AllowOverride None
    Require all granted
</Directory>

# MIME类型
TypesConfig /etc/mime.types
DirectoryIndex index.html

# CGI处理 - Shell脚本API
AddHandler cgi-script .sh
<Files "*.sh">
    Options +ExecCGI
</Files>

# SSL配置
SSLEngine on
SSLProtocol all -SSLv3 -TLSv1 -TLSv1.1
SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
SSLHonorCipherOrder off
SSLSessionTickets off

# SSL证书
SSLCertificateFile "/root/ebpf/apache-test-server/ssl/apache-server.crt"
SSLCertificateKeyFile "/root/ebpf/apache-test-server/ssl/apache-server.key"

# URL重写规则 - API路由
RewriteEngine On
RewriteRule ^/api/(.*)$ /api.sh?endpoint=$1 [QSA,L] 